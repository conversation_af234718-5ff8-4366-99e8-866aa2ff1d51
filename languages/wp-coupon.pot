#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-02-13 03:12+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/"

#: 404.php:30
msgid ""
"It looks like nothing was found at this location. Maybe try one of the links "
"below or a search?"
msgstr ""

#: 404.php:35 inc/widgets/popular-stores.php:113
msgid "Popular Stores"
msgstr ""

#: 404.php:42
msgid "Recent coupons"
msgstr ""

#. Name of the template
#. Name of the template
msgid "Store Listing by Alphabet"
msgstr ""

#: archive-store.php:68 templates/store-az.php:86
msgid "0 - 9"
msgstr ""

#: archive-store.php:83 templates/store-az.php:100
msgid "Featured Stores"
msgstr ""

#: archive-store.php:108 templates/store-az.php:125
#, php-format
msgid "Stores - %s"
msgstr ""

#: archive-store.php:123 templates/store-az.php:140
msgid "Stores - 0-9"
msgstr ""

#: comments.php:25
#, php-format
msgctxt "comments title"
msgid "One thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#: comments.php:48
msgid "Comment navigation"
msgstr ""

#: comments.php:51
msgid "Older Comments"
msgstr ""

#: comments.php:55
msgid "Newer Comments"
msgstr ""

#: comments.php:72 loop/coupon-meta.php:58
msgid "Comments are closed."
msgstr ""

#: comments.php:92
#, php-format
msgid "Required fields are marked %s"
msgstr ""

#: comments.php:99 inc/widgets/categories.php:157
msgid "Name"
msgstr ""

#: comments.php:104 single-coupon.php:132 loop/coupon-meta.php:12
#: inc/config/option-config.php:1274
msgid "Email"
msgstr ""

#: comments.php:108
msgid "Website"
msgstr ""

#: comments.php:112
msgctxt "noun"
msgid "Comment"
msgstr ""

#: comments.php:118
#, php-format
msgid "You must be %s to post a comment."
msgstr ""

#: comments.php:119
msgid "logged in"
msgstr ""

#: comments.php:125
#, php-format
msgid "Logged in as %1$s. %2$s"
msgstr ""

#: comments.php:127
msgid "Log out of this account"
msgstr ""

#: comments.php:129
msgid "Your email address will not be published."
msgstr ""

#: comments.php:142 loop/coupon-meta.php:60
msgid "Let other know how much you saved"
msgstr ""

#: content-none.php:2
msgid "Nothing Found"
msgstr ""

#: content-none.php:7
#, php-format
msgid "Ready to publish your first post? %s."
msgstr ""

#: content-none.php:10
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""

#: content-none.php:15
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#: coupon-comments.php:32 inc/template-tags.php:220
msgid "Reply"
msgstr ""

#: coupon-comments.php:40 taxonomy-coupon_category.php:131
#: taxonomy-coupon_store.php:128 taxonomy-coupon_store.php:166
#: taxonomy-coupon_store.php:204 inc/widgets/coupons.php:134
#: inc/widgets/coupons.php:179 inc/widgets/coupons.php:225
msgid "Loading..."
msgstr ""

#: coupon-comments.php:40
msgid "Load More Comments"
msgstr ""

#: coupon-comments.php:49
msgid "No comment, be the first."
msgstr ""

#: footer.php:63
#, php-format
msgid "Copyright &copy; %1$s %2$s. All Rights Reserved. "
msgstr ""

#: footer.php:70
#, php-format
msgid "WordPress Coupon Theme by %s"
msgstr ""

#: functions.php:78 inc/config/option-config.php:317
msgid "Primary"
msgstr ""

#: functions.php:79 inc/config/option-config.php:1120
msgid "Footer"
msgstr ""

#: functions.php:104
msgid "Blog Sidebar"
msgstr ""

#: functions.php:113
msgid "Page Sidebar"
msgstr ""

#: functions.php:123
msgid "Coupon Category Sidebar"
msgstr ""

#: functions.php:125
msgid "The sidebar will display on coupon category, tag page."
msgstr ""

#: functions.php:133
msgid "Store Sidebar"
msgstr ""

#: functions.php:143
msgid "WooCommerce Sidebar"
msgstr ""

#: functions.php:153
msgid "Footer 1"
msgstr ""

#: functions.php:162
msgid "Footer 2"
msgstr ""

#: functions.php:171
msgid "Footer 3"
msgstr ""

#: functions.php:180
msgid "Footer 4"
msgstr ""

#: functions.php:191
msgid "Frontpage Before Main Content"
msgstr ""

#: functions.php:193 functions.php:203 functions.php:213 functions.php:222
msgid "This sidebar display on frontpage template"
msgstr ""

#: functions.php:201
msgid "Frontpage Main Content"
msgstr ""

#: functions.php:211
msgid "Frontpage Main Sidebar"
msgstr ""

#: functions.php:220
msgid "Frontpage After Main Content"
msgstr ""

#: functions.php:260
msgid "Favorited"
msgstr ""

#: functions.php:261 taxonomy-coupon_store.php:43
msgid "Favorite This Store"
msgstr ""

#: functions.php:262
msgid "Please login to continue..."
msgstr ""

#: functions.php:263 single-coupon.php:110 loop/coupon-modal.php:62
#: loop/loop-coupon-cat.php:108 loop/loop-coupon.php:103
msgid "Save this coupon"
msgstr ""

#: functions.php:264
msgid "Coupon Saved"
msgstr ""

#: functions.php:265
msgid "No results..."
msgstr ""

#: functions.php:266
msgid "Copied"
msgstr ""

#: functions.php:267 loop/coupon-modal.php:49
msgid "Copy"
msgstr ""

#: header.php:63
msgid "Search stores for coupons, deals ..."
msgstr ""

#: header.php:65
msgid "Search"
msgstr ""

#: header.php:87
msgid "Top Searches:"
msgstr ""

#: header.php:105
msgid "Skip to content"
msgstr ""

#: single-coupon.php:68 loop/loop-coupon-cat.php:104 loop/loop-coupon.php:40
#: inc/user/user.php:255
#, php-format
msgid "Expires %s"
msgstr ""

#: single-coupon.php:81 loop/loop-coupon-cat.php:75 loop/loop-coupon.php:74
msgid "Get Deal"
msgstr ""

#: single-coupon.php:86 loop/loop-coupon-cat.php:80 loop/loop-coupon.php:79
msgid "Print Coupon"
msgstr ""

#: single-coupon.php:95 loop/loop-coupon-cat.php:89 loop/loop-coupon.php:88
msgid "Click to copy & open site"
msgstr ""

#: single-coupon.php:101 loop/loop-coupon-cat.php:95 loop/loop-coupon.php:94
msgid "Get Code"
msgstr ""

#: single-coupon.php:108 loop/coupon-modal.php:60 loop/loop-coupon.php:101
msgid "This worked"
msgstr ""

#: single-coupon.php:109 loop/coupon-modal.php:61 loop/loop-coupon.php:102
msgid "It didn't work"
msgstr ""

#: single-coupon.php:112 loop/loop-coupon.php:105
#, php-format
msgid "%s%% Success"
msgstr ""

#: single-coupon.php:130 loop/coupon-meta.php:10 loop/coupon-modal.php:90
#, php-format
msgid "%1$s Used - %2$s Today"
msgstr ""

#: single-coupon.php:131 loop/coupon-meta.php:11
msgid "Share it with your friend"
msgstr ""

#: single-coupon.php:131 loop/coupon-meta.php:11 loop/coupon-modal.php:91
msgid "Share"
msgstr ""

#: single-coupon.php:132 single-coupon.php:159 loop/coupon-meta.php:12
#: loop/coupon-meta.php:40
msgid "Send this coupon to an email"
msgstr ""

#: single-coupon.php:136 loop/coupon-meta.php:17
msgid "Share it with your friends"
msgstr ""

#: single-coupon.php:161 loop/coupon-meta.php:42
msgid "Email address ..."
msgstr ""

#: single-coupon.php:163 loop/coupon-meta.php:44
msgid "Send"
msgstr ""

#: single-coupon.php:165 loop/coupon-meta.php:46
msgid ""
"This is not a email subscription service. Your email (or your friend's email)"
" will only be used to send this coupon."
msgstr ""

#: single-coupon.php:185 inc/config/option-config.php:1014
msgid "Most popular {store} coupons."
msgstr ""

#: single-coupon.php:253
msgid "Tags:"
msgstr ""

#: single.php:56 single.php:57 loop/loop.php:31 loop/loop.php:32
#, php-format
msgid "Posts by %s"
msgstr ""

#: single.php:63 loop/loop.php:39
msgid "0 Comments"
msgstr ""

#: single.php:64 loop/loop.php:40
msgid "1 Comment"
msgstr ""

#: single.php:65 loop/loop.php:41
msgid "% Comments"
msgstr ""

#: taxonomy-coupon_category.php:54 inc/config/option-config.php:945
msgid "Newest %coupon_cate% Coupons"
msgstr ""

#: taxonomy-coupon_category.php:131 taxonomy-coupon_store.php:128
#: taxonomy-coupon_store.php:166 taxonomy-coupon_store.php:204
#: inc/widgets/coupons.php:134 inc/widgets/coupons.php:179
#: inc/widgets/coupons.php:225
msgid "Load More Coupons"
msgstr ""

#: taxonomy-coupon_store.php:37
msgid "Shop "
msgstr ""

#: taxonomy-coupon_store.php:220 inc/widgets/coupons.php:124
#: inc/widgets/coupons.php:163 inc/widgets/coupons.php:209
msgid "Oops! No coupons found"
msgstr ""

#: taxonomy-coupon_store.php:222
msgid "There is no coupons for this store, please comeback later."
msgstr ""

#: inc/extras.php:72
msgid ""
"This widget area is currently disabled. You can enable it Theme Options - "
"Footer section."
msgstr ""

#: inc/extras.php:182
msgid "Read more"
msgstr ""

#: inc/post-type.php:24
msgctxt "post type general name"
msgid "Coupons"
msgstr ""

#: inc/post-type.php:25
msgctxt "post type singular name"
msgid "Coupon"
msgstr ""

#: inc/post-type.php:26
msgctxt "admin menu"
msgid "Coupons"
msgstr ""

#: inc/post-type.php:27
msgctxt "add new on admin bar"
msgid "Coupon"
msgstr ""

#: inc/post-type.php:28
msgctxt "coupon"
msgid "Add New"
msgstr ""

#: inc/post-type.php:29
msgid "Add New Coupon"
msgstr ""

#: inc/post-type.php:30
msgid "New Coupon"
msgstr ""

#: inc/post-type.php:31
msgid "Edit Coupon"
msgstr ""

#: inc/post-type.php:32
msgid "View Coupon"
msgstr ""

#: inc/post-type.php:33
msgid "All Coupons"
msgstr ""

#: inc/post-type.php:34
msgid "Search Coupons"
msgstr ""

#: inc/post-type.php:35
msgid "Parent Coupons:"
msgstr ""

#: inc/post-type.php:36
msgid "No coupons found."
msgstr ""

#: inc/post-type.php:37
msgid "No coupons found in Trash."
msgstr ""

#: inc/post-type.php:76
msgctxt "taxonomy general name"
msgid "Coupon Categories"
msgstr ""

#: inc/post-type.php:77
msgctxt "taxonomy singular name"
msgid "Coupon Category"
msgstr ""

#: inc/post-type.php:78
msgid "Search Coupon Categories"
msgstr ""

#: inc/post-type.php:79
msgid "All Coupon Categories"
msgstr ""

#: inc/post-type.php:80
msgid "Parent Coupon Category"
msgstr ""

#: inc/post-type.php:81
msgid "Parent Coupon Category:"
msgstr ""

#: inc/post-type.php:82
msgid "Edit Coupon Category"
msgstr ""

#: inc/post-type.php:83
msgid "Update Category"
msgstr ""

#: inc/post-type.php:84
msgid "Add New Coupon Category"
msgstr ""

#: inc/post-type.php:85
msgid "New Coupon Category Name"
msgstr ""

#: inc/post-type.php:86
msgid "Categories"
msgstr ""

#: inc/post-type.php:113
msgctxt "taxonomy general name"
msgid "Coupon Stores"
msgstr ""

#: inc/post-type.php:114
msgctxt "taxonomy singular name"
msgid "Coupon Store"
msgstr ""

#: inc/post-type.php:115
msgid "Search Stores"
msgstr ""

#: inc/post-type.php:116
msgid "All Stores"
msgstr ""

#: inc/post-type.php:117
msgid "Parent Store"
msgstr ""

#: inc/post-type.php:118
msgid "Parent Store:"
msgstr ""

#: inc/post-type.php:119
msgid "Update Store"
msgstr ""

#: inc/post-type.php:120
msgid "Add New Store"
msgstr ""

#: inc/post-type.php:121
msgid "New Store"
msgstr ""

#: inc/post-type.php:122
msgid "Stores"
msgstr ""

#: inc/post-type.php:123
msgid "View Store"
msgstr ""

#: inc/post-type.php:124
msgid "Edit Store"
msgstr ""

#: inc/post-type.php:152
msgctxt "taxonomy general name"
msgid "Coupon Tags"
msgstr ""

#: inc/post-type.php:153
msgctxt "taxonomy singular name"
msgid "Coupon Tag"
msgstr ""

#: inc/post-type.php:154
msgid "Search Tag"
msgstr ""

#: inc/post-type.php:155
msgid "All Tags"
msgstr ""

#: inc/post-type.php:156
msgid "Parent Tags"
msgstr ""

#: inc/post-type.php:157
msgid "Parent Tag:"
msgstr ""

#: inc/post-type.php:158
msgid "Update Tag"
msgstr ""

#: inc/post-type.php:159
msgid "Add New Tag"
msgstr ""

#: inc/post-type.php:160
msgid "New Tag"
msgstr ""

#: inc/post-type.php:161
msgid "Tags"
msgstr ""

#: inc/post-type.php:162
msgid "View Tags"
msgstr ""

#: inc/post-type.php:163
msgid "Edit Tag"
msgstr ""

#: inc/post-type.php:200 inc/config/metabox-config.php:418
msgid "Icon"
msgstr ""

#: inc/post-type.php:224 inc/config/metabox-config.php:358
msgid "Thumbnail"
msgstr ""

#: inc/post-type.php:227 inc/config/option-config.php:516
#: inc/widgets/slider.php:78
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:161
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:219
msgid "URL"
msgstr ""

#: inc/post-type.php:228
msgid "Out"
msgstr ""

#: inc/post-type.php:259
msgid "URL:"
msgstr ""

#: inc/post-type.php:260 inc/post-type.php:264
msgid "[empty]"
msgstr ""

#: inc/post-type.php:263
msgid "Aff:"
msgstr ""

#: inc/post-type.php:297 inc/config/metabox-config.php:96
#: inc/core/coupon.php:706 inc/core/coupon.php:714 inc/core/coupon.php:1003
#: inc/core/coupon.php:1011
msgid "Code"
msgstr ""

#: inc/post-type.php:298 inc/config/metabox-config.php:97
#: inc/core/coupon.php:707 inc/core/coupon.php:1004
msgid "Sale"
msgstr ""

#: inc/post-type.php:299 inc/template-tags.php:310
#: inc/config/metabox-config.php:98 inc/core/coupon.php:708
#: inc/core/coupon.php:1005
msgid "Printable"
msgstr ""

#: inc/post-type.php:308
msgid "All coupon types"
msgstr ""

#: inc/post-type.php:319
msgid "Filter store"
msgstr ""

#: inc/post-type.php:469
msgid "Coupon"
msgstr ""

#: inc/post-type.php:470 loop/coupon-modal.php:82
#: inc/config/metabox-config.php:136
msgid "Expires"
msgstr ""

#: inc/post-type.php:471
msgid "Votes / Clicks"
msgstr ""

#: inc/post-type.php:514
msgid "[No Code]"
msgstr ""

#: inc/post-type.php:522 inc/core/coupon.php:591
msgid "Expired"
msgstr ""

#: inc/post-type.php:531 inc/config/metabox-config.php:166
msgid "Vote Up"
msgstr ""

#: inc/post-type.php:532 inc/config/metabox-config.php:173
msgid "Vote Down"
msgstr ""

#: inc/post-type.php:533
msgid "Total Used"
msgstr ""

#: inc/siteorigin.php:10
msgid "Boxed mod"
msgstr ""

#: inc/siteorigin.php:13 inc/config/metabox-config.php:292
#: inc/widgets/carousel.php:240 inc/metabox/includes/CMB2_JS.php:87
msgid "Default"
msgstr ""

#: inc/siteorigin.php:14
msgid "Shadow box"
msgstr ""

#: inc/template-tags.php:42
msgid "&larr; Previous"
msgstr ""

#: inc/template-tags.php:43
msgid "Next &rarr;"
msgstr ""

#: inc/template-tags.php:50
msgid "Posts navigation"
msgstr ""

#: inc/template-tags.php:74
#, php-format
msgid "Category: %s"
msgstr ""

#: inc/template-tags.php:76
#, php-format
msgid "Tag: %s"
msgstr ""

#: inc/template-tags.php:78
#, php-format
msgid "Author: %s"
msgstr ""

#: inc/template-tags.php:80
#, php-format
msgid "Year: %s"
msgstr ""

#: inc/template-tags.php:80
msgctxt "yearly archives date format"
msgid "Y"
msgstr ""

#: inc/template-tags.php:82
#, php-format
msgid "Month: %s"
msgstr ""

#: inc/template-tags.php:82
msgctxt "monthly archives date format"
msgid "F Y"
msgstr ""

#: inc/template-tags.php:84
#, php-format
msgid "Day: %s"
msgstr ""

#: inc/template-tags.php:84
msgctxt "daily archives date format"
msgid "F j, Y"
msgstr ""

#: inc/template-tags.php:86
msgctxt "post format archive title"
msgid "Asides"
msgstr ""

#: inc/template-tags.php:88
msgctxt "post format archive title"
msgid "Galleries"
msgstr ""

#: inc/template-tags.php:90
msgctxt "post format archive title"
msgid "Images"
msgstr ""

#: inc/template-tags.php:92
msgctxt "post format archive title"
msgid "Videos"
msgstr ""

#: inc/template-tags.php:94
msgctxt "post format archive title"
msgid "Quotes"
msgstr ""

#: inc/template-tags.php:96
msgctxt "post format archive title"
msgid "Links"
msgstr ""

#: inc/template-tags.php:98
msgctxt "post format archive title"
msgid "Statuses"
msgstr ""

#: inc/template-tags.php:100
msgctxt "post format archive title"
msgid "Audio"
msgstr ""

#: inc/template-tags.php:102
msgctxt "post format archive title"
msgid "Chats"
msgstr ""

#: inc/template-tags.php:104
#, php-format
msgid "Archives: %s"
msgstr ""

#. 1: Taxonomy singular name, 2: Current taxonomy term
#: inc/template-tags.php:108
#, php-format
msgid "%1$s: %2$s"
msgstr ""

#: inc/template-tags.php:110
msgid "Archives"
msgstr ""

#: inc/template-tags.php:190 inc/core/coupon.php:1078
msgid "Pingback:"
msgstr ""

#: inc/template-tags.php:190 inc/core/coupon.php:1078
msgid "(Edit)"
msgstr ""

#: inc/template-tags.php:206 inc/core/coupon.php:1093
#, php-format
msgid " %s ago"
msgstr ""

#: inc/template-tags.php:210 inc/core/coupon.php:1097
msgid "Your comment is awaiting moderation."
msgstr ""

#: inc/template-tags.php:285
msgid "Share link on Facebook"
msgstr ""

#: inc/template-tags.php:285 inc/core/helper.php:218
msgid "Facebook"
msgstr ""

#: inc/template-tags.php:286
msgid "Share link on Twitter"
msgstr ""

#: inc/template-tags.php:286 inc/core/helper.php:248
msgid "Twitter"
msgstr ""

#: inc/template-tags.php:287
msgid "Share link on Google+"
msgstr ""

#: inc/template-tags.php:287
msgid "Google+"
msgstr ""

#: inc/template-tags.php:288
msgid "Share image on Pinterest"
msgstr ""

#: inc/template-tags.php:288
msgid "Pinterest"
msgstr ""

#: inc/template-tags.php:307
msgid "All"
msgstr ""

#: inc/template-tags.php:308
msgid "Codes"
msgstr ""

#: inc/template-tags.php:309
msgid "Sales"
msgstr ""

#: inc/template-tags.php:405
msgid "Oops! That page can&rsquo;t be found."
msgstr ""

#: inc/template-tags.php:412
#, php-format
msgid "Search Results for: %s"
msgstr ""

#: inc/template-tags.php:625
msgid "&laquo; Previous"
msgstr ""

#: inc/template-tags.php:626
msgid "Next &raquo;"
msgstr ""

#: inc/template-tags.php:704
msgid "&hellip;"
msgstr ""

#: loop/coupon-meta.php:13
msgid "Coupon Comments"
msgstr ""

#: loop/coupon-meta.php:13
msgid "Comments"
msgstr ""

#: loop/coupon-meta.php:51
msgid "Showing most recent comments"
msgstr ""

#: loop/coupon-meta.php:54
msgid "Loading comments...."
msgstr ""

#: loop/coupon-meta.php:64 inc/core/ajax.php:152
msgid "Your comment submitted."
msgstr ""

#: loop/coupon-meta.php:68 inc/core/ajax.php:154
msgid "Something wrong! Please try again later."
msgstr ""

#: loop/coupon-meta.php:73
msgid "Add a comment"
msgstr ""

#: loop/coupon-meta.php:78
msgid "Your Name"
msgstr ""

#: loop/coupon-meta.php:81
msgid "Your Email"
msgstr ""

#: loop/coupon-meta.php:85
msgid "Submit"
msgstr ""

#: loop/coupon-modal.php:17
msgid "Deal Activated, no coupon code required!"
msgstr ""

#: loop/coupon-modal.php:20 inc/core/coupon.php:1340 inc/core/coupon.php:1436
msgid "Print this coupon and redeem it in-store"
msgstr ""

#: loop/coupon-modal.php:23
msgid "Copy this code and use at checkout"
msgstr ""

#: loop/coupon-modal.php:33 loop/coupon-modal.php:69
msgid "Go To Store"
msgstr ""

#: loop/coupon-modal.php:67
msgid "Print Now"
msgstr ""

#: loop/coupon-modal.php:75
msgid "Did it work?"
msgstr ""

#: loop/coupon-modal.php:76
msgid "Coupon Detail"
msgstr ""

#: loop/coupon-modal.php:83
msgid "Submitted"
msgstr ""

#: loop/coupon-modal.php:84
#, php-format
msgid "%s ago"
msgstr ""

#: loop/loop-coupon-cat.php:27
#, php-format
msgid "%s Coupons"
msgstr ""

#: loop/loop-coupon-cat.php:52 loop/loop-coupon.php:50
msgid "More"
msgstr ""

#: loop/loop-coupon-cat.php:60 loop/loop-coupon.php:58
#: inc/config/option-config.php:794 inc/config/option-config.php:900
#: inc/widgets/coupons.php:289
msgid "Less"
msgstr ""

#: loop/loop.php:54
msgid "Read More"
msgstr ""

#. Name of the template
msgid "Coupon Categories Listing"
msgstr ""

#: templates/category-az.php:110
msgid "Oops! No categories found"
msgstr ""

#: templates/category-az.php:112 templates/store-az.php:162
msgid "You must activate wpcoupons plugin to use this template."
msgstr ""

#. Name of the template
msgid "Front Page"
msgstr ""

#: templates/store-az.php:160
msgid "Oops! No stores found"
msgstr ""

#. Name of the template
msgid "Coupon Submit"
msgstr ""

#: inc/config/metabox-config.php:79
msgid "Coupon Settings"
msgstr ""

#: inc/config/metabox-config.php:91
msgid "Coupon Type"
msgstr ""

#: inc/config/metabox-config.php:103
msgid "Coupon Code"
msgstr ""

#: inc/config/metabox-config.php:107
msgid "Example: EMIAXHGF"
msgstr ""

#: inc/config/metabox-config.php:115
msgid "Coupon Printable Image"
msgstr ""

#: inc/config/metabox-config.php:119 inc/config/metabox-config.php:131
msgid "http://..."
msgstr ""

#: inc/config/metabox-config.php:126
msgid "Coupon URL"
msgstr ""

#: inc/config/metabox-config.php:129
msgid "Coupon URL, if this field empty then Store Aff URL will be use."
msgstr ""

#: inc/config/metabox-config.php:139
msgid "Set expires for coupon. Uses GMT+0"
msgstr ""

#: inc/config/metabox-config.php:143
msgid "Exclusive Coupon"
msgstr ""

#: inc/config/metabox-config.php:144
msgid "This coupon is exclusive"
msgstr ""

#: inc/config/metabox-config.php:152
msgid "Number Coupon Used"
msgstr ""

#: inc/config/metabox-config.php:159
msgid "Number Views"
msgstr ""

#: inc/config/metabox-config.php:193
msgid "Page Settings"
msgstr ""

#: inc/config/metabox-config.php:198
msgid "Page layout"
msgstr ""

#: inc/config/metabox-config.php:199
msgid ""
"Select page layout to display, leave empty to use theme option settings."
msgstr ""

#: inc/config/metabox-config.php:202 inc/config/metabox-config.php:228
msgid "Default (Theme options)"
msgstr ""

#: inc/config/metabox-config.php:205 inc/config/option-config.php:231
#: inc/config/option-config.php:819 inc/config/option-config.php:925
msgid "Right sidebar"
msgstr ""

#: inc/config/metabox-config.php:206 inc/config/option-config.php:229
#: inc/config/option-config.php:818 inc/config/option-config.php:924
msgid "Left sidebar"
msgstr ""

#: inc/config/metabox-config.php:207 inc/config/option-config.php:230
msgid "No sidebar"
msgstr ""

#: inc/config/metabox-config.php:212
msgid "Display content in shadow box"
msgstr ""

#: inc/config/metabox-config.php:213
msgid "Wrapper content by shadow box."
msgstr ""

#: inc/config/metabox-config.php:218
msgid "No, display by default"
msgstr ""

#: inc/config/metabox-config.php:219
msgid "Yes, Display content in a shadow box"
msgstr ""

#: inc/config/metabox-config.php:224
msgid "Custom page header"
msgstr ""

#: inc/config/metabox-config.php:225
msgid "Custom page header."
msgstr ""

#: inc/config/metabox-config.php:231
msgid "Show page title"
msgstr ""

#: inc/config/metabox-config.php:232
msgid "Hide page title"
msgstr ""

#: inc/config/metabox-config.php:238
msgid "Hide breadcrumb"
msgstr ""

#: inc/config/metabox-config.php:240
#, php-format
msgid ""
"Check this if you want to hide breadcrumb. NOTE: you must install plugin "
"%1$s to use Breadcrumb."
msgstr ""

#: inc/config/metabox-config.php:240
msgid "Breadcrumb Navxt"
msgstr ""

#: inc/config/metabox-config.php:246
msgid "Custom page title"
msgstr ""

#: inc/config/metabox-config.php:248
msgid "Display page title difference the title above."
msgstr ""

#: inc/config/metabox-config.php:253
msgid "Hide Header cover"
msgstr ""

#: inc/config/metabox-config.php:255
msgid "Check this if you want to hide header cover"
msgstr ""

#: inc/config/metabox-config.php:261
msgid "Cover background image"
msgstr ""

#: inc/config/metabox-config.php:267
msgid "Cover background color"
msgstr ""

#: inc/config/metabox-config.php:277
msgid "Number products to show"
msgstr ""

#: inc/config/metabox-config.php:286
msgid "Number products per row"
msgstr ""

#: inc/config/metabox-config.php:294
msgid "2 columns"
msgstr ""

#: inc/config/metabox-config.php:295 inc/config/option-config.php:1164
msgid "3 Columns"
msgstr ""

#: inc/config/metabox-config.php:296 inc/config/option-config.php:1165
msgid "4 Columns"
msgstr ""

#: inc/config/metabox-config.php:297
msgid "5 Columns"
msgstr ""

#: inc/config/metabox-config.php:298
msgid "6 Columns"
msgstr ""

#: inc/config/metabox-config.php:323
msgid "Store Descriptions"
msgstr ""

#: inc/config/metabox-config.php:330
msgid "Home URL"
msgstr ""

#: inc/config/metabox-config.php:332
msgid "Store Website home page URL."
msgstr ""

#: inc/config/metabox-config.php:335 inc/config/metabox-config.php:345
msgid "http://example.com"
msgstr ""

#: inc/config/metabox-config.php:340
msgid "Affiliate URL"
msgstr ""

#: inc/config/metabox-config.php:342
msgid "Store Affiliate URL."
msgstr ""

#: inc/config/metabox-config.php:351
msgid "Auto generate thumbnail"
msgstr ""

#: inc/config/metabox-config.php:352
msgid ""
"Auto download store home page screenshoot and set it as thumbnail for this "
"store if store url is correct. This function is disable automatically if the "
"thumbnail bellow has data."
msgstr ""

#: inc/config/metabox-config.php:369
msgid "Custom store heading"
msgstr ""

#: inc/config/metabox-config.php:371
#, php-format
msgid ""
"The title will display in single store, example: Macy's Coupon Code and "
"Deals, if empty then store custom heading from theme option will be used. "
"You can use %store_name% for current store name."
msgstr ""

#: inc/config/metabox-config.php:377
msgid "Featured Store"
msgstr ""

#: inc/config/metabox-config.php:378
msgid "Check this if you want to this store is featured."
msgstr ""

#: inc/config/metabox-config.php:385
msgid "Extra Info"
msgstr ""

#: inc/config/metabox-config.php:386
msgid "This content display after product listing on single store page."
msgstr ""

#: inc/config/metabox-config.php:411
msgid "Category info"
msgstr ""

#: inc/config/metabox-config.php:426
msgid "Image"
msgstr ""

#: inc/config/option-config.php:50
msgid "Theme Information 1"
msgstr ""

#: inc/config/option-config.php:51 inc/config/option-config.php:57
msgid "<p>This is the tab content, HTML is allowed.</p>"
msgstr ""

#: inc/config/option-config.php:56
msgid "Theme Information 2"
msgstr ""

#: inc/config/option-config.php:61
msgid "<p>This is the sidebar content, HTML is allowed.</p>"
msgstr ""

#: inc/config/option-config.php:84
msgid "Coupon WP"
msgstr ""

#: inc/config/option-config.php:85
msgid "Theme Options"
msgstr ""

#: inc/config/option-config.php:197
msgid "General"
msgstr ""

#: inc/config/option-config.php:208
msgid "Site Logo"
msgstr ""

#: inc/config/option-config.php:210
msgid "Upload your logo here."
msgstr ""

#: inc/config/option-config.php:216
msgid "Site Logo Retina"
msgstr ""

#: inc/config/option-config.php:218
msgid ""
"Upload at exactly 2x the size of your standard logo (optional), the name "
"should include @2x at the end, example <EMAIL>"
msgstr ""

#: inc/config/option-config.php:223
msgid "Site Layout"
msgstr ""

#: inc/config/option-config.php:224
msgid "Default site layout"
msgstr ""

#: inc/config/option-config.php:240
msgid "Coupon categories listing page"
msgstr ""

#: inc/config/option-config.php:249
msgid "Stores listing page"
msgstr ""

#: inc/config/option-config.php:257
msgid "Hide child stores on listing page"
msgstr ""

#: inc/config/option-config.php:271
msgid "Custom Store rewrite slug"
msgstr ""

#: inc/config/option-config.php:272
msgid "Default: store"
msgstr ""

#: inc/config/option-config.php:274 inc/config/option-config.php:284
#, php-format
msgid ""
"If you change this option please go to Settings &#8594; %1$s and refresh "
"your permalink structure before your custom post type will show the correct "
"structure."
msgstr ""

#: inc/config/option-config.php:274 inc/config/option-config.php:284
msgid "Permalinks"
msgstr ""

#: inc/config/option-config.php:281
msgid "Custom coupon category rewrite slug"
msgstr ""

#: inc/config/option-config.php:282
msgid "Default: coupon-category"
msgstr ""

#: inc/config/option-config.php:296
msgid "Disable feed links."
msgstr ""

#: inc/config/option-config.php:297
msgid "If you want to disable feed links just check this option."
msgstr ""

#: inc/config/option-config.php:308
msgid "Styling"
msgstr ""

#: inc/config/option-config.php:405
msgid "Secondary"
msgstr ""

#: inc/config/option-config.php:439
msgid "Coupon code"
msgstr ""

#: inc/config/option-config.php:452
msgid "Coupon sale"
msgstr ""

#: inc/config/option-config.php:465
msgid "Coupon print"
msgstr ""

#: inc/config/option-config.php:478
msgid "Body background"
msgstr ""

#: inc/config/option-config.php:492
msgid "Header"
msgstr ""

#: inc/config/option-config.php:502
msgid "Enable Header Sticky"
msgstr ""

#: inc/config/option-config.php:504
msgid "This function apply for desktop only."
msgstr ""

#: inc/config/option-config.php:510
msgid "Header Icons"
msgstr ""

#: inc/config/option-config.php:512
#, php-format
msgid "You can find icon code at %s"
msgstr ""

#: inc/config/option-config.php:514
msgid "Label"
msgstr ""

#: inc/config/option-config.php:515
msgid "Enter you icon code, Ability use HTML code here."
msgstr ""

#: inc/config/option-config.php:524
msgid "Item"
msgstr ""

#: inc/config/option-config.php:530
msgid "Top Search Stores"
msgstr ""

#: inc/config/option-config.php:542
msgid "Custom your header style?"
msgstr ""

#: inc/config/option-config.php:550
msgid "Header Background"
msgstr ""

#: inc/config/option-config.php:564
msgid "Typography"
msgstr ""

#: inc/config/option-config.php:574
msgid "Body"
msgstr ""

#: inc/config/option-config.php:590
msgid "Select custom font for your main body text."
msgstr ""

#: inc/config/option-config.php:598
msgid "Heading"
msgstr ""

#: inc/config/option-config.php:614
msgid "Select custom font for heading like h1, h2, h3, ..."
msgstr ""

#: inc/config/option-config.php:625
msgid "Menu Bar"
msgstr ""

#: inc/config/option-config.php:639
msgid "Primary Menu Typography"
msgstr ""

#: inc/config/option-config.php:656
msgid "Custom typography for primary menu."
msgstr ""

#: inc/config/option-config.php:667
msgid "Page"
msgstr ""

#: inc/config/option-config.php:676 inc/config/option-config.php:729
msgid "Page header"
msgstr ""

#: inc/config/option-config.php:681 inc/config/option-config.php:733
msgid "Show page header"
msgstr ""

#: inc/config/option-config.php:682 inc/config/option-config.php:734
msgid "Hide page header "
msgstr ""

#: inc/config/option-config.php:689 inc/config/option-config.php:749
msgid "Show header breadcrumb"
msgstr ""

#: inc/config/option-config.php:692 inc/config/option-config.php:752
msgid ""
"Check this if you want to show breadcrumb. NOTE: you must install plugin "
"Breadcrumb Navxt to use this function."
msgstr ""

#: inc/config/option-config.php:698 inc/config/option-config.php:758
msgid "Show cover image"
msgstr ""

#: inc/config/option-config.php:707 inc/config/option-config.php:767
msgid "Header cover image"
msgstr ""

#: inc/config/option-config.php:721
msgid "Blog"
msgstr ""

#: inc/config/option-config.php:741
msgid "Custom blog title"
msgstr ""

#: inc/config/option-config.php:780
msgid "Single Store"
msgstr ""

#: inc/config/option-config.php:788
msgid "Coupon Store template"
msgstr ""

#: inc/config/option-config.php:789
msgid "Select template for store coupons."
msgstr ""

#: inc/config/option-config.php:793 inc/config/option-config.php:901
#: inc/widgets/coupons.php:290
msgid "Full"
msgstr ""

#: inc/config/option-config.php:802 inc/config/option-config.php:909
#: inc/config/option-config.php:1032
msgid "Show coupon item thumbnails"
msgstr ""

#: inc/config/option-config.php:804 inc/config/option-config.php:911
#: inc/config/option-config.php:1034
msgid "Default, Show if has thumbnail else store thumbnail instead."
msgstr ""

#: inc/config/option-config.php:805 inc/config/option-config.php:912
#: inc/config/option-config.php:1035
msgid "Show if has thumbnail"
msgstr ""

#: inc/config/option-config.php:806 inc/config/option-config.php:913
#: inc/config/option-config.php:1036
msgid "Hide All"
msgstr ""

#: inc/config/option-config.php:812
msgid "Single Store Layout"
msgstr ""

#: inc/config/option-config.php:813
msgid "Default single store layout."
msgstr ""

#: inc/config/option-config.php:825
msgid "Store social sharing"
msgstr ""

#: inc/config/option-config.php:826
msgid "Enable social share under store description"
msgstr ""

#: inc/config/option-config.php:833
msgid "Store custom heading"
msgstr ""

#: inc/config/option-config.php:834
msgid ""
"Custom heading text for display on single store page. Use %store_name% to "
"replace with current store name."
msgstr ""

#: inc/config/option-config.php:840
msgid "Store unpopular coupon text"
msgstr ""

#: inc/config/option-config.php:846
msgid "Store expired coupon text."
msgstr ""

#: inc/config/option-config.php:853
msgid "Number active coupons to show"
msgstr ""

#: inc/config/option-config.php:860
msgid "Number unpopular coupons to show"
msgstr ""

#: inc/config/option-config.php:867
msgid "Number expires coupons to show"
msgstr ""

#: inc/config/option-config.php:875
msgid "Custom goto store slug"
msgstr ""

#: inc/config/option-config.php:876 inc/config/option-config.php:992
#: inc/config/option-config.php:1108
#, php-format
msgid ""
"When you enable this option maybe the permalinks will effect, to resolve "
"this go to %1$s and hit \"Save Changes\" button."
msgstr ""

#: inc/config/option-config.php:876 inc/config/option-config.php:992
#: inc/config/option-config.php:1108
msgid "Permalinks Settings"
msgstr ""

#: inc/config/option-config.php:887
msgid "Coupon Category"
msgstr ""

#: inc/config/option-config.php:895
msgid "Coupon Category template"
msgstr ""

#: inc/config/option-config.php:896
msgid "Select template for coupon category."
msgstr ""

#: inc/config/option-config.php:919
msgid "Coupon Category Layout"
msgstr ""

#: inc/config/option-config.php:920
msgid "Default coupon category layout."
msgstr ""

#: inc/config/option-config.php:931
msgid "Coupon category social sharing"
msgstr ""

#: inc/config/option-config.php:932
msgid "Enable social share under coupon category description"
msgstr ""

#: inc/config/option-config.php:939
msgid "Coupon category custom heading"
msgstr ""

#: inc/config/option-config.php:940
msgid ""
"Custom heading text for display on coupon category page. You can use "
"%coupon_cate% to display category name."
msgstr ""

#: inc/config/option-config.php:946
msgid "Coupon category sub-heading"
msgstr ""

#: inc/config/option-config.php:947
msgid "You can use %coupon_cate% to display category name."
msgstr ""

#: inc/config/option-config.php:953
msgid "How many coupons display by default?"
msgstr ""

#: inc/config/option-config.php:957
msgid "Coupon listing paging"
msgstr ""

#: inc/config/option-config.php:961
msgid "Paging Navigation"
msgstr ""

#: inc/config/option-config.php:962
msgid "Load more with ajax"
msgstr ""

#: inc/config/option-config.php:968
msgid "Category advertisement"
msgstr ""

#: inc/config/option-config.php:969
msgid "Display custom ads after coupons listing on single category."
msgstr ""

#: inc/config/option-config.php:981
msgid "Coupons"
msgstr ""

#: inc/config/option-config.php:991
msgid "Enable single page for coupon."
msgstr ""

#: inc/config/option-config.php:999
msgid "Auto open coupon modal on single coupon."
msgstr ""

#: inc/config/option-config.php:1007
msgid "Enable popular coupons on single page."
msgstr ""

#: inc/config/option-config.php:1015
msgid "Custom popular text."
msgstr ""

#: inc/config/option-config.php:1016
msgid "Use {store} to display store name."
msgstr ""

#: inc/config/option-config.php:1024
msgid "Number popular coupons on single page."
msgstr ""

#: inc/config/option-config.php:1044
msgid "Show coupon read more coupon description."
msgstr ""

#: inc/config/option-config.php:1051
msgid "Exclusive Coupon Message"
msgstr ""

#: inc/config/option-config.php:1056
msgid "When coupon expires"
msgstr ""

#: inc/config/option-config.php:1060
msgid "Do Nothing"
msgstr ""

#: inc/config/option-config.php:1061
msgid "Disable"
msgstr ""

#: inc/config/option-config.php:1062 inc/widgets/slider.php:62
#: inc/widgets/slider.php:85 inc/metabox/includes/CMB2_JS.php:138
#: inc/metabox/includes/CMB2_Types.php:257
#: inc/metabox/includes/types/CMB2_Type_File_Base.php:80
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:139
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:199
msgid "Remove"
msgstr ""

#: inc/config/option-config.php:1068
msgid "Run expires action time"
msgstr ""

#: inc/config/option-config.php:1069
msgid "Run action after coupon expires x seconds., default: 604800 (1 week)"
msgstr ""

#: inc/config/option-config.php:1078
msgid "Open store website in previous tab when click on print button."
msgstr ""

#: inc/config/option-config.php:1085
msgid "Open store website in previous tab when click on \"Get Deal\" button."
msgstr ""

#: inc/config/option-config.php:1092
msgid "Open store website in previous tab when click on \"Get Code\" button."
msgstr ""

#: inc/config/option-config.php:1098
msgid "Default coupon excerpt length"
msgstr ""

#: inc/config/option-config.php:1107
msgid "Custom coupon go out slug"
msgstr ""

#: inc/config/option-config.php:1129
msgid "Before footer"
msgstr ""

#: inc/config/option-config.php:1130
msgid "Note: This field only display on homepage"
msgstr ""

#: inc/config/option-config.php:1137
msgid "Before Footer Display"
msgstr ""

#: inc/config/option-config.php:1138
msgid ""
"Note: Setting home page goto Settings -> Reading -> Front page displays -> "
"Check static page -> Select a page"
msgstr ""

#: inc/config/option-config.php:1142
msgid "Apply for home page only."
msgstr ""

#: inc/config/option-config.php:1143
msgid "Apply for all pages."
msgstr ""

#: inc/config/option-config.php:1151
msgid "Enable footer widgets area."
msgstr ""

#: inc/config/option-config.php:1157
msgid "Footer Columns"
msgstr ""

#: inc/config/option-config.php:1158
msgid ""
"Select the number of columns you would like for your footer widgets area."
msgstr ""

#: inc/config/option-config.php:1162
msgid "1 Column"
msgstr ""

#: inc/config/option-config.php:1163
msgid "2 Columns"
msgstr ""

#: inc/config/option-config.php:1174
msgid "Footer 2 Columns layout"
msgstr ""

#: inc/config/option-config.php:1175 inc/config/option-config.php:1185
#: inc/config/option-config.php:1195
msgid "Custom footer columns width"
msgstr ""

#: inc/config/option-config.php:1176 inc/config/option-config.php:1186
#: inc/config/option-config.php:1196
msgid ""
"Enter int numbers and sum of them must smaller or equal 16, separated by "
"\"+\""
msgstr ""

#: inc/config/option-config.php:1184
msgid "Footer 3 Columns layout"
msgstr ""

#: inc/config/option-config.php:1194
msgid "Footer 4 Columns layout"
msgstr ""

#: inc/config/option-config.php:1202
msgid "Footer Copyright"
msgstr ""

#: inc/config/option-config.php:1203
msgid "Enter the copyright section text."
msgstr ""

#: inc/config/option-config.php:1209
msgid "Enable theme author links."
msgstr ""

#: inc/config/option-config.php:1216
msgid "Custom your footer style?"
msgstr ""

#: inc/config/option-config.php:1224
msgid "Footer Background"
msgstr ""

#: inc/config/option-config.php:1235
msgid "Footer Text Color"
msgstr ""

#: inc/config/option-config.php:1244
msgid "Footer Link Color"
msgstr ""

#: inc/config/option-config.php:1253
msgid "Footer Link Color Hover"
msgstr ""

#: inc/config/option-config.php:1262
msgid "Footer Widget Title Color"
msgstr ""

#: inc/config/option-config.php:1283
msgid "Share coupon code email title"
msgstr ""

#: inc/config/option-config.php:1284
msgid ""
"Available Tags: {coupon_title}, {coupon_description}, "
"{coupon_destination_url}, {coupon_print_image_url}, {coupon_code}, "
"{store_name}, {store_go_out_url}, {store_url}, {store_aff_url}, {home_url}, "
"{share_email}"
msgstr ""

#: inc/config/option-config.php:1291
msgid "Share coupon code email template"
msgstr ""

#: inc/config/option-config.php:1292 inc/config/option-config.php:1300
#: inc/config/option-config.php:1308
msgid ""
"Available Tags: {coupon_title}, {coupon_description}, "
"{coupon_destination_url}, {coupon_print_image}, {coupon_print_image_url}, "
"{coupon_code}, {store_name}, {store_image}, {store_go_out_url}, {store_url}, "
"{store_aff_url}, {home_url}, {share_email}"
msgstr ""

#: inc/config/option-config.php:1299
msgid "Share coupon sale email template"
msgstr ""

#: inc/config/option-config.php:1307
msgid "Share coupon print email template"
msgstr ""

#: inc/core/admin-update.php:112 inc/core/admin-update.php:113
msgid "Auto Update"
msgstr ""

#. Name of the theme
#: inc/core/admin-update.php:120 inc/core/admin-update.php:121
msgid "WP Coupon"
msgstr ""

#: inc/core/admin-update.php:158
#, php-format
msgid "Auto Update Theme- %1$s"
msgstr ""

#: inc/core/admin-update.php:505
msgid "Theme License"
msgstr ""

#: inc/core/admin-update.php:506
msgid "Enter your theme license key."
msgstr ""

#: inc/core/admin-update.php:507
msgid "License Key"
msgstr ""

#: inc/core/admin-update.php:508
msgid "License Action"
msgstr ""

#: inc/core/admin-update.php:509
msgid "Deactivate License"
msgstr ""

#: inc/core/admin-update.php:510
msgid "Activate License"
msgstr ""

#: inc/core/admin-update.php:511 inc/core/admin-update.php:512
#: inc/core/admin-update.php:524
msgid "License status is unknown."
msgstr ""

#: inc/core/admin-update.php:513
msgid "Renew?"
msgstr ""

#: inc/core/admin-update.php:514
msgid "unlimited"
msgstr ""

#: inc/core/admin-update.php:515
msgid "License key is active."
msgstr ""

#: inc/core/admin-update.php:516
#, php-format
msgid "Expires %s."
msgstr ""

#: inc/core/admin-update.php:517
#, php-format
msgid "You have %1$s / %2$s sites activated."
msgstr ""

#: inc/core/admin-update.php:518
#, php-format
msgid "License key expired %s."
msgstr ""

#: inc/core/admin-update.php:519
msgid "License key has expired."
msgstr ""

#: inc/core/admin-update.php:520
msgid "License keys do not match."
msgstr ""

#: inc/core/admin-update.php:521
msgid "License is inactive."
msgstr ""

#: inc/core/admin-update.php:522
msgid "License key is disabled."
msgstr ""

#: inc/core/admin-update.php:523
msgid "Site is inactive."
msgstr ""

#: inc/core/admin-update.php:525
msgid ""
"Updating this theme will lose any customizations you have made. 'Cancel' to "
"stop, 'OK' to update."
msgstr ""

#: inc/core/admin-update.php:526
#, php-format
msgid ""
"<strong>%1$s %2$s</strong> is available. <a href=\"%3$s\" class=\"thickbox\" "
"title=\"%4s\">Check out what's new</a> or <a href=\"%5$s\"%6$s>update now</a>"
"."
msgstr ""

#: inc/core/ajax.php:47 inc/core/store.php:609
#, php-format
msgid "%d Coupon"
msgid_plural "%d Coupons"
msgstr[0] ""
msgstr[1] ""

#: inc/core/ajax.php:127
msgid "Your email has been sent successfully"
msgstr ""

#: inc/core/ajax.php:229
msgid "Unknown request."
msgstr ""

#: inc/core/coupon.php:594
msgid "N/A"
msgstr ""

#: inc/core/coupon.php:1330 inc/core/coupon.php:1426
msgid "Coupon Description:"
msgstr ""

#: inc/core/coupon.php:1332 inc/core/coupon.php:1428
msgid "Store:"
msgstr ""

#: inc/core/coupon.php:1336 inc/core/coupon.php:1432
msgid "Click the link bellow to get deal"
msgstr ""

#: inc/core/coupon.php:1337 inc/core/coupon.php:1433
msgid "Get deal now"
msgstr ""

#: inc/core/coupon.php:1345 inc/core/coupon.php:1442
msgid "Copy coupon code and use at checkout:"
msgstr ""

#: inc/core/coupon.php:1348 inc/core/coupon.php:1446
msgid "Store website:"
msgstr ""

#: inc/core/coupon.php:1350 inc/core/coupon.php:1449
#, php-format
msgid "This email was sent form %1$s"
msgstr ""

#: inc/core/coupon.php:1394
msgid "Print coupon"
msgstr ""

#: inc/core/schedule-event.php:25
#, php-format
msgid "Expires <span class=\"count\">(%s)</span>"
msgid_plural "Expires <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/core/store.php:401
msgid "Untitled"
msgstr ""

#: inc/core/term-editor.php:105 inc/core/term-editor.php:132
#: inc/widgets/slider.php:73
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:157
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:216
msgid "Description"
msgstr ""

#: inc/core/term-editor.php:107 inc/core/term-editor.php:134
msgid ""
"The description is not prominent by default, however some themes may show it."
msgstr ""

#: inc/metabox-addons/extra-types.php:43
#, php-format
msgid "%s view"
msgid_plural "%s views"
msgstr[0] ""
msgstr[1] ""

#: inc/metabox-addons/post-search-field.php:55
msgid "An error has occurred. Please reload the page and try again."
msgstr ""

#: inc/metabox-addons/post-search-field.php:56
msgid "Find Posts or Pages"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:46
msgid "Redux Framework"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:52
msgid "Breadcrumb NavXT"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:58
msgid "WP Users"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:65
msgid "WPCoupon Demo Import"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:89
msgid "Install Required Plugins"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:90
msgid "Install Plugins"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:91
#, php-format
msgid "Installing Plugin: %s"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:92
msgid "Something went wrong with the plugin API."
msgstr ""

#: inc/tgmpa/tgmpa-config.php:93
#, php-format
msgid "This theme requires the following plugin: %1$s."
msgid_plural "This theme requires the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:94
#, php-format
msgid "This theme recommends the following plugin: %1$s."
msgid_plural "This theme recommends the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:95
#, php-format
msgid ""
"Sorry, but you do not have the correct permissions to install the %s plugin. "
"Contact the administrator of this site for help on getting the plugin "
"installed."
msgid_plural ""
"Sorry, but you do not have the correct permissions to install the %s plugins."
" Contact the administrator of this site for help on getting the plugins "
"installed."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:96
#, php-format
msgid "The following required plugin is currently inactive: %1$s."
msgid_plural "The following required plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:97
#, php-format
msgid "The following recommended plugin is currently inactive: %1$s."
msgid_plural "The following recommended plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:98
#, php-format
msgid ""
"Sorry, but you do not have the correct permissions to activate the %s plugin."
" Contact the administrator of this site for help on getting the plugin "
"activated."
msgid_plural ""
"Sorry, but you do not have the correct permissions to activate the %s "
"plugins. Contact the administrator of this site for help on getting the "
"plugins activated."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:99
#, php-format
msgid ""
"The following plugin needs to be updated to its latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgid_plural ""
"The following plugins need to be updated to their latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:100
#, php-format
msgid ""
"Sorry, but you do not have the correct permissions to update the %s plugin. "
"Contact the administrator of this site for help on getting the plugin "
"updated."
msgid_plural ""
"Sorry, but you do not have the correct permissions to update the %s plugins. "
"Contact the administrator of this site for help on getting the plugins "
"updated."
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:101
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:102
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/tgmpa/tgmpa-config.php:103
msgid "Return to Required Plugins Installer"
msgstr ""

#: inc/tgmpa/tgmpa-config.php:104
msgid "Plugin activated successfully."
msgstr ""

#: inc/tgmpa/tgmpa-config.php:105
#, php-format
msgid "All plugins installed and activated successfully. %s"
msgstr ""

#: inc/user/user.php:179 inc/user/user.php:420 inc/user/user.php:423
msgid "Dashboard"
msgstr ""

#: inc/user/user.php:180
msgid "Saved Coupons"
msgstr ""

#: inc/user/user.php:181
msgid "Favorites Stores"
msgstr ""

#: inc/user/user.php:182
msgid "Settings"
msgstr ""

#: inc/user/user.php:184
msgid "Public profile"
msgstr ""

#: inc/user/user.php:225
msgid "Recently Saved Coupons"
msgstr ""

#: inc/user/user.php:267
msgid "See all your saved coupons"
msgstr ""

#: inc/user/user.php:275
msgid "It's looks like you didn't add any coupons yet."
msgstr ""

#: inc/user/user.php:281
msgid "Browse Coupons to add"
msgstr ""

#: inc/user/user.php:302 inc/user/user.php:683
msgid "Recent Favorite Stores"
msgstr ""

#: inc/user/user.php:322
msgid "See all your favorite stores"
msgstr ""

#: inc/user/user.php:330
msgid "It's looks like you didn't add any stores yet."
msgstr ""

#: inc/user/user.php:338
msgid "Browse Stores to add"
msgstr ""

#: inc/user/user.php:376
msgid "Saved"
msgstr ""

#: inc/user/user.php:389
msgid "Please login to see your saved coupons"
msgstr ""

#: inc/user/user.php:398
msgid "Favorites"
msgstr ""

#: inc/user/user.php:411
msgid "Please login to see your favorite stores"
msgstr ""

#: inc/user/user.php:420
msgid "Login"
msgstr ""

#: inc/user/user.php:424
msgid "Account Settings"
msgstr ""

#: inc/user/user.php:425
msgid "Sign Out"
msgstr ""

#: inc/user/user.php:448
msgid "User Name:"
msgstr ""

#: inc/user/user.php:455
msgid "E-mail:"
msgstr ""

#: inc/user/user.php:464
msgid "First Name:"
msgstr ""

#: inc/user/user.php:474
msgid "Last Name:"
msgstr ""

#: inc/user/user.php:483
msgid "Display Name:"
msgstr ""

#: inc/user/user.php:490
msgid "Website:"
msgstr ""

#: inc/user/user.php:498
msgid "Bio:"
msgstr ""

#: inc/user/user.php:631 inc/user/user.php:781
msgid "Best coupons form your Favorite Stores"
msgstr ""

#: inc/user/user.php:633 inc/user/user.php:657 inc/user/user.php:685
msgid "View all"
msgstr ""

#: inc/user/user.php:645
msgid "No Coupons found! Please add more favorite stores to see this."
msgstr ""

#: inc/user/user.php:655
msgid "Recent Saved Coupons"
msgstr ""

#: inc/user/user.php:671
msgid "No Coupons found! Please add more see this."
msgstr ""

#: inc/user/user.php:726
msgid "No Stores found! Please add more see this."
msgstr ""

#: inc/user/user.php:744
msgid "Your Saved Coupons"
msgstr ""

#: inc/user/user.php:757
msgid "No saved coupons found ! Please add more."
msgstr ""

#: inc/user/user.php:794
msgid "No coupons found ! Please add more favorite store to see this."
msgstr ""

#: inc/user/user.php:807
msgid "Favorite Stores"
msgstr ""

#: inc/user/user.php:848
msgid "No saved stores found ! Please add more."
msgstr ""

#. Base ID
#: inc/widgets/carousel.php:12
msgid "WPCoupon Store Carousel"
msgstr ""

#: inc/widgets/carousel.php:14
msgid "Display store width Carousel"
msgstr ""

#: inc/widgets/carousel.php:210 inc/widgets/categories.php:118
#: inc/widgets/coupons.php:269 inc/widgets/headline.php:32
#: inc/widgets/newsletter.php:158 inc/widgets/popular-stores.php:117
#: inc/widgets/slider.php:113
msgid "Title:"
msgstr ""

#: inc/widgets/carousel.php:217
msgid "Featured stores only."
msgstr ""

#: inc/widgets/carousel.php:221
msgid "Include:"
msgstr ""

#: inc/widgets/carousel.php:226
msgid "Exclude:"
msgstr ""

#: inc/widgets/carousel.php:231
msgid "How many stores to display ?"
msgstr ""

#: inc/widgets/carousel.php:236 inc/widgets/categories.php:152
msgid "Order by:"
msgstr ""

#: inc/widgets/carousel.php:241
msgid "Number coupons"
msgstr ""

#: inc/widgets/carousel.php:242 inc/widgets/slider.php:68
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:153
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:212
msgid "Title"
msgstr ""

#: inc/widgets/carousel.php:243
msgid "Preserve Store ID order given in the inlcude IDs"
msgstr ""

#: inc/widgets/carousel.php:252 inc/widgets/categories.php:166
msgid "Order:"
msgstr ""

#: inc/widgets/carousel.php:256 inc/widgets/categories.php:170
msgid "Desc"
msgstr ""

#: inc/widgets/carousel.php:257 inc/widgets/categories.php:171
msgid "Asc"
msgstr ""

#: inc/widgets/carousel.php:269 inc/widgets/slider.php:144
msgid "Slide speed (millisecond)"
msgstr ""

#: inc/widgets/carousel.php:274 inc/widgets/slider.php:149
msgid "Pagination speed (millisecond)"
msgstr ""

#: inc/widgets/carousel.php:279
msgid "Number items visible:"
msgstr ""

#: inc/widgets/carousel.php:289
msgid "Number items visible on small desktop :"
msgstr ""

#: inc/widgets/carousel.php:299
msgid "Number items visible on mobile:"
msgstr ""

#: inc/widgets/carousel.php:310 inc/widgets/slider.php:154
msgid "Auto play:"
msgstr ""

#: inc/widgets/carousel.php:314 inc/widgets/carousel.php:328
#: inc/widgets/coupons.php:303 inc/widgets/slider.php:158
#: inc/widgets/slider.php:172
msgid "Yes"
msgstr ""

#: inc/widgets/carousel.php:315 inc/widgets/slider.php:159
#: inc/widgets/slider.php:173
msgid "no"
msgstr ""

#: inc/widgets/carousel.php:324 inc/widgets/slider.php:168
msgid "Stop on hover:"
msgstr ""

#: inc/widgets/carousel.php:329 inc/widgets/coupons.php:302
msgid "No"
msgstr ""

#. Base ID
#: inc/widgets/categories.php:12
msgid "WPCoupon Categories"
msgstr ""

#: inc/widgets/categories.php:14
msgid "Display any taxonomies as categories list"
msgstr ""

#: inc/widgets/categories.php:123
msgid "Taxonomy:"
msgstr ""

#: inc/widgets/categories.php:132
msgid "Depth:"
msgstr ""

#: inc/widgets/categories.php:136
msgid "All Categories and child Categories"
msgstr ""

#: inc/widgets/categories.php:137
msgid ""
"All Categories displayed in flat (no indent) form (overrides hierarchical)"
msgstr ""

#: inc/widgets/categories.php:138
msgid "Show only top level Categories"
msgstr ""

#: inc/widgets/categories.php:139
msgid ""
"Value of n (a number) specifies the depth (or level) to descend in "
"displaying Categories"
msgstr ""

#: inc/widgets/categories.php:156
msgid "Count"
msgstr ""

#: inc/widgets/categories.php:181 inc/widgets/popular-stores.php:121
msgid "Number store to show:"
msgstr ""

#: inc/widgets/categories.php:186
msgid "Show count ?"
msgstr ""

#: inc/widgets/categories.php:191 inc/widgets/popular-stores.php:133
msgid "Number item per row:"
msgstr ""

#: inc/widgets/categories.php:288
#, php-format
msgid "Feed for all posts filed under %s"
msgstr ""

#. Base ID
#: inc/widgets/coupons.php:12
msgid "WPCoupon Coupons"
msgstr ""

#: inc/widgets/coupons.php:14
msgid "Display Coupons"
msgstr ""

#: inc/widgets/coupons.php:96
msgid "Latest Coupons"
msgstr ""

#: inc/widgets/coupons.php:99
msgid "Popular Coupons"
msgstr ""

#: inc/widgets/coupons.php:102
msgid "Ending Soon"
msgstr ""

#: inc/widgets/coupons.php:126 inc/widgets/coupons.php:165
msgid "There is no coupons! Please comeback later."
msgstr ""

#: inc/widgets/coupons.php:211
msgid "There is no coupons ! Please comeback later."
msgstr ""

#: inc/widgets/coupons.php:273
msgid "Number coupons to show:"
msgstr ""

#: inc/widgets/coupons.php:277
msgid "Excerpt length:"
msgstr ""

#: inc/widgets/coupons.php:281
msgid "Excerpt length if hide thumbnails:"
msgstr ""

#: inc/widgets/coupons.php:285
msgid "Box layout:"
msgstr ""

#: inc/widgets/coupons.php:298
msgid "Show paging:"
msgstr ""

#: inc/widgets/coupons.php:313
msgid "Do not show expired coupons."
msgstr ""

#: inc/widgets/coupons.php:319
msgid "Hide latest coupons tab"
msgstr ""

#: inc/widgets/coupons.php:323
msgid "Show popular tab"
msgstr ""

#: inc/widgets/coupons.php:327
msgid "Show ending tab"
msgstr ""

#. Base ID
#: inc/widgets/headline.php:12
msgid "WPCoupon Headline"
msgstr ""

#: inc/widgets/headline.php:14
msgid "Display a headline"
msgstr ""

#: inc/widgets/headline.php:37
msgid "Heading level"
msgstr ""

#. Base ID
#: inc/widgets/newsletter.php:13
msgid "WPCoupon Newsletter"
msgstr ""

#: inc/widgets/newsletter.php:15
msgid "Display newsletter box"
msgstr ""

#: inc/widgets/newsletter.php:66
msgid "Your email"
msgstr ""

#: inc/widgets/newsletter.php:68
msgid "Subscribe"
msgstr ""

#: inc/widgets/newsletter.php:163
msgid "Mailchimp action:"
msgstr ""

#: inc/widgets/newsletter.php:171
msgid "Before form"
msgstr ""

#: inc/widgets/newsletter.php:176
msgid "After form"
msgstr ""

#: inc/widgets/newsletter.php:181
msgid "facebook URL:"
msgstr ""

#: inc/widgets/newsletter.php:185
msgid "twitter URL:"
msgstr ""

#: inc/widgets/newsletter.php:189
msgid "linkedin URL:"
msgstr ""

#: inc/widgets/newsletter.php:193
msgid "google URL:"
msgstr ""

#: inc/widgets/newsletter.php:197
msgid "flickr URL:"
msgstr ""

#: inc/widgets/newsletter.php:201
msgid "youtube URL:"
msgstr ""

#: inc/widgets/newsletter.php:205
msgid "Instagram URL:"
msgstr ""

#: inc/widgets/newsletter.php:209
msgid "Social link target"
msgstr ""

#: inc/widgets/newsletter.php:211
msgid "_blank"
msgstr ""

#: inc/widgets/newsletter.php:212
msgid "_self"
msgstr ""

#: inc/widgets/newsletter.php:213
msgid "_parent"
msgstr ""

#: inc/widgets/newsletter.php:214
msgid "_top"
msgstr ""

#. Base ID
#: inc/widgets/popular-stores.php:12
msgid "WPCoupon Popular Stores"
msgstr ""

#: inc/widgets/popular-stores.php:13
msgid "Display Popular Stores"
msgstr ""

#: inc/widgets/popular-stores.php:125
msgid "Comma-separated of term ids to include:"
msgstr ""

#: inc/widgets/popular-stores.php:129
msgid "Comma-separated of term ids to exclude:"
msgstr ""

#. Base ID
#: inc/widgets/sidebar.php:12
msgid "WPCoupon Sidebar"
msgstr ""

#: inc/widgets/sidebar.php:14
msgid "Display a Sidebar"
msgstr ""

#: inc/widgets/sidebar.php:39
msgid "Sidebar"
msgstr ""

#. Base ID
#: inc/widgets/slider.php:12
msgid "WPCoupon Slider"
msgstr ""

#: inc/widgets/slider.php:14
msgid "Display a Slider"
msgstr ""

#: inc/widgets/slider.php:85
msgid "Close"
msgstr ""

#: inc/widgets/slider.php:125
msgid "Add item"
msgstr ""

#: inc/woocomerce/woocomerce.php:58
msgctxt "breadcrumb"
msgid "Home"
msgstr ""

#: inc/woocomerce/woocomerce.php:65
msgctxt "breadcrumb"
msgid "Shop"
msgstr ""

#: inc/metabox/includes/CMB2.php:129
msgid "Metabox configuration is required to have an ID parameter"
msgstr ""

#: inc/metabox/includes/CMB2.php:418
msgid "Click to toggle"
msgstr ""

#: inc/metabox/includes/CMB2_Ajax.php:71
msgid "Please Try Again"
msgstr ""

#: inc/metabox/includes/CMB2_Ajax.php:173
msgid "Remove Embed"
msgstr ""

#: inc/metabox/includes/CMB2_Ajax.php:177
#, php-format
msgid "No oEmbed Results Found for %s. View more info at"
msgstr ""

#: inc/metabox/includes/CMB2_Field.php:1186
msgid "Add Group"
msgstr ""

#: inc/metabox/includes/CMB2_Field.php:1187
msgid "Remove Group"
msgstr ""

#: inc/metabox/includes/CMB2_Field.php:1209
#: inc/metabox/includes/CMB2_Field.php:1213
msgid "None"
msgstr ""

#: inc/metabox/includes/CMB2_Field.php:1269
msgid "Sorry, this field does not have a cmb_id specified."
msgstr ""

#: inc/metabox/includes/CMB2_Field_Display.php:408
#: inc/metabox/includes/CMB2_JS.php:139
#: inc/metabox/includes/types/CMB2_Type_File_Base.php:75
msgid "File:"
msgstr ""

#: inc/metabox/includes/CMB2_hookup.php:143
msgid ""
"Term Metadata is a WordPress > 4.4 feature. Please upgrade your WordPress "
"install."
msgstr ""

#: inc/metabox/includes/CMB2_hookup.php:147
msgid "Term metaboxes configuration requires a 'taxonomies' parameter"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:86 inc/metabox/includes/CMB2_JS.php:119
msgid "Clear"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:88
msgid "Select Color"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:89
msgid "Current Color"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:109
msgctxt "Valid formatDate string for jquery-ui datepicker"
msgid "mm/dd/yy"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:110
msgid "Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:111
msgid "Su, Mo, Tu, We, Th, Fr, Sa"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:112
msgid "Sun, Mon, Tue, Wed, Thu, Fri, Sat"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:113
msgid ""
"January, February, March, April, May, June, July, August, September, October,"
" November, December"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:114
msgid "Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:115
msgid "Next"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:116
msgid "Prev"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:117
msgid "Today"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:118 inc/metabox/includes/CMB2_JS.php:128
msgid "Done"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:122
msgid "Choose Time"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:123
msgid "Time"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:124
msgid "Hour"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:125
msgid "Minute"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:126
msgid "Second"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:127
msgid "Now"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:129
msgctxt ""
"Valid formatting string, as per http://trentrichardson."
"com/examples/timepicker/"
msgid "hh:mm TT"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:135
msgid "Use this file"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:136
msgid "Use these files"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:137
#: inc/metabox/includes/types/CMB2_Type_File_Base.php:61
msgid "Remove Image"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:140
#: inc/metabox/includes/types/CMB2_Type_File_Base.php:78
msgid "Download"
msgstr ""

#: inc/metabox/includes/CMB2_JS.php:141
msgid "Select / Deselect All"
msgstr ""

#: inc/metabox/includes/CMB2_Types.php:194
msgid "Add Row"
msgstr ""

#: inc/metabox/includes/helper-functions.php:93
#, php-format
msgid "No oEmbed Results Found for %s. View more info at %s"
msgstr ""

#: inc/metabox/includes/helper-functions.php:279
msgid "Save"
msgstr ""

#: inc/metabox-addons/icon/icon.php:72
msgid "Search Icon"
msgstr ""

#: inc/metabox/includes/types/CMB2_Type_File.php:36
msgid "Add or Upload File"
msgstr ""

#: inc/metabox/includes/types/CMB2_Type_File_List.php:36
msgid "Add or Upload Files"
msgstr ""

#: inc/metabox/includes/types/CMB2_Type_Taxonomy_Multicheck.php:27
#: inc/metabox/includes/types/CMB2_Type_Taxonomy_Radio.php:25
msgid "No terms"
msgstr ""

#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:74
msgid "Slide"
msgstr ""

#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:79
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:182
#, php-format
msgid "New %s"
msgstr ""

#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:132
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:197
msgid "Upload"
msgstr ""

#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:175
#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:232
msgid "Delete"
msgstr ""

#: inc/redux-extensions/slides_v2/slides_v2/field_slides_v2.php:235
#, php-format
msgid "Add %s"
msgstr ""

#. Description of the theme
msgid "An advanded WordPress Coupon and Deal Theme"
msgstr ""

#. URI of the theme
msgid "https://www.famethemes.com/themes/coupon-wp/"
msgstr ""

#. Author of the theme
msgid "famethemes"
msgstr ""

#. Author URI of the theme
msgid "http://www.famethemes.com"
msgstr ""
