# Ag-Coupon Theme Template Tags Reference

This document lists all available template tags and functions from the `inc/core/` directory that can be used in templates without creating new functions.

## 🎫 COUPON FUNCTIONS

### Data Retrieval Functions

#### `wpcoupon_get_coupons($args, $paged, &$max_pages)`
Get coupons with custom arguments
```php
$coupons = wpcoupon_get_coupons(array(
    'posts_per_page' => 8,
    'hide_expired' => true
));
```

#### `wpcoupon_get_ending_soon_coupons($days_left, $posts_per_page, $paged)`
Get coupons ending soon
```php
$ending_coupons = wpcoupon_get_ending_soon_coupons(3, 10, 1);
```

#### `wpcoupon_get_popular_coupons($posts_per_page, $paged)`
Get popular coupons based on usage
```php
$popular_coupons = wpcoupon_get_popular_coupons(10, 1);
```

### Setup Functions

#### `wpcoupon_setup_coupon($coupon, $current_link)`
Setup coupon data for use in templates
```php
wpcoupon_setup_coupon($coupon);
$coupon_obj = wpcoupon_coupon();
```

#### `wpcoupon_coupon($coupon, $current_link)`
Get coupon object (use after setup)
```php
$coupon = wpcoupon_coupon();
```

### Coupon Object Methods (WPCoupon_Coupon)

#### Basic Information
```php
$coupon->post_title          // Coupon title
$coupon->post_excerpt        // Coupon excerpt
$coupon->post_content        // Coupon content
$coupon->ID                  // Coupon ID
```

#### Coupon Type & Code
```php
$coupon->get_type()                    // Get coupon type (code/sale/print)
$coupon->get_coupon_type_text()        // Get type as text
$coupon->get_code()                    // Get coupon code
$coupon->get_code(4)                   // Get last 4 characters of code
```

#### Dates & Expiration
```php
$coupon->get_expires()                 // Get expiration date
$coupon->get_expires('Y-m-d')          // Custom date format
$coupon->has_expired()                 // Check if expired
```

#### Store Information
```php
$coupon->get_store_id()                // Get store ID
$coupon->get_store_url()               // Get store page URL
$coupon->get_store_site_url()          // Get store website URL
$coupon->store                         // Store object
```

#### URLs & Links
```php
$coupon->get_href()                    // Get coupon permalink
$coupon->get_destination_url()         // Get destination URL
$coupon->get_go_out_url()              // Get tracking URL
$coupon->get_share_url()               // Get share URL
```

#### Images & Thumbnails
```php
$coupon->has_thumb()                   // Check if has thumbnail
$coupon->get_thumb('medium')           // Get thumbnail HTML
$coupon->get_thumb('medium', false, true) // Get thumbnail URL only
$coupon->get_store_thumb()             // Get store thumbnail
$coupon->get_print_image()             // Get print coupon image
```

#### Statistics & Tracking
```php
$coupon->get_total_used()              // Total times used
$coupon->get_used_today()              // Used today count
$coupon->percent_success()             // Success percentage
$coupon->is_exclusive()                // Check if exclusive
```

#### Content & Excerpt
```php
$coupon->get_excerpt(20)               // Get excerpt with word limit
$coupon->get_categories()              // Get coupon categories
$coupon->get_categories('ids')         // Get category IDs only
```

### Standalone Coupon Functions

#### `wpcoupon_get_coupon_expires($coupon_id)`
```php
$expires = wpcoupon_get_coupon_expires(123);
```

#### `wpcoupon_get_coupon_type($coupon_id)`
```php
$type = wpcoupon_get_coupon_type(123);
```

#### `wpcoupon_get_coupon_code($coupon_id)`
```php
$code = wpcoupon_get_coupon_code(123);
```

## 🏪 STORE FUNCTIONS

### Data Retrieval Functions

#### `wpcoupon_get_stores($args)`
Get stores with custom arguments
```php
$stores = wpcoupon_get_stores(array(
    'number' => 16,
    'orderby' => 'count',
    'order' => 'DESC'
));
```

#### `wpcoupon_get_featured_stores($number)`
Get featured stores
```php
$featured_stores = wpcoupon_get_featured_stores(12);
```

#### `wpcoupon_get_store_coupons($store_id, $number, $paged, $type)`
Get coupons for specific store
```php
$store_coupons = wpcoupon_get_store_coupons(123, 20, 1, 'active');
```

### Setup Functions

#### `wpcoupon_setup_store($store)`
Setup store data for use in templates
```php
wpcoupon_setup_store($store);
$store_obj = wpcoupon_store();
```

#### `wpcoupon_store($store)`
Get store object (use after setup)
```php
$store = wpcoupon_store();
```

### Store Object Methods (WPCoupon_Store)

#### Basic Information
```php
$store->term_id                        // Store ID
$store->name                           // Store name
$store->slug                           // Store slug
$store->description                    // Store description
$store->count                          // Coupon count
```

#### URLs & Links
```php
$store->get_url()                      // Get store page URL
$store->get_home_url()                 // Get store home URL
$store->get_website_url()              // Get store website URL
$store->get_display_name()             // Get display name
$store->get_single_store_name()        // Get single page name
```

#### Images & Content
```php
$store->has_thumbnail()                // Check if has thumbnail
$store->get_thumbnail('medium')        // Get thumbnail HTML
$store->get_thumbnail('medium', true)  // Get thumbnail URL only
$store->get_content()                  // Get store content
$store->get_extra_info()               // Get extra info
```

#### Status & Features
```php
$store->is_store()                     // Check if valid store
$store->is_featured()                  // Check if featured
$store->count_coupon()                 // Count coupons by type
```

## 🔧 HELPER FUNCTIONS

### General Helpers

#### `wpcoupon_get_paged()`
Get current page number
```php
$paged = wpcoupon_get_paged();
```

### Search Functions

#### `wpcoupon_get_stores_search($args)`
Get stores for search results
```php
$search_stores = wpcoupon_get_stores_search(array(
    'name__like' => 'amazon',
    'number' => 8
));
```

## 📊 AJAX FUNCTIONS

### AJAX Data Functions

#### `wpcoupon_ajax_coupons($doing)`
Get AJAX coupon data
```php
$ajax_data = wpcoupon_ajax_coupons('load_popular_coupons');
```

#### `wpcoupon_ajax_store_coupons()`
Get AJAX store coupon data
```php
$store_ajax_data = wpcoupon_ajax_store_coupons();
```

## 🎨 USAGE EXAMPLES

### Complete Coupon Loop
```php
$coupons = wpcoupon_get_coupons(array('posts_per_page' => 8));
foreach ($coupons as $coupon) {
    wpcoupon_setup_coupon($coupon);
    $coupon_obj = wpcoupon_coupon();
    $store = wpcoupon_store();
    
    echo '<div class="coupon-card">';
    echo '<h3>' . esc_html($coupon_obj->post_title) . '</h3>';
    echo '<p>' . esc_html($store->name) . '</p>';
    echo '<span>' . esc_html($coupon_obj->get_code()) . '</span>';
    echo '<a href="' . esc_url($coupon_obj->get_href()) . '">View Coupon</a>';
    echo '</div>';
}
```

### Complete Store Loop
```php
$stores = wpcoupon_get_featured_stores(12);
foreach ($stores as $store) {
    wpcoupon_setup_store($store);
    $store_obj = wpcoupon_store();
    
    echo '<div class="store-card">';
    echo $store_obj->get_thumbnail('medium');
    echo '<h3>' . esc_html($store_obj->name) . '</h3>';
    echo '<p>' . sprintf(__('%d coupons', 'wp-coupon'), $store_obj->count) . '</p>';
    echo '<a href="' . esc_url($store_obj->get_url()) . '">View Store</a>';
    echo '</div>';
}
```

## 🚨 IMPORTANT NOTES

1. **Always use setup functions** before accessing object methods
2. **Use proper escaping** for all output (esc_html, esc_url, etc.)
3. **Check if objects exist** before using methods
4. **Use proper WordPress hooks** for performance
5. **Follow theme coding standards** for consistency

## 🔗 RELATED FILES

- `inc/core/coupon.php` - Coupon functions and class
- `inc/core/store.php` - Store functions and class  
- `inc/core/helper.php` - Helper functions
- `inc/core/ajax.php` - AJAX functions
- `inc/template-tags.php` - Additional template tags
