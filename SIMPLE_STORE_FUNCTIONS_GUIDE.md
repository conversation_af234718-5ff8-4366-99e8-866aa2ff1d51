# Simple Store Functions - Quick Guide

## Overview

Simplified store system with clean functions for displaying featured stores, all stores, and custom store selections. Includes beautiful slider functionality using Splide with full RTL/LTR support.

## Core Functions

### 1. Get Stores Functions

```php
// Get featured stores
$featured_stores = get_featured_stores(12);

// Get all stores (non-featured)
$all_stores = get_all_stores(12);

// Get all stores excluding specific IDs
$popular_stores = get_all_stores(12, array(1, 2, 3)); // Exclude store IDs 1, 2, 3

// Get stores by specific IDs
$custom_stores = get_stores_by_ids(array(1, 5, 10, 15));
```

### 2. Render Functions

```php
// Render single store card
render_store_card($store, 'featured');  // Styles: featured, slider, grid, minimal

// Render stores as slider (for featured stores)
render_featured_stores_slider($stores, array(
    'slides_per_view' => 4,
    'autoplay' => true,
    'loop' => true
));

// Render stores as grid
render_stores_grid($stores, 'grid', array(
    'columns' => 4,
    'gap' => 6
));
```

### 3. Shortcode Usage

```php
// Featured stores slider
[stores type="featured" display="slider" slides="4" autoplay="true"]

// Featured stores grid
[stores type="featured" display="grid" columns="4"]

// All stores grid
[stores type="all" number="12" display="grid" columns="3"]

// Specific stores by IDs
[stores type="ids" ids="1,2,3,4,5" display="grid"]

// All stores excluding specific IDs
[stores type="all" exclude="1,2,3" number="8"]
```

## Practical Examples

### Example 1: Featured Stores Slider

```php
<?php
// Get featured stores
$featured_stores = get_featured_stores(12);

if (!empty($featured_stores)) {
    // Render as slider
    render_featured_stores_slider($featured_stores, array(
        'slides_per_view' => 4,
        'slides_per_view_mobile' => 1,
        'slides_per_view_tablet' => 2,
        'autoplay' => true,
        'autoplay_delay' => 4000,
        'loop' => true,
        'pagination' => true,
        'navigation' => true
    ));
}
?>
```

### Example 2: Popular Stores Grid (Excluding Featured)

```php
<?php
// Get featured stores first
$featured_stores = get_featured_stores(8);

// Extract featured store IDs
$featured_ids = array();
foreach ($featured_stores as $store) {
    $featured_ids[] = $store->term_id;
}

// Get popular stores excluding featured ones
$popular_stores = get_all_stores(12, $featured_ids);

if (!empty($popular_stores)) {
    // Render as grid
    render_stores_grid($popular_stores, 'grid', array(
        'columns' => 4,
        'columns_tablet' => 2,
        'columns_mobile' => 1,
        'gap' => 6
    ));
}
?>
```

### Example 3: Custom Store Selection

```php
<?php
// Display specific stores by IDs
$custom_store_ids = array(1, 5, 10, 15, 20);
$custom_stores = get_stores_by_ids($custom_store_ids);

if (!empty($custom_stores)) {
    echo '<div class="custom-stores-section">';
    echo '<h2>متاجر مختارة</h2>';

    foreach ($custom_stores as $store) {
        render_store_card($store, 'featured');
    }

    echo '</div>';
}
?>
```

### Example 4: Mixed Layout

```php
<?php
// Section 1: Featured stores as slider
$featured_stores = get_featured_stores(8);
if (!empty($featured_stores)) {
    echo '<section class="featured-section">';
    echo '<h2>المتاجر المميزة</h2>';
    render_featured_stores_slider($featured_stores);
    echo '</section>';
}

// Section 2: Popular stores as grid (excluding featured)
$featured_ids = array();
foreach ($featured_stores as $store) {
    $featured_ids[] = $store->term_id;
}

$popular_stores = get_all_stores(12, $featured_ids);
if (!empty($popular_stores)) {
    echo '<section class="popular-section">';
    echo '<h2>المتاجر الشائعة</h2>';
    render_stores_grid($popular_stores, 'grid');
    echo '</section>';
}
?>
```

## Shortcode Examples

### Basic Usage

```html
<!-- Featured stores slider -->
[stores type="featured" display="slider"]

<!-- All stores grid -->
[stores type="all" display="grid"]

<!-- Specific stores -->
[stores type="ids" ids="1,2,3,4,5"]
```

### Advanced Configuration

```html
<!-- Custom slider settings -->
[stores type="featured" display="slider" slides="3" autoplay="false" loop="false"]

<!-- Custom grid settings -->
[stores type="all" display="grid" columns="3" number="9"]

<!-- Exclude specific stores -->
[stores type="all" exclude="1,2,3" number="12" display="grid" columns="4"]
```

### Responsive Settings

```html
<!-- Responsive slider -->
[stores type="featured" display="slider" slides="4" gap="24"]

<!-- Responsive grid -->
[stores type="all" display="grid" columns="4" gap="6"]
```

## Configuration Options

### Slider Options

```php
$slider_args = array(
    'slides_per_view' => 4,           // Desktop slides
    'slides_per_view_mobile' => 1,    // Mobile slides
    'slides_per_view_tablet' => 2,    // Tablet slides
    'gap' => 24,                      // Gap between slides (px)
    'autoplay' => true,               // Enable autoplay
    'autoplay_delay' => 4000,         // Autoplay delay (ms)
    'loop' => true,                   // Enable loop
    'pagination' => true,             // Show dots
    'navigation' => true              // Show arrows
);
```

### RTL/LTR Support

The slider automatically detects the current language direction and adjusts:

- **RTL Languages (Arabic)**: Slides move right-to-left, arrows are flipped
- **LTR Languages (English)**: Slides move left-to-right, standard direction
- **Automatic Detection**: Uses WordPress `is_rtl()` function
- **Proper Accessibility**: ARIA labels in correct language
- **CSS Direction**: Proper directional styling applied

### Grid Options

```php
$grid_args = array(
    'columns' => 4,                   // Desktop columns
    'columns_tablet' => 2,            // Tablet columns
    'columns_mobile' => 1,            // Mobile columns
    'gap' => 6,                       // Gap between items (Tailwind scale)
    'container_class' => 'my-grid'    // Custom container class
);
```

### Card Styles

```php
// Available card styles
'featured'  // Featured store card with badge
'slider'    // Optimized for slider display
'grid'      // Standard grid card
'minimal'   // Minimal design
```

## Template Integration

### In Template Files

```php
// In your template file
<?php
$stores = get_featured_stores(8);
render_featured_stores_slider($stores);
?>
```

### In Widgets

```php
class Custom_Stores_Widget extends WP_Widget {
    public function widget($args, $instance) {
        $stores = get_featured_stores(5);

        echo $args['before_widget'];
        echo $args['before_title'] . 'متاجر مميزة' . $args['after_title'];

        foreach ($stores as $store) {
            render_store_card($store, 'minimal');
        }

        echo $args['after_widget'];
    }
}
```

### In Custom Functions

```php
function display_homepage_stores() {
    // Featured stores slider
    $featured = get_featured_stores(8);
    if (!empty($featured)) {
        echo '<div class="featured-stores-section">';
        render_featured_stores_slider($featured);
        echo '</div>';
    }

    // Popular stores grid
    $featured_ids = array_map(function($store) {
        return $store->term_id;
    }, $featured);

    $popular = get_all_stores(12, $featured_ids);
    if (!empty($popular)) {
        echo '<div class="popular-stores-section">';
        render_stores_grid($popular, 'grid');
        echo '</div>';
    }
}
```

## Quick Reference

### Function Summary

| Function | Purpose | Parameters |
|----------|---------|------------|
| `get_featured_stores($number)` | Get featured stores | Number of stores |
| `get_all_stores($number, $exclude)` | Get all stores | Number, exclude IDs |
| `get_stores_by_ids($ids)` | Get specific stores | Array of store IDs |
| `render_store_card($store, $style)` | Render single card | Store object, style |
| `render_featured_stores_slider($stores, $args)` | Render slider | Stores array, config |
| `render_stores_grid($stores, $style, $args)` | Render grid | Stores array, style, config |

### Shortcode Parameters

| Parameter | Values | Description |
|-----------|--------|-------------|
| `type` | featured, all, ids | Store selection type |
| `number` | 1-50 | Number of stores |
| `ids` | 1,2,3,4 | Comma-separated IDs |
| `exclude` | 1,2,3 | IDs to exclude |
| `display` | grid, slider | Display type |
| `style` | featured, grid, minimal | Card style |
| `columns` | 1-6 | Grid columns |
| `slides` | 1-8 | Slider slides |
| `autoplay` | true, false | Slider autoplay |
| `loop` | true, false | Slider loop |

### Common Patterns

```php
// Featured only
$stores = get_featured_stores(8);

// Non-featured only
$featured_ids = array_map(function($s) { return $s->term_id; }, get_featured_stores(100));
$stores = get_all_stores(12, $featured_ids);

// Specific stores
$stores = get_stores_by_ids(array(1, 5, 10));

// Slider display
render_featured_stores_slider($stores);

// Grid display
render_stores_grid($stores, 'grid');
```

## Benefits

✅ **Simple API** - Easy to understand and use
✅ **No Duplication Logic** - Clean separation of featured/non-featured
✅ **Flexible Display** - Slider or grid options
✅ **Responsive Design** - Works on all devices
✅ **Shortcode Support** - Easy content management
✅ **Performance Optimized** - Efficient queries
✅ **Modern Slider** - Beautiful Splide integration

This simplified system gives you everything you need without complexity!
