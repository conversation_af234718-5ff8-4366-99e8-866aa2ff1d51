<?php
/**
 * AJAX Test File for Debugging Load More Functionality
 * Place this in the theme root and access via: http://l-couponatt.local/wp-content/themes/Ag-Coupon/ajax-test.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>AJAX Load More Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>AJAX Load More Test</h1>
    
    <div class="test-section">
        <h2>1. WordPress Environment Check</h2>
        <div class="result">
            <strong>WordPress Loaded:</strong> <?php echo defined('ABSPATH') ? 'Yes' : 'No'; ?><br>
            <strong>Theme:</strong> <?php echo get_template(); ?><br>
            <strong>AJAX URL:</strong> <?php echo admin_url('admin-ajax.php'); ?><br>
            <strong>Nonce:</strong> <?php echo wp_create_nonce('wpcoupon_ajax_nonce'); ?><br>
        </div>
    </div>

    <div class="test-section">
        <h2>2. Coupon Count Check</h2>
        <div class="result">
            <?php
            $coupons = get_posts(array(
                'post_type' => 'coupon',
                'posts_per_page' => -1,
                'post_status' => 'publish'
            ));
            echo '<strong>Total Coupons:</strong> ' . count($coupons) . '<br>';
            
            if (count($coupons) > 0) {
                echo '<strong>First 5 Coupon IDs:</strong> ';
                for ($i = 0; $i < min(5, count($coupons)); $i++) {
                    echo $coupons[$i]->ID . ' ';
                }
            }
            ?>
        </div>
    </div>

    <div class="test-section">
        <h2>3. AJAX Function Test</h2>
        <button onclick="testAjaxDirect()">Test AJAX Direct</button>
        <button onclick="testAjaxWithArgs()">Test AJAX with Args</button>
        <div id="ajax-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Load More Button Test</h2>
        <div id="test-coupons-grid" style="border: 1px solid #ddd; padding: 10px; min-height: 100px;">
            <!-- Initial coupons will be loaded here -->
            <?php
            $initial_coupons = get_posts(array(
                'post_type' => 'coupon',
                'posts_per_page' => 2,
                'post_status' => 'publish'
            ));
            
            foreach ($initial_coupons as $coupon) {
                echo '<div style="border: 1px solid #ccc; margin: 5px; padding: 10px;">';
                echo '<strong>' . get_the_title($coupon->ID) . '</strong> (ID: ' . $coupon->ID . ')';
                echo '</div>';
            }
            ?>
        </div>
        
        <button class="load-more-btn" 
                data-doing="load_coupons"
                data-next-page="2"
                data-per-page="2"
                data-args='{"layout":"","posts_per_page":"","num_words":"","hide_expired":""}'>
            Load More Test
        </button>
    </div>

    <script>
        // WordPress AJAX settings
        const ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
        const nonce = '<?php echo wp_create_nonce('wpcoupon_ajax_nonce'); ?>';
        
        function logResult(message, type = 'info') {
            const resultDiv = document.getElementById('ajax-result');
            const className = type === 'success' ? 'success' : (type === 'error' ? 'error' : '');
            resultDiv.innerHTML += `<div class="${className}">${message}</div>`;
            console.log(message);
        }
        
        function testAjaxDirect() {
            logResult('Testing direct AJAX call...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wpcoupon_coupon_ajax',
                    st_doing: 'load_coupons',
                    next_page: 2,
                    _wpnonce: nonce
                },
                success: function(response) {
                    logResult('AJAX Success: ' + JSON.stringify(response), 'success');
                    if (response.success && response.data && response.data.content) {
                        logResult('Content length: ' + response.data.content.length + ' characters', 'success');
                    }
                },
                error: function(xhr, status, error) {
                    logResult('AJAX Error: ' + error + ' - ' + xhr.responseText, 'error');
                }
            });
        }
        
        function testAjaxWithArgs() {
            logResult('Testing AJAX with args...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wpcoupon_coupon_ajax',
                    st_doing: 'load_coupons',
                    next_page: 2,
                    _wpnonce: nonce,
                    args: {
                        layout: '',
                        posts_per_page: '2',
                        num_words: '',
                        hide_expired: ''
                    }
                },
                success: function(response) {
                    logResult('AJAX with Args Success: ' + JSON.stringify(response), 'success');
                    if (response.success && response.data && response.data.content) {
                        logResult('Content preview: ' + response.data.content.substring(0, 200) + '...', 'success');
                        
                        // Try to append to test grid
                        $('#test-coupons-grid').append(response.data.content);
                    }
                },
                error: function(xhr, status, error) {
                    logResult('AJAX with Args Error: ' + error + ' - ' + xhr.responseText, 'error');
                }
            });
        }
        
        // Test load more button click
        $(document).on('click', '.load-more-btn', function(e) {
            e.preventDefault();
            logResult('Load more button clicked!');
            testAjaxWithArgs();
        });
    </script>
</body>
</html>
