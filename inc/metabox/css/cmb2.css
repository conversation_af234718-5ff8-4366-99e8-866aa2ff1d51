/**
 * CMB2 Styling
 */

/*--------------------------------------------------------------
Main Wrap
--------------------------------------------------------------*/

/* line 5, sass/partials/_main_wrap.scss */

.cmb2-wrap {
	margin: 0;
}

/* line 8, sass/partials/_main_wrap.scss */

.cmb2-wrap input,
.cmb2-wrap textarea {
	font-size: 14px;
	max-width: 100%;
	padding: 5px;
}

/* line 18, sass/partials/_main_wrap.scss */

.cmb2-wrap input[type=text].cmb2-oembed {
	width: 100%;
}

/* line 23, sass/partials/_main_wrap.scss */

.cmb2-wrap textarea {
	width: 500px;
}

/* line 26, sass/partials/_main_wrap.scss */

.cmb2-wrap textarea.cmb2-textarea-code {
	font-family: "Courier 10 Pitch", Courier, monospace;
	line-height: 16px;
}

/* line 34, sass/partials/_main_wrap.scss */

.cmb2-wrap input.cmb2-text-small,
.cmb2-wrap input.cmb2-timepicker {
	width: 100px;
}

/* line 40, sass/partials/_main_wrap.scss */

.cmb2-wrap input.cmb2-text-money {
	width: 90px;
}

/* line 45, sass/partials/_main_wrap.scss */

.cmb2-wrap input.cmb2-text-medium {
	width: 230px;
}

/* line 50, sass/partials/_main_wrap.scss */

.cmb2-wrap input.cmb2-upload-file {
	width: 65%;
}

/* line 54, sass/partials/_main_wrap.scss */

.cmb2-wrap input.ed_button {
	padding: 2px 4px;
}

/* line 59, sass/partials/_main_wrap.scss */

.cmb2-wrap input:not([type="hidden"]) + input,
.cmb2-wrap input:not([type="hidden"]) + .button,
.cmb2-wrap input:not([type="hidden"]) + select {
	margin-left: 20px;
}

/* line 67, sass/partials/_main_wrap.scss */

.cmb2-wrap ul {
	margin: 0;
}

/* line 71, sass/partials/_main_wrap.scss */

.cmb2-wrap li {
	font-size: 14px;
	line-height: 16px;
	margin: 1px 0 5px 0;
}

/* line 82, sass/partials/_main_wrap.scss */

.cmb2-wrap select {
	font-size: 14px;
	margin-top: 3px;
}

/* line 87, sass/partials/_main_wrap.scss */

.cmb2-wrap input:focus,
.cmb2-wrap textarea:focus {
	background: #fffff8;
}

/* line 92, sass/partials/_main_wrap.scss */

.cmb2-wrap input[type="radio"] {
	margin: 0 5px 0 0;
	padding: 0;
}

/* line 97, sass/partials/_main_wrap.scss */

.cmb2-wrap input[type="checkbox"] {
	margin: 0 5px 0 0;
	padding: 0;
}

/* line 102, sass/partials/_main_wrap.scss */

.cmb2-wrap button,
.cmb2-wrap .button {
	white-space: nowrap;
}

/* line 107, sass/partials/_main_wrap.scss */

.cmb2-wrap .mceLayout {
	border: 1px solid #e9e9e9 !important;
}

/* line 111, sass/partials/_main_wrap.scss */

.cmb2-wrap .mceIframeContainer {
	background: #fff;
}

/* line 115, sass/partials/_main_wrap.scss */

.cmb2-wrap .meta_mce {
	width: 97%;
}

/* line 118, sass/partials/_main_wrap.scss */

.cmb2-wrap .meta_mce textarea {
	width: 100%;
}

/* line 126, sass/partials/_main_wrap.scss */

.cmb2-wrap .wp-color-result,
.cmb2-wrap .wp-picker-input-wrap {
	vertical-align: middle;
}

/* line 131, sass/partials/_main_wrap.scss */

.cmb2-wrap .wp-color-result,
.cmb2-wrap .wp-picker-container {
	margin: 0 10px 0 0;
}

/* line 136, sass/partials/_main_wrap.scss */

.cmb2-wrap .cmb-row {
	margin: 0;
}

/* line 139, sass/partials/_main_wrap.scss */

.cmb2-wrap .cmb-row:after {
	content: '';
	clear: both;
	display: block;
	width: 100%;
}

/* line 146, sass/partials/_main_wrap.scss */

.cmb2-wrap .cmb-row.cmb-repeat-row {
	padding: 1.8em 0 0;
}

/* line 149, sass/partials/_main_wrap.scss */

.cmb2-wrap .cmb-row.cmb-repeat-row:first-of-type {
	padding: 0;
}

/* line 154, sass/partials/_main_wrap.scss */

.cmb2-wrap .cmb-row.cmb-repeat .cmb2-metabox-description {
	padding-top: 0;
	padding-bottom: 1.8em;
}

/* line 161, sass/partials/_main_wrap.scss */

.cmb2-metabox {
	clear: both;
	margin: 0;
}

/* line 167, sass/partials/_main_wrap.scss */

.cmb2-metabox > .cmb-row:first-of-type > .cmb-td,
.cmb2-metabox > .cmb-row:first-of-type > .cmb-th,
.cmb2-metabox .cmb-field-list > .cmb-row:first-of-type > .cmb-td,
.cmb2-metabox .cmb-field-list > .cmb-row:first-of-type > .cmb-th {
	border: 0;
}

/* line 174, sass/partials/_main_wrap.scss */

.cmb2-metabox > .cmb-row .cmb-repeat-table .cmb-row > .cmb-td {
	padding-right: 20px;
	box-sizing: border-box;
	float: left;
}

/* line 182, sass/partials/_main_wrap.scss */

.cmb-add-row {
	margin: 1.8em 0 0;
}

/* line 186, sass/partials/_main_wrap.scss */

.cmb-nested .cmb-td,
.cmb-repeatable-group .cmb-th,
.cmb-repeatable-group:first-of-type {
	border: 0;
}

/* line 192, sass/partials/_main_wrap.scss */

.cmb-row:last-of-type,
.cmb2-wrap .cmb-row:last-of-type,
.cmb-repeatable-group:last-of-type {
	border-bottom: 0;
}

/* line 198, sass/partials/_main_wrap.scss */

.cmb-repeatable-grouping {
	border: 1px solid #e9e9e9;
	padding: 0 1em;
	max-width: 1000px;
}

/* line 202, sass/partials/_main_wrap.scss */

.cmb-repeatable-grouping.cmb-row {
	margin: 0 0 0.8em;
}

/* line 209, sass/partials/_main_wrap.scss */

.cmb-th {
	color: #222222;
	float: left;
	font-weight: 600;
	line-height: 1.3;
	padding: 20px 10px 20px 0;
	vertical-align: top;
	width: 200px;
}

/* line 223, sass/partials/_main_wrap.scss */

.cmb-td {
	line-height: 1.3;
	max-width: 100%;
	padding: 15px 10px;
	vertical-align: middle;
}

/* line 232, sass/partials/_main_wrap.scss */

.cmb-type-title .cmb-td {
	padding: 0;
}

/* line 237, sass/partials/_main_wrap.scss */

.cmb-th label {
	display: block;
	padding: 5px 0;
}

/* line 242, sass/partials/_main_wrap.scss */

.cmb-th + .cmb-td {
	float: left;
}

/* line 246, sass/partials/_main_wrap.scss */

.cmb-td .cmb-td {
	padding-bottom: 1em;
}

/* line 250, sass/partials/_main_wrap.scss */

.cmb-remove-row {
	text-align: right;
}

/* line 254, sass/partials/_main_wrap.scss */

.empty-row.hidden {
	display: none;
}

/* line 260, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-th {
	padding: 5px;
}

/* line 264, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-group-title {
	background-color: #e9e9e9;
	padding: 8px 12px 8px 2.2em;
	margin: 0 -1em;
	min-height: 1.5em;
	font-size: 14px;
	line-height: 1.4;
}

/* line 272, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-group-title h4 {
	border: 0;
	margin: 0;
	font-size: 1.2em;
	font-weight: 500;
	padding: 0.5em 0.75em;
}

/* line 280, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-group-title .cmb-th {
	display: block;
	width: 100%;
}

/* line 286, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-group-description .cmb-th {
	font-size: 1.2em;
	display: block;
	float: none;
	padding-bottom: 1em;
	text-align: left;
	width: 100%;
}

/* line 27, sass/partials/_mixins.scss */

.cmb-repeatable-group .cmb-group-description .cmb-th label {
	display: block;
	margin-top: 0em;
	padding-bottom: 5px;
}

/* line 32, sass/partials/_mixins.scss */

.cmb-repeatable-group .cmb-group-description .cmb-th label:after {
	border-bottom: 1px solid #e9e9e9;
	content: '';
	clear: both;
	display: block;
	padding-top: .4em;
}

/* line 290, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-shift-rows {
	font-size: 1em;
	margin-right: 1em;
	text-decoration: none;
}

/* line 295, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-shift-rows .dashicons {
	font-size: 1.5em;
	height: 1.5em;
	line-height: 1.2em;
	width: 1em;
}

/* line 301, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb-shift-rows .dashicons.dashicons-arrow-down-alt2 {
	line-height: 1.3em;
}

/* line 308, sass/partials/_main_wrap.scss */

.cmb-repeatable-group .cmb2-upload-button {
	float: right;
}

/* line 314, sass/partials/_main_wrap.scss */

p.cmb2-metabox-description {
	color: #aaaaaa;
	font-style: italic;
	margin: 0;
	padding-top: .5em;
}

/* line 321, sass/partials/_main_wrap.scss */

span.cmb2-metabox-description {
	color: #aaaaaa;
	font-style: italic;
}

/* line 326, sass/partials/_main_wrap.scss */

.cmb2-metabox-title {
	margin: 0 0 5px 0;
	padding: 5px 0 0 0;
	font-size: 14px;
}

/* line 332, sass/partials/_main_wrap.scss */

.cmb-inline ul {
	padding: 4px 0 0 0;
}

/* line 336, sass/partials/_main_wrap.scss */

.cmb-inline li {
	display: inline-block;
	padding-right: 18px;
}

/* line 341, sass/partials/_main_wrap.scss */

.cmb-type-textarea-code pre {
	margin: 0;
}

/* line 347, sass/partials/_main_wrap.scss */

.cmb2-media-status .img-status {
	clear: none;
	display: inline-block;
	float: left;
	margin-right: 10px;
	width: auto;
}

/* line 354, sass/partials/_main_wrap.scss */

.cmb2-media-status .img-status img {
	max-width: 350px;
}

/* line 359, sass/partials/_main_wrap.scss */

.cmb2-media-status .img-status img,
.cmb2-media-status .embed-status {
	background: #ffffff;
	border: 1px solid #e9e9e9;
	border-radius: 2px;
	-moz-border-radius: 2px;
	margin: 15px 0 0 0;
	padding: 5px;
}

/* line 369, sass/partials/_main_wrap.scss */

.cmb2-media-status .embed-status {
	float: left;
	max-width: 800px;
}

/* line 374, sass/partials/_main_wrap.scss */

.cmb2-media-status .img-status,
.cmb2-media-status .embed-status {
	position: relative;
}

/* line 377, sass/partials/_main_wrap.scss */

.cmb2-media-status .img-status .cmb2-remove-file-button,
.cmb2-media-status .embed-status .cmb2-remove-file-button {
	background: url(../images/ico-delete.png);
	height: 16px;
	left: -5px;
	position: absolute;
	text-indent: -9999px;
	top: -5px;
	width: 16px;
}

/* line 391, sass/partials/_main_wrap.scss */

.cmb2-media-status .img-status .cmb2-remove-file-button {
	top: 10px;
}

/* line 396, sass/partials/_main_wrap.scss */

.cmb2-media-status .img-status img,
.cmb2-media-status .file-status > span {
	cursor: pointer;
}

/* line 402, sass/partials/_main_wrap.scss */

.cmb-type-file-list .cmb2-media-status .img-status {
	clear: none;
	float: left;
	margin-right: 10px;
	width: auto;
}

/* line 409, sass/partials/_main_wrap.scss */

.cmb-attach-list li {
	clear: both;
	display: inline-block;
	margin-bottom: 25px;
	width: 100%;
}

/* line 415, sass/partials/_main_wrap.scss */

.cmb-attach-list li img {
	cursor: move;
	float: left;
	margin-right: 10px;
}

/* line 422, sass/partials/_main_wrap.scss */

.cmb2-remove-wrapper {
	margin: 0;
}

/* line 426, sass/partials/_main_wrap.scss */

.child-cmb2 .cmb-th {
	text-align: left;
}

/*--------------------------------------------------------------
Post Metaboxes
--------------------------------------------------------------*/

/* line 4, sass/partials/_post_metaboxes.scss */

#poststuff .cmb-group-title {
	margin-left: -1em;
	margin-right: -1em;
	min-height: 1.5em;
}

/* line 10, sass/partials/_post_metaboxes.scss */

#poststuff .repeatable .cmb-group-title {
	padding-left: 2.2em;
}

/* line 16, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-wrap,
.cmb-type-group .cmb2-wrap {
	margin: 0;
}

/* line 19, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-wrap > .cmb-field-list > .cmb-row,
.cmb-type-group .cmb2-wrap > .cmb-field-list > .cmb-row {
	padding: 1.8em 0;
}

/* line 25, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-wrap input[type=text].cmb2-oembed,
.cmb-type-group .cmb2-wrap input[type=text].cmb2-oembed {
	width: 100%;
}

/* line 31, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-row,
.cmb-type-group .cmb-row {
	padding: 0 0 1.8em;
	margin: 0 0 0.8em;
}

/* line 35, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-row .cmbhandle,
.cmb-type-group .cmb-row .cmbhandle {
	right: -1em;
	position: relative;
}

/* line 41, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-repeatable-grouping,
.cmb-type-group .cmb-repeatable-grouping {
	padding: 0 1em;
	max-width: 100%;
	min-width: 1px !important;
}

/* line 47, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-repeatable-group > .cmb-row,
.cmb-type-group .cmb-repeatable-group > .cmb-row {
	padding-bottom: 0;
}

/* line 51, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-th,
.cmb-type-group .cmb-th {
	width: 18%;
	padding: 0 2% 0 0;
}

/* line 57, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-td,
.cmb-type-group .cmb-td {
	margin-bottom: 0;
	padding: 0;
	line-height: 1.3;
}

/* line 63, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-repeat-row .cmb-td,
.cmb-type-group .cmb-repeat-row .cmb-td {
	padding-bottom: 1.8em;
}

/* line 67, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-th + .cmb-td,
.cmb-type-group .cmb-th + .cmb-td {
	width: 80%;
	float: right;
}

/* line 72, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-row:not(:last-of-type),
.postbox-container .cmb-repeatable-group:not(:last-of-type),
.cmb-type-group .cmb-row:not(:last-of-type),
.cmb-type-group .cmb-repeatable-group:not(:last-of-type) {
	border-bottom: 1px solid #e9e9e9;
}

/* line 81, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-repeat-group-field,
.postbox-container .cmb-remove-field-row,
.cmb-type-group .cmb-repeat-group-field,
.cmb-type-group .cmb-remove-field-row {
	padding-top: 1.8em;
}

/* line 88, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody,
.postbox-container .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody,
.cmb-type-group .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody,
.cmb-type-group .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody {
	display: table;
	width: 100%;
}

/* line 92, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody input.regular-text,
.postbox-container .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody input.regular-text,
.cmb-type-group .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody input.regular-text,
.cmb-type-group .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody input.regular-text {
	width: 100%;
}

/* line 96, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(.hidden):not(.empty-row),
.postbox-container .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(.hidden):not(.empty-row),
.cmb-type-group .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(.hidden):not(.empty-row),
.cmb-type-group .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(.hidden):not(.empty-row) {
	display: table-row;
}

/* line 100, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td,
.postbox-container .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td,
.cmb-type-group .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td,
.cmb-type-group .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td {
	display: table-cell;
	float: none;
	width: 100%;
}

/* line 106, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(:first-of-type) .cmb-td,
.postbox-container .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(:first-of-type) .cmb-td,
.cmb-type-group .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(:first-of-type) .cmb-td,
.cmb-type-group .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-row:not(:first-of-type) .cmb-td {
	padding-top: 1.8em;
}

/* line 110, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td.cmb-remove-row,
.postbox-container .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td.cmb-remove-row,
.cmb-type-group .cmb2-metabox > .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td.cmb-remove-row,
.cmb-type-group .cmb2-metabox > .cmb-row .cmb-row.table-layout .cmb-repeat-table .cmb-tbody .cmb-td.cmb-remove-row {
	padding-right: 0;
}

/*--------------------------------------------------------------
Misc.
--------------------------------------------------------------*/

/* line 5, sass/partials/_misc.scss */

#poststuff .cmb-repeatable-group h2 {
	margin: 0;
}

/* line 12, sass/partials/_misc.scss */

.edit-tags-php .cmb2-metabox-title,
.profile-php .cmb2-metabox-title,
.user-edit-php .cmb2-metabox-title {
	font-size: 1.4em;
}

/* line 18, sass/partials/_misc.scss */

.cmb2-options-page .cmb2-metabox-title {
	font-size: 1.3em;
	margin: 1em 0;
}

/* line 21, sass/partials/_misc.scss */

.cmb2-options-page .cmb2-metabox-title + p.cmb2-metabox-description {
	margin-top: -1.6em;
	margin-bottom: .8em;
}

/* line 27, sass/partials/_misc.scss */

.postbox .cmb-spinner {
	float: left;
}

/*--------------------------------------------------------------
Sidebar Placement Adjustments
--------------------------------------------------------------*/

/* line 10, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-wrap > .cmb-field-list > .cmb-row,
#side-sortables .cmb2-wrap > .cmb-field-list > .cmb-row {
	padding: 1.4em 0;
}

/* line 16, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-wrap input[type=text]:not(.wp-color-picker),
#side-sortables .cmb2-wrap input[type=text]:not(.wp-color-picker) {
	width: 100%;
}

/* line 20, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-wrap input + input:not(.wp-picker-clear),
.inner-sidebar .cmb2-wrap input + select,
#side-sortables .cmb2-wrap input + input:not(.wp-picker-clear),
#side-sortables .cmb2-wrap input + select {
	margin-left: 0;
	margin-top: 1em;
	display: block;
}

/* line 26, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-wrap input.cmb2-text-money,
#side-sortables .cmb2-wrap input.cmb2-text-money {
	max-width: 70%;
}

/* line 28, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-wrap input.cmb2-text-money + .cmb2-metabox-description,
#side-sortables .cmb2-wrap input.cmb2-text-money + .cmb2-metabox-description {
	display: block;
}

/* line 34, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-wrap label,
#side-sortables .cmb2-wrap label {
	display: block;
	font-weight: 700;
	padding: 0 0 5px;
}

/* line 42, sass/partials/_sidebar_placements.scss */

.inner-sidebar textarea,
#side-sortables textarea {
	max-width: 99%;
}

/* line 46, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-repeatable-group,
#side-sortables .cmb-repeatable-group {
	border-bottom: 1px solid #e9e9e9;
}

/* line 50, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-type-group > .cmb-td > .cmb-repeatable-group,
#side-sortables .cmb-type-group > .cmb-td > .cmb-repeatable-group {
	border-bottom: 0;
	margin-bottom: -1.4em;
}

/* line 55, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-th,
.inner-sidebar .cmb-td,
.inner-sidebar .cmb-th + .cmb-td,
#side-sortables .cmb-th,
#side-sortables .cmb-td,
#side-sortables .cmb-th + .cmb-td {
	width: 100%;
	display: block;
	float: none;
}

/* line 63, sass/partials/_sidebar_placements.scss */

.inner-sidebar .closed .inside,
#side-sortables .closed .inside {
	display: none;
}

/* line 67, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-td .cmb-td,
#side-sortables .cmb-td .cmb-td {
	padding-bottom: 1em;
}

/* line 71, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-th,
#side-sortables .cmb-th {
	display: block;
	float: none;
	padding-bottom: 1em;
	text-align: left;
	width: 100%;
	padding-left: 0;
	padding-right: 0;
}

/* line 27, sass/partials/_mixins.scss */

.inner-sidebar .cmb-th label,
#side-sortables .cmb-th label {
	display: block;
	margin-top: 0em;
	padding-bottom: 5px;
}

/* line 32, sass/partials/_mixins.scss */

.inner-sidebar .cmb-th label:after,
#side-sortables .cmb-th label:after {
	border-bottom: 1px solid #e9e9e9;
	content: '';
	clear: both;
	display: block;
	padding-top: .4em;
}

/* line 14, sass/partials/_mixins.scss */

.inner-sidebar .cmb-th label,
#side-sortables .cmb-th label {
	font-size: 14px;
	line-height: 1.4em;
}

/* line 78, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-group-description .cmb-th,
#side-sortables .cmb-group-description .cmb-th {
	padding-top: 0;
}

/* line 81, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-group-description .cmb2-metabox-description,
#side-sortables .cmb-group-description .cmb2-metabox-description {
	padding: 0;
}

/* line 88, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-group-title .cmb-th,
#side-sortables .cmb-group-title .cmb-th {
	padding: 0;
}

/* line 94, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-repeatable-grouping + .cmb-repeatable-grouping,
#side-sortables .cmb-repeatable-grouping + .cmb-repeatable-grouping {
	margin-top: 1em;
}

/* line 103, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-media-status .img-status img,
.inner-sidebar .cmb2-media-status .embed-status img,
#side-sortables .cmb2-media-status .img-status img,
#side-sortables .cmb2-media-status .embed-status img {
	max-width: 90%;
	height: auto;
}

/* line 111, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-list label,
#side-sortables .cmb2-list label {
	display: inline;
	font-weight: normal;
}

/* line 116, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-metabox-description,
#side-sortables .cmb2-metabox-description {
	display: block;
	padding: 7px 0 0;
}

/* line 123, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-type-checkbox .cmb-td label,
.inner-sidebar .cmb-type-checkbox .cmb2-metabox-description,
#side-sortables .cmb-type-checkbox .cmb-td label,
#side-sortables .cmb-type-checkbox .cmb2-metabox-description {
	font-weight: normal;
	display: inline;
}

/* line 130, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-row .cmb2-metabox-description,
#side-sortables .cmb-row .cmb2-metabox-description {
	padding-bottom: 1.8em;
}

/* line 134, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-metabox-title,
#side-sortables .cmb2-metabox-title {
	font-size: 1.2em;
	font-style: italic;
}

/* line 139, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-remove-row,
#side-sortables .cmb-remove-row {
	clear: both;
	padding-top: 12px;
	padding-bottom: 0;
}

/* line 146, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-type-colorpicker .cmb-repeat-row .cmb-td,
#side-sortables .cmb-type-colorpicker .cmb-repeat-row .cmb-td {
	width: auto;
	clear: none;
	float: left;
	padding-top: 0;
}

/* line 151, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb-type-colorpicker .cmb-repeat-row .cmb-td.cmb-remove-row,
#side-sortables .cmb-type-colorpicker .cmb-repeat-row .cmb-td.cmb-remove-row {
	float: right;
	margin: 0;
}

/* line 158, sass/partials/_sidebar_placements.scss */

.inner-sidebar .cmb2-upload-button,
#side-sortables .cmb2-upload-button {
	clear: both;
	margin-top: 12px;
}

/* line 2, sass/partials/_collapsible_ui.scss */

.cmb2-metabox .cmb-type-group {
	max-width: 1000px;
}

/* line 5, sass/partials/_collapsible_ui.scss */

.cmb2-metabox .cmbhandle {
	color: #aaa;
	float: right;
	width: 27px;
	height: 30px;
	cursor: pointer;
	right: -1em;
	position: relative;
}

/* line 13, sass/partials/_collapsible_ui.scss */

.cmb2-metabox .cmbhandle:before {
	content: '\f142';
	right: 12px;
	font: normal 20px/1 'dashicons';
	speak: none;
	display: inline-block;
	padding: 8px 10px;
	top: 0;
	position: relative;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none !important;
}

/* line 30, sass/partials/_collapsible_ui.scss */

.cmb2-metabox .postbox.closed .cmbhandle:before {
	content: '\f140';
}

/* line 36, sass/partials/_collapsible_ui.scss */

.cmb2-metabox button.dashicons-before.dashicons-no-alt.cmb-remove-group-row {
	-webkit-appearance: none !important;
	background: none !important;
	border: none !important;
	position: absolute;
	left: 0;
	top: .5em;
	line-height: 1em;
	cursor: pointer;
	padding: 2px 6px 3px;
}

/* line 46, sass/partials/_collapsible_ui.scss */

.cmb2-metabox button.dashicons-before.dashicons-no-alt.cmb-remove-group-row:not([disabled]) {
	color: #a00;
}

/* line 48, sass/partials/_collapsible_ui.scss */

.cmb2-metabox button.dashicons-before.dashicons-no-alt.cmb-remove-group-row:not([disabled]):hover {
	color: #f00;
}

/*
 * jQuery UI CSS Framework 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 *
 * WordPress Styles adopted from "jQuery UI Datepicker CSS for WordPress"
 * https://github.com/stuttter/wp-datepicker-styling
 *
 */

/* line 15, sass/partials/_jquery_ui.scss */

* html .cmb2-element.ui-helper-clearfix {
	height: 1%;
}

/* line 24, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker,
.cmb2-element .ui-datepicker {
	padding: 0;
	margin: 0;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	background-color: #fff;
	border: 1px solid #dfdfdf;
	border-top: none;
	-webkit-box-shadow: 0 3px 6px rgba(0, 0, 0, 0.075);
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.075);
	min-width: 17em;
	width: auto; /* Default Color Scheme */
}

/* line 38, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker *,
.cmb2-element .ui-datepicker * {
	padding: 0;
	font-family: "Open Sans", sans-serif;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}

/* line 46, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker table,
.cmb2-element .ui-datepicker table {
	font-size: 13px;
	margin: 0;
	border: none;
	border-collapse: collapse;
}

/* line 53, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-widget-header,
.cmb2-element.ui-datepicker .ui-datepicker-header,
.cmb2-element .ui-datepicker .ui-widget-header,
.cmb2-element .ui-datepicker .ui-datepicker-header {
	background-image: none;
	border: none;
	color: #fff;
	font-weight: normal;
}

/* line 61, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-header .ui-state-hover,
.cmb2-element .ui-datepicker .ui-datepicker-header .ui-state-hover {
	background: transparent;
	border-color: transparent;
	cursor: pointer;
}

/* line 67, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-title,
.cmb2-element .ui-datepicker .ui-datepicker-title {
	margin: 0;
	padding: 10px 0;
	color: #fff;
	font-size: 14px;
	line-height: 14px;
	text-align: center;
}

/* line 75, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-title select,
.cmb2-element .ui-datepicker .ui-datepicker-title select {
	margin-top: -8px;
	margin-bottom: -8px;
}

/* line 81, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-prev,
.cmb2-element.ui-datepicker .ui-datepicker-next,
.cmb2-element .ui-datepicker .ui-datepicker-prev,
.cmb2-element .ui-datepicker .ui-datepicker-next {
	position: relative;
	top: 0;
	height: 34px;
	width: 34px;
}

/* line 89, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-state-hover.ui-datepicker-prev,
.cmb2-element.ui-datepicker .ui-state-hover.ui-datepicker-next,
.cmb2-element .ui-datepicker .ui-state-hover.ui-datepicker-prev,
.cmb2-element .ui-datepicker .ui-state-hover.ui-datepicker-next {
	border: none;
}

/* line 94, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-prev,
.cmb2-element.ui-datepicker .ui-datepicker-prev-hover,
.cmb2-element .ui-datepicker .ui-datepicker-prev,
.cmb2-element .ui-datepicker .ui-datepicker-prev-hover {
	left: 0;
}

/* line 99, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-next,
.cmb2-element.ui-datepicker .ui-datepicker-next-hover,
.cmb2-element .ui-datepicker .ui-datepicker-next,
.cmb2-element .ui-datepicker .ui-datepicker-next-hover {
	right: 0;
}

/* line 104, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-next span,
.cmb2-element.ui-datepicker .ui-datepicker-prev span,
.cmb2-element .ui-datepicker .ui-datepicker-next span,
.cmb2-element .ui-datepicker .ui-datepicker-prev span {
	display: none;
}

/* line 109, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-prev,
.cmb2-element .ui-datepicker .ui-datepicker-prev {
	float: left;
}

/* line 113, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-next,
.cmb2-element .ui-datepicker .ui-datepicker-next {
	float: right;
}

/* line 117, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-prev:before,
.cmb2-element.ui-datepicker .ui-datepicker-next:before,
.cmb2-element .ui-datepicker .ui-datepicker-prev:before,
.cmb2-element .ui-datepicker .ui-datepicker-next:before {
	font: normal 20px/34px 'dashicons';
	padding-left: 7px;
	color: #fff;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	width: 34px;
	height: 34px;
}

/* line 129, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-prev:before,
.cmb2-element .ui-datepicker .ui-datepicker-prev:before {
	content: '\f341';
}

/* line 133, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-next:before,
.cmb2-element .ui-datepicker .ui-datepicker-next:before {
	content: '\f345';
}

/* line 137, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-datepicker-prev-hover:before,
.cmb2-element.ui-datepicker .ui-datepicker-next-hover:before,
.cmb2-element .ui-datepicker .ui-datepicker-prev-hover:before,
.cmb2-element .ui-datepicker .ui-datepicker-next-hover:before {
	opacity: 0.7;
}

/* line 142, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker select.ui-datepicker-month,
.cmb2-element.ui-datepicker select.ui-datepicker-year,
.cmb2-element .ui-datepicker select.ui-datepicker-month,
.cmb2-element .ui-datepicker select.ui-datepicker-year {
	width: 33%;
	background: transparent;
	border-color: transparent;
	box-shadow: none;
	color: #fff;
}

/* line 151, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker thead,
.cmb2-element .ui-datepicker thead {
	color: #fff;
	font-weight: 600;
}

/* line 154, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker thead th,
.cmb2-element .ui-datepicker thead th {
	font-weight: normal;
}

/* line 159, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker th,
.cmb2-element .ui-datepicker th {
	padding: 10px;
}

/* line 163, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td,
.cmb2-element .ui-datepicker td {
	padding: 0;
	border: 1px solid #f4f4f4;
}

/* line 168, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td.ui-datepicker-other-month,
.cmb2-element .ui-datepicker td.ui-datepicker-other-month {
	border: transparent;
}

/* line 172, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td.ui-datepicker-week-end,
.cmb2-element .ui-datepicker td.ui-datepicker-week-end {
	background-color: #f4f4f4;
	border: 1px solid #f4f4f4;
}

/* line 175, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td.ui-datepicker-week-end.ui-datepicker-today,
.cmb2-element .ui-datepicker td.ui-datepicker-week-end.ui-datepicker-today {
	-webkit-box-shadow: inset 0px 0px 1px 0px rgba(0, 0, 0, 0.1);
	-moz-box-shadow: inset 0px 0px 1px 0px rgba(0, 0, 0, 0.1);
	box-shadow: inset 0px 0px 1px 0px rgba(0, 0, 0, 0.1);
}

/* line 182, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td.ui-datepicker-today,
.cmb2-element .ui-datepicker td.ui-datepicker-today {
	background-color: #f0f0c0;
}

/* line 186, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td.ui-datepicker-current-day,
.cmb2-element .ui-datepicker td.ui-datepicker-current-day {
	background: #bbdd88;
}

/* line 190, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td .ui-state-default,
.cmb2-element .ui-datepicker td .ui-state-default {
	background: transparent;
	border: none;
	text-align: center;
	text-decoration: none;
	width: auto;
	display: block;
	padding: 5px 10px;
	font-weight: normal;
	color: #444;
}

/* line 202, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td.ui-state-disabled .ui-state-default,
.cmb2-element .ui-datepicker td.ui-state-disabled .ui-state-default {
	opacity: 0.5;
}

/* line 207, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-widget-header,
.cmb2-element.ui-datepicker .ui-datepicker-header,
.cmb2-element .ui-datepicker .ui-widget-header,
.cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #00a0d2;
}

/* line 212, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker thead,
.cmb2-element .ui-datepicker thead {
	background: #32373c;
}

/* line 216, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker td .ui-state-hover,
.cmb2-element.ui-datepicker td .ui-state-active,
.cmb2-element .ui-datepicker td .ui-state-hover,
.cmb2-element .ui-datepicker td .ui-state-active {
	background: #0073aa;
	color: #fff;
}

/* line 221, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-timepicker-div,
.cmb2-element .ui-datepicker .ui-timepicker-div {
	font-size: 14px;
}

/* line 223, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-timepicker-div dl,
.cmb2-element .ui-datepicker .ui-timepicker-div dl {
	text-align: left;
	padding: 0 .6em;
}

/* line 226, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-timepicker-div dl dt,
.cmb2-element .ui-datepicker .ui-timepicker-div dl dt {
	float: left;
	clear: left;
	padding: 0 0 0 5px;
}

/* line 231, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-timepicker-div dl dd,
.cmb2-element .ui-datepicker .ui-timepicker-div dl dd {
	margin: 0 10px 10px 40%;
}

/* line 233, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-timepicker-div dl dd select,
.cmb2-element .ui-datepicker .ui-timepicker-div dl dd select {
	width: 100%;
}

/* line 239, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-timepicker-div + .ui-datepicker-buttonpane,
.cmb2-element .ui-datepicker .ui-timepicker-div + .ui-datepicker-buttonpane {
	padding: .6em;
	text-align: left;
}

/* line 243, sass/partials/_jquery_ui.scss */

.cmb2-element.ui-datepicker .ui-timepicker-div + .ui-datepicker-buttonpane .button-primary,
.cmb2-element.ui-datepicker .ui-timepicker-div + .ui-datepicker-buttonpane .button-secondary,
.cmb2-element .ui-datepicker .ui-timepicker-div + .ui-datepicker-buttonpane .button-primary,
.cmb2-element .ui-datepicker .ui-timepicker-div + .ui-datepicker-buttonpane .button-secondary {
	padding: 0 10px 1px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	margin: 0 .6em .4em .4em;
}

/* line 257, sass/partials/_jquery_ui.scss */

.admin-color-fresh .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-fresh .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-fresh .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-fresh .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #00a0d2;
}

/* line 262, sass/partials/_jquery_ui.scss */

.admin-color-fresh .cmb2-element.ui-datepicker thead,
.admin-color-fresh .cmb2-element .ui-datepicker thead {
	background: #32373c;
}

/* line 266, sass/partials/_jquery_ui.scss */

.admin-color-fresh .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-fresh .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #0073aa;
	color: #fff;
}

/* line 274, sass/partials/_jquery_ui.scss */

.admin-color-blue .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-blue .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-blue .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-blue .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #52accc;
}

/* line 279, sass/partials/_jquery_ui.scss */

.admin-color-blue .cmb2-element.ui-datepicker thead,
.admin-color-blue .cmb2-element .ui-datepicker thead {
	background: #4796b3;
}

/* line 288, sass/partials/_jquery_ui.scss */

.admin-color-blue .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-blue .cmb2-element.ui-datepicker td .ui-state-active,
.admin-color-blue .cmb2-element .ui-datepicker td .ui-state-hover,
.admin-color-blue .cmb2-element .ui-datepicker td .ui-state-active {
	background: #096484;
	color: #fff;
}

/* line 293, sass/partials/_jquery_ui.scss */

.admin-color-blue .cmb2-element.ui-datepicker td.ui-datepicker-today,
.admin-color-blue .cmb2-element .ui-datepicker td.ui-datepicker-today {
	background: #eee;
}

/* line 302, sass/partials/_jquery_ui.scss */

.admin-color-coffee .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-coffee .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-coffee .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-coffee .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #59524c;
}

/* line 307, sass/partials/_jquery_ui.scss */

.admin-color-coffee .cmb2-element.ui-datepicker thead,
.admin-color-coffee .cmb2-element .ui-datepicker thead {
	background: #46403c;
}

/* line 311, sass/partials/_jquery_ui.scss */

.admin-color-coffee .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-coffee .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #c7a589;
	color: #fff;
}

/* line 319, sass/partials/_jquery_ui.scss */

.admin-color-ectoplasm .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-ectoplasm .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-ectoplasm .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-ectoplasm .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #523f6d;
}

/* line 324, sass/partials/_jquery_ui.scss */

.admin-color-ectoplasm .cmb2-element.ui-datepicker thead,
.admin-color-ectoplasm .cmb2-element .ui-datepicker thead {
	background: #413256;
}

/* line 328, sass/partials/_jquery_ui.scss */

.admin-color-ectoplasm .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-ectoplasm .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #a3b745;
	color: #fff;
}

/* line 336, sass/partials/_jquery_ui.scss */

.admin-color-midnight .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-midnight .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-midnight .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-midnight .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #363b3f;
}

/* line 341, sass/partials/_jquery_ui.scss */

.admin-color-midnight .cmb2-element.ui-datepicker thead,
.admin-color-midnight .cmb2-element .ui-datepicker thead {
	background: #26292c;
}

/* line 345, sass/partials/_jquery_ui.scss */

.admin-color-midnight .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-midnight .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #e14d43;
	color: #fff;
}

/* line 353, sass/partials/_jquery_ui.scss */

.admin-color-ocean .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-ocean .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-ocean .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-ocean .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #738e96;
}

/* line 358, sass/partials/_jquery_ui.scss */

.admin-color-ocean .cmb2-element.ui-datepicker thead,
.admin-color-ocean .cmb2-element .ui-datepicker thead {
	background: #627c83;
}

/* line 362, sass/partials/_jquery_ui.scss */

.admin-color-ocean .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-ocean .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #9ebaa0;
	color: #fff;
}

/* line 370, sass/partials/_jquery_ui.scss */

.admin-color-sunrise .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-sunrise .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-sunrise .cmb2-element.ui-datepicker .ui-datepicker-header .ui-state-hover,
.admin-color-sunrise .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-sunrise .cmb2-element .ui-datepicker .ui-datepicker-header,
.admin-color-sunrise .cmb2-element .ui-datepicker .ui-datepicker-header .ui-state-hover {
	background: #cf4944;
}

/* line 376, sass/partials/_jquery_ui.scss */

.admin-color-sunrise .cmb2-element.ui-datepicker th,
.admin-color-sunrise .cmb2-element .ui-datepicker th {
	border-color: #be3631;
	background: #be3631;
}

/* line 381, sass/partials/_jquery_ui.scss */

.admin-color-sunrise .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-sunrise .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #dd823b;
	color: #fff;
}

/* line 389, sass/partials/_jquery_ui.scss */

.admin-color-light .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-light .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-light .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-light .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #e5e5e5;
}

/* line 394, sass/partials/_jquery_ui.scss */

.admin-color-light .cmb2-element.ui-datepicker select.ui-datepicker-month,
.admin-color-light .cmb2-element.ui-datepicker select.ui-datepicker-year,
.admin-color-light .cmb2-element .ui-datepicker select.ui-datepicker-month,
.admin-color-light .cmb2-element .ui-datepicker select.ui-datepicker-year {
	color: #555;
}

/* line 399, sass/partials/_jquery_ui.scss */

.admin-color-light .cmb2-element.ui-datepicker thead,
.admin-color-light .cmb2-element .ui-datepicker thead {
	background: #888;
}

/* line 403, sass/partials/_jquery_ui.scss */

.admin-color-light .cmb2-element.ui-datepicker .ui-datepicker-title,
.admin-color-light .cmb2-element.ui-datepicker td .ui-state-default,
.admin-color-light .cmb2-element.ui-datepicker .ui-datepicker-prev:before,
.admin-color-light .cmb2-element.ui-datepicker .ui-datepicker-next:before,
.admin-color-light .cmb2-element .ui-datepicker .ui-datepicker-title,
.admin-color-light .cmb2-element .ui-datepicker td .ui-state-default,
.admin-color-light .cmb2-element .ui-datepicker .ui-datepicker-prev:before,
.admin-color-light .cmb2-element .ui-datepicker .ui-datepicker-next:before {
	color: #555;
}

/* line 411, sass/partials/_jquery_ui.scss */

.admin-color-light .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-light .cmb2-element.ui-datepicker td .ui-state-active,
.admin-color-light .cmb2-element .ui-datepicker td .ui-state-hover,
.admin-color-light .cmb2-element .ui-datepicker td .ui-state-active {
	background: #ccc;
}

/* line 415, sass/partials/_jquery_ui.scss */

.admin-color-light .cmb2-element.ui-datepicker td.ui-datepicker-today,
.admin-color-light .cmb2-element .ui-datepicker td.ui-datepicker-today {
	background: #eee;
}

/* line 423, sass/partials/_jquery_ui.scss */

.admin-color-bbp-evergreen .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-bbp-evergreen .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-bbp-evergreen .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-bbp-evergreen .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #56b274;
}

/* line 428, sass/partials/_jquery_ui.scss */

.admin-color-bbp-evergreen .cmb2-element.ui-datepicker thead,
.admin-color-bbp-evergreen .cmb2-element .ui-datepicker thead {
	background: #36533f;
}

/* line 432, sass/partials/_jquery_ui.scss */

.admin-color-bbp-evergreen .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-bbp-evergreen .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #446950;
	color: #fff;
}

/* line 440, sass/partials/_jquery_ui.scss */

.admin-color-bbp-mint .cmb2-element.ui-datepicker .ui-widget-header,
.admin-color-bbp-mint .cmb2-element.ui-datepicker .ui-datepicker-header,
.admin-color-bbp-mint .cmb2-element .ui-datepicker .ui-widget-header,
.admin-color-bbp-mint .cmb2-element .ui-datepicker .ui-datepicker-header {
	background: #4ca26a;
}

/* line 445, sass/partials/_jquery_ui.scss */

.admin-color-bbp-mint .cmb2-element.ui-datepicker thead,
.admin-color-bbp-mint .cmb2-element .ui-datepicker thead {
	background: #4f6d59;
}

/* line 449, sass/partials/_jquery_ui.scss */

.admin-color-bbp-mint .cmb2-element.ui-datepicker td .ui-state-hover,
.admin-color-bbp-mint .cmb2-element .ui-datepicker td .ui-state-hover {
	background: #5fb37c;
	color: #fff;
}

/*# sourceMappingURL=cmb2.css.map */

@media (max-width: 450px) {

/* line 209, sass/partials/_main_wrap.scss */

.cmb-th {
	font-size: 1.2em;
	display: block;
	float: none;
	padding-bottom: 1em;
	text-align: left;
	width: 100%;
}

/* line 27, sass/partials/_mixins.scss */

.cmb-th label {
	display: block;
	margin-top: 0em;
	padding-bottom: 5px;
}

/* line 32, sass/partials/_mixins.scss */

.cmb-th label:after {
	border-bottom: 1px solid #e9e9e9;
	content: '';
	clear: both;
	display: block;
	padding-top: .4em;
}

/* line 431, sass/partials/_main_wrap.scss */

.cmb-th,
.cmb-td,
.cmb-th + .cmb-td {
	display: block;
	float: none;
	width: 100%;
}

/* line 72, sass/partials/_post_metaboxes.scss */

.postbox-container .cmb-row:not(:last-of-type),
.postbox-container .cmb-repeatable-group:not(:last-of-type),
.cmb-type-group .cmb-row:not(:last-of-type),
.cmb-type-group .cmb-repeatable-group:not(:last-of-type) {
	border-bottom: 0;
}

}

