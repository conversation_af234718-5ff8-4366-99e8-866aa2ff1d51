/*
 * jQuery UI CSS Framework 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 *
 * WordPress Styles adopted from "jQuery UI Datepicker CSS for WordPress"
 * https://github.com/stuttter/wp-datepicker-styling
 *
 */

* html .cmb2-element.ui-helper-clearfix {
	height:1%;
}

$weekend: #f4f4f4;
$freshblue: #00a0d2;
$freshdark: #32373c;
$freshdarkblue: #0073aa;

.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
	padding: 0;
	margin: 0;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	background-color: #fff;
	border: 1px solid #dfdfdf;
	border-top: none;
	-webkit-box-shadow: 0 3px 6px rgba(0, 0, 0, 0.075);
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.075);
	min-width: 17em;
	width: auto;

	* {
		padding: 0;
		font-family: "Open Sans", sans-serif;
		-webkit-border-radius: 0;
		-moz-border-radius: 0;
		border-radius: 0;
	}

	table {
		font-size: 13px;
		margin: 0;
		border: none;
		border-collapse: collapse;
	}

	.ui-widget-header,
	.ui-datepicker-header {
		background-image: none;
		border: none;
		color: #fff;
		font-weight: normal;
	}

	.ui-datepicker-header .ui-state-hover {
		background: transparent;
		border-color: transparent;
		cursor: pointer;
	}

	.ui-datepicker-title {
		margin: 0;
		padding: 10px 0;
		color: #fff;
		font-size: 14px;
		line-height: 14px;
		text-align: center;

		select {
			margin-top: -8px;
			margin-bottom: -8px;
		}
	}

	.ui-datepicker-prev,
	.ui-datepicker-next {
		position: relative;
		top: 0;
		height: 34px;
		width: 34px;
	}

	.ui-state-hover.ui-datepicker-prev,
	.ui-state-hover.ui-datepicker-next {
		border: none;
	}

	.ui-datepicker-prev,
	.ui-datepicker-prev-hover {
		left: 0;
	}

	.ui-datepicker-next,
	.ui-datepicker-next-hover {
		right: 0;
	}

	.ui-datepicker-next span,
	.ui-datepicker-prev span {
		display: none;
	}

	.ui-datepicker-prev {
		float: left;
	}

	.ui-datepicker-next {
		float: right;
	}

	.ui-datepicker-prev:before,
	.ui-datepicker-next:before {
		font: normal 20px/34px 'dashicons';
		padding-left: 7px;
		color: #fff;
		speak: none;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		width: 34px;
		height: 34px;
	}

	.ui-datepicker-prev:before {
		content: '\f341';
	}

	.ui-datepicker-next:before {
		content: '\f345';
	}

	.ui-datepicker-prev-hover:before,
	.ui-datepicker-next-hover:before {
		opacity: 0.7;
	}

	select.ui-datepicker-month,
	select.ui-datepicker-year {
		width: 33%;
		background: transparent;
		border-color: transparent;
		box-shadow: none;
		color: #fff;
	}

	thead {
		color: #fff;
		font-weight: 600;
		th {
			font-weight: normal;
		}
	}

	th {
		padding: 10px;
	}

	td {
		padding: 0;
		border: 1px solid $weekend;
	}

	td.ui-datepicker-other-month {
		border: transparent;
	}

	td.ui-datepicker-week-end {
		background-color: $weekend;
		border: 1px solid $weekend;
		&.ui-datepicker-today {
			-webkit-box-shadow: inset 0px 0px 1px 0px rgba(0, 0, 0, 0.1);
			-moz-box-shadow: inset 0px 0px 1px 0px rgba(0, 0, 0, 0.1);
			box-shadow: inset 0px 0px 1px 0px rgba(0, 0, 0, 0.1);
		}
	}

	td.ui-datepicker-today {
		background-color: #f0f0c0;
	}

	td.ui-datepicker-current-day {
		background: #bbdd88;
	}

	td .ui-state-default {
		background: transparent;
		border: none;
		text-align: center;
		text-decoration: none;
		width: auto;
		display: block;
		padding: 5px 10px;
		font-weight: normal;
		color: #444;
	}

	td.ui-state-disabled .ui-state-default {
		opacity: 0.5;
	}

	/* Default Color Scheme */
	.ui-widget-header,
	.ui-datepicker-header {
		background: $freshblue;
	}

	thead {
		background: $freshdark;
	}

	td .ui-state-hover, td .ui-state-active {
		background: $freshdarkblue;
		color: #fff;
	}

	.ui-timepicker-div {
		font-size: 14px;
		dl {
			text-align: left;
			padding: 0 .6em;
			dt {
				float: left;
				clear:left;
				padding: 0 0 0 5px;
			}
			dd {
				margin: 0 10px 10px 40%;
				select {
					width: 100%;
				}
			}
		}

		+ .ui-datepicker-buttonpane {
			padding: .6em;
			text-align: left;

			.button-primary, .button-secondary {
				padding: 0 10px 1px;
				-webkit-border-radius: 3px;
				-moz-border-radius: 3px;
				border-radius: 3px;
				margin: 0 .6em .4em .4em;
			}

		}
	}

}
.admin-color-fresh {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: $freshblue;
		}

		thead {
			background: $freshdark;
		}

		td .ui-state-hover {
			background: $freshdarkblue;
			color: #fff;
		}
	}
}
.admin-color-blue {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #52accc;
		}

		thead {
			background: #4796b3;
		}

		// td .ui-state-hover {
		// 	background: #096484;
		// }

		td {
			.ui-state-hover, .ui-state-active {
				background: #096484;
				color: #fff;
			}

			&.ui-datepicker-today {
				background: #eee;
			}
		}

	}
}
.admin-color-coffee {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #59524c;
		}

		thead {
			background: #46403c;
		}

		td .ui-state-hover {
			background: #c7a589;
			color: #fff;
		}
	}
}
.admin-color-ectoplasm {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #523f6d;
		}

		thead {
			background: #413256;
		}

		td .ui-state-hover {
			background: #a3b745;
			color: #fff;
		}
	}
}
.admin-color-midnight {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #363b3f;
		}

		thead {
			background: #26292c;
		}

		td .ui-state-hover {
			background: #e14d43;
			color: #fff;
		}
	}
}
.admin-color-ocean {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #738e96;
		}

		thead {
			background: #627c83;
		}

		td .ui-state-hover {
			background: #9ebaa0;
			color: #fff;
		}
	}
}
.admin-color-sunrise {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header,
		.ui-datepicker-header .ui-state-hover {
			background: #cf4944;
		}

		th {
			border-color: #be3631;
			background: #be3631;
		}

		td .ui-state-hover {
			background: #dd823b;
			color: #fff;
		}
	}
}
.admin-color-light {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #e5e5e5;
		}

		select.ui-datepicker-month,
		select.ui-datepicker-year {
			color: #555;
		}

		thead {
			background: #888;
		}

		.ui-datepicker-title,
		td .ui-state-default,
		.ui-datepicker-prev:before,
		.ui-datepicker-next:before {
			color: #555;
		}

		td {
			.ui-state-hover, .ui-state-active {
				background: #ccc;
			}

			&.ui-datepicker-today {
				background: #eee;
			}
		}
	}
}
.admin-color-bbp-evergreen {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #56b274;
		}

		thead {
			background: #36533f;
		}

		td .ui-state-hover {
			background: #446950;
			color: #fff;
		}
	}
}
.admin-color-bbp-mint {
	.cmb2-element.ui-datepicker, .cmb2-element .ui-datepicker {
		.ui-widget-header,
		.ui-datepicker-header {
			background: #4ca26a;
		}

		thead {
			background: #4f6d59;
		}

		td .ui-state-hover {
			background: #5fb37c;
			color: #fff;
		}
	}
}
