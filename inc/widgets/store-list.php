<?php
    /**
    * Front-end display of widget.
    *
    * @see WP_Widget::widget()
    *
    * @param array $args     Widget arguments.
    * @param array $instance Saved values from database.
    */


        $stores = get_terms( $tax_args );
        global $post;
        ?>
        <!-- Tailwind CSS Store Widget -->
        <div class="widget-content bg-white rounded-xl shadow-coupon p-4">
            <div class="grid grid-cols-<?php echo $instance['item_per_row']; ?> gap-4">
                <?php
                foreach ( $stores as $store ) {
                    wpcoupon_setup_store( $store );
                    $store_coupons_count = wpcoupon_store()->count;
                    $is_featured = get_term_meta( wpcoupon_store()->term_id, '_wpc_is_featured', true );
                    ?>
                <div class="store-widget-item group">
                    <div class="store-card relative overflow-hidden transition-all duration-300 hover:shadow-store <?php echo $is_featured ? 'ring-1 ring-primary-300' : ''; ?>">

                        <?php if ( $is_featured ) { ?>
                            <!-- Featured Badge -->
                            <div class="absolute top-1 right-1 z-10">
                                <span class="bg-primary-300 text-primary-900 text-xs px-1 py-0.5 rounded-full font-semibold">
                                    <?php esc_html_e( 'مميز', 'wp-coupon' ); ?>
                                </span>
                            </div>
                        <?php } ?>

                        <!-- Store Thumbnail -->
                        <div class="store-card-header p-3 bg-gradient-to-br from-gray-50 to-gray-100">
                            <a href="<?php echo wpcoupon_store()->get_url(); ?>" class="block">
                                <div class="store-logo w-12 h-12 mx-auto rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <?php echo wpcoupon_store()->get_thumbnail() ?>
                                </div>
                            </a>
                        </div>

                        <!-- Store Name -->
                        <div class="store-card-body p-2">
                            <h4 class="store-name text-center">
                                <a href="<?php echo wpcoupon_store()->get_url(); ?>"
                                   class="text-sm font-semibold text-gray-900 hover:text-secondary-600 transition-colors duration-200 line-clamp-1">
                                    <?php echo wpcoupon_store()->name; ?>
                                </a>
                            </h4>

                            <!-- Store Stats -->
                            <div class="mt-1 text-center">
                                <span class="inline-flex items-center bg-secondary-50 text-secondary-700 px-2 py-0.5 rounded-full text-xs">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM10 18a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="font-medium"><?php echo $store_coupons_count; ?></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>
        <?php
       // wp_reset_postdata();
        echo $args['after_widget'];



// register Foo_Widget widget
function wpcoupon_register_popular_store_widget() {
    register_widget( 'WPCoupon_Popular_Store_list' );
}
add_action( 'widgets_init', 'wpcoupon_register_popular_store_list_widget' );