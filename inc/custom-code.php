<?php


/**
 * Output Header Tracking Code to wp_head hook.
 */
function wpcoupon_theme_header_code() {
    $site_header_tracking = wpcoupon_get_option('site_header_tracking');
    if ( $site_header_tracking !== '' ) echo $site_header_tracking;
}
add_action( 'wp_head', 'wpcoupon_theme_header_code' );

/**
 * Output Footer Tracking Code to wp_footer hook.
 */
function wpcoupon_theme_footer_code() {
    $site_footer_tracking = wpcoupon_get_option('site_footer_tracking');
    if ( $site_footer_tracking !== '' ) echo $site_footer_tracking;
}
add_action( 'wp_footer', 'wpcoupon_theme_footer_code' );







/**
 * ========================================
 * PROFESSIONAL RATING SYSTEM
 * ========================================
 *
 * Features:
 * - SVG stars with RTL/LTR support
 * - Store taxonomy and Coupon post type rating support
 * - Uses wpcoupon_setup_store() and wpcoupon_setup_coupon() functions
 * - SEO optimized with schema markup
 * - Performance optimized with caching
 * - AJAX rating submission
 * - Loop card integration ready
 */

/**
 * Get rating data for store taxonomy or coupon post
 *
 * @param int $id Term ID for store taxonomy or Post ID for coupon
 * @param string $type 'store' or 'coupon'
 * @return array Rating data
 */
function wpcoupon_get_rating_data($id, $type = null) {
    // Auto-detect type if not provided
    if (!$type) {
        // Check if we're on a store taxonomy page or dealing with store
        if (is_tax('store') || (function_exists('get_queried_object') && get_queried_object() && get_queried_object()->taxonomy === 'store')) {
            $type = 'store';
        } elseif (get_post_type($id) === 'coupon' || is_singular('coupon')) {
            $type = 'coupon';
        } else {
            $type = 'coupon'; // Default fallback
        }
    }

    // Cache key for performance
    $cache_key = "wpcoupon_rating_{$type}_{$id}";
    $cached_data = wp_cache_get($cache_key, 'wpcoupon_ratings');

    if ($cached_data !== false) {
        return $cached_data;
    }

    // Get rating data from meta (store uses term meta, coupon uses post meta)
    if ($type === 'store') {
        $total_ratings = (int) get_term_meta($id, '_wpcoupon_total_ratings', true);
        $total_score = (float) get_term_meta($id, '_wpcoupon_total_score', true);
        $average_rating = $total_ratings > 0 ? round($total_score / $total_ratings, 1) : 0;

        // Get store data using wpcoupon_setup_store function
        $store_data = wpcoupon_setup_store($id);
        $name = $store_data['name'] ?? get_term($id)->name ?? 'متجر';
        $url = $store_data['url'] ?? get_term_link($id);
        $description = $store_data['description'] ?? '';
    } else {
        $total_ratings = (int) get_post_meta($id, '_wpcoupon_total_ratings', true);
        $total_score = (float) get_post_meta($id, '_wpcoupon_total_score', true);
        $average_rating = $total_ratings > 0 ? round($total_score / $total_ratings, 1) : 0;

        // Get coupon data using wpcoupon_setup_coupon function
        $coupon_data = wpcoupon_setup_coupon($id);
        $name = $coupon_data['title'] ?? get_the_title($id);
        $url = $coupon_data['url'] ?? get_permalink($id);
        $description = $coupon_data['description'] ?? '';
    }

    $rating_data = array(
        'id' => $id,
        'type' => $type,
        'name' => $name,
        'url' => $url,
        'description' => $description,
        'average_rating' => $average_rating,
        'total_ratings' => $total_ratings,
        'total_score' => $total_score,
        'has_ratings' => $total_ratings > 0
    );

    // Cache for 1 hour
    wp_cache_set($cache_key, $rating_data, 'wpcoupon_ratings', HOUR_IN_SECONDS);

    return $rating_data;
}

/**
 * Generate SVG stars for rating display with RTL/LTR support and enhanced hover effects
 *
 * @param float $rating Rating value (0-5)
 * @param bool $interactive Whether stars are clickable
 * @param string $size Size class (small, medium, large)
 * @return string SVG stars HTML
 */
function wpcoupon_generate_rating_stars($rating, $interactive = false, $size = 'medium') {
    $rating = max(0, min(5, (float) $rating)); // Ensure rating is between 0-5
    $full_stars = floor($rating);
    $half_star = ($rating - $full_stars) >= 0.5;
    $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);

    // Size classes
    $size_classes = array(
        'small' => 'w-4 h-4',
        'medium' => 'w-5 h-5',
        'large' => 'w-6 h-6'
    );
    $star_size = $size_classes[$size] ?? $size_classes['medium'];

    // RTL support
    $is_rtl = is_rtl();
    $direction_class = $is_rtl ? 'flex-row-reverse' : 'flex-row';

    // Enhanced interactive class with hover effects
    $interactive_container_class = $interactive ? 'wpcoupon-interactive-stars' : '';

    $stars_html = '<div class="wpcoupon-rating-stars flex items-center ' . $direction_class . ' ' . $interactive_container_class . '" data-rating="' . esc_attr($rating) . '">';

    // Generate all 5 stars
    for ($i = 1; $i <= 5; $i++) {
        $is_filled = $i <= $full_stars;
        $is_half = !$is_filled && $i == ($full_stars + 1) && $half_star;
        $is_empty = !$is_filled && !$is_half;

        $interactive_attrs = $interactive ? 'data-star="' . $i . '" role="button" tabindex="0" aria-label="' . sprintf(__('Rate %d stars', 'wpcoupon'), $i) . '"' : '';

        // Enhanced interactive classes with hover effects
        $star_classes = array($star_size, 'fill-current', 'transition-all', 'duration-200');

        if ($interactive) {
            $star_classes[] = 'cursor-pointer';
            $star_classes[] = 'wpcoupon-star-interactive';
            $star_classes[] = 'hover:scale-110';
            $star_classes[] = 'hover:drop-shadow-lg';
        }

        // Determine star color
        if ($is_filled) {
            $star_classes[] = 'text-yellow-400';
        } elseif ($is_half) {
            // Half star will be handled separately
            $star_classes[] = 'text-gray-300';
        } else {
            $star_classes[] = 'text-gray-300';
            if ($interactive) {
                $star_classes[] = 'hover:text-yellow-200';
            }
        }

        if ($is_half) {
            // Half star with special styling
            $stars_html .= '<div class="relative ' . $star_size . ' ' . ($interactive ? 'cursor-pointer wpcoupon-star-interactive hover:scale-110 transition-all duration-200' : '') . '" ' . $interactive_attrs . '>
                <svg class="' . $star_size . ' text-gray-300 fill-current absolute" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <svg class="' . $star_size . ' text-yellow-400 fill-current absolute" style="clip-path: ' . ($is_rtl ? 'inset(0 0 0 50%)' : 'inset(0 50% 0 0)') . ';" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
            </div>';
        } else {
            // Regular star
            $stars_html .= '<svg class="' . implode(' ', $star_classes) . '" ' . $interactive_attrs . ' viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>';
        }
    }

    $stars_html .= '</div>';

    return $stars_html;
}

/**
 * Display rating system for single store taxonomy or coupon page
 *
 * @param int $id Term ID for store or Post ID for coupon (optional, auto-detects current)
 * @param array $options Display options
 * @return string Rating HTML
 */
function wpcoupon_display_rating_system($id = null, $options = array()) {
    // Auto-detect current page if no ID provided
    if (!$id) {
        if (is_tax('store')) {
            $id = get_queried_object_id();
            $type = 'store';
        } elseif (is_singular('coupon')) {
            $id = get_the_ID();
            $type = 'coupon';
        } else {
            return ''; // Not a store or coupon page
        }
    } else {
        // Determine type based on context or default to coupon
        $type = (is_tax('store') || (isset($options['type']) && $options['type'] === 'store')) ? 'store' : 'coupon';
    }

    if (!$id) {
        return '';
    }

    // Default options
    $defaults = array(
        'show_rating_button' => true,
        'show_average' => true,
        'show_count' => true,
        'size' => 'medium',
        'context' => 'single'
    );
    $options = wp_parse_args($options, $defaults);

    // Get rating data
    $rating_data = wpcoupon_get_rating_data($id, $type);

    // RTL support
    $is_rtl = is_rtl();
    $text_align = $is_rtl ? 'text-right' : 'text-left';
    $flex_direction = $is_rtl ? 'flex-row-reverse' : 'flex-row';

    $html = '<div class="wpcoupon-rating-system bg-white rounded-lg border border-gray-200 p-4 mb-6" data-id="' . esc_attr($id) . '" data-type="' . esc_attr($type) . '">';

    // Rating header
    $html .= '<div class="flex items-center justify-between mb-4 ' . $flex_direction . '">';
    $html .= '<h3 class="text-lg font-bold text-gray-900 ' . $text_align . '">';
    $html .= $type === 'store' ? __('تقييم المتجر', 'wpcoupon') : __('تقييم الكوبون', 'wpcoupon');
    $html .= '</h3>';

    if ($options['show_average'] && $rating_data['has_ratings']) {
        $html .= '<div class="flex items-center space-x-2 space-x-reverse">';
        $html .= '<span class="text-2xl font-bold text-yellow-600">' . number_format($rating_data['average_rating'], 1) . '</span>';
        $html .= '<span class="text-gray-500">/5</span>';
        $html .= '</div>';
    }
    $html .= '</div>';

    // Stars display
    $html .= '<div class="flex items-center justify-between mb-4 ' . $flex_direction . '">';
    $html .= '<div class="flex items-center space-x-3 space-x-reverse">';
    $html .= wpcoupon_generate_rating_stars($rating_data['average_rating'], false, $options['size']);

    if ($options['show_count'] && $rating_data['has_ratings']) {
        $html .= '<span class="text-sm text-gray-600 ' . ($is_rtl ? 'mr-3' : 'ml-3') . '">';
        $html .= sprintf(_n('تقييم واحد', '%s تقييم', $rating_data['total_ratings'], 'wpcoupon'), number_format($rating_data['total_ratings']));
        $html .= '</span>';
    }
    $html .= '</div>';
    $html .= '</div>';

    // Interactive rating section (automatic submission)
    if ($options['show_rating_button']) {
        $html .= '<div class="border-t border-gray-200 pt-4">';
        $html .= '<h4 class="text-md font-semibold text-gray-800 mb-3 ' . $text_align . '">' . __('قيم هذا', 'wpcoupon') . ' ' . ($type === 'store' ? __('المتجر', 'wpcoupon') : __('الكوبون', 'wpcoupon')) . '</h4>';

        $html .= '<div class="flex items-center justify-center ' . $flex_direction . '">';
        $html .= '<div class="wpcoupon-interactive-rating" data-id="' . esc_attr($id) . '" data-type="' . esc_attr($type) . '">';
        $html .= wpcoupon_generate_rating_stars(0, true, $options['size']);
        $html .= '</div>';
        $html .= '</div>';

        // Rating message and loading indicator
        $html .= '<div class="wpcoupon-rating-message mt-3 text-sm text-center hidden"></div>';
        $html .= '<div class="wpcoupon-rating-loading mt-3 text-center hidden">';
        $html .= '<div class="inline-flex items-center px-4 py-2 text-sm text-blue-600">';
        $html .= '<svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">';
        $html .= '<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>';
        $html .= '<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>';
        $html .= '</svg>';
        $html .= __('جاري إرسال التقييم...', 'wpcoupon');
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Display compact rating for loop cards
 *
 * @param int $id Term ID for store or Post ID for coupon
 * @param string $type 'store' or 'coupon'
 * @param string $size Star size (small, medium, large)
 * @return string Compact rating HTML
 */
function wpcoupon_display_loop_rating($id, $type = 'coupon', $size = 'small') {
    $rating_data = wpcoupon_get_rating_data($id, $type);

    if (!$rating_data['has_ratings']) {
        return '';
    }

    $is_rtl = is_rtl();
    $flex_direction = $is_rtl ? 'flex-row-reverse' : 'flex-row';

    $html = '<div class="wpcoupon-loop-rating flex items-center gap-1 ' . $flex_direction . '">';
    $html .= wpcoupon_generate_rating_stars($rating_data['average_rating'], false, $size);
    $html .= '<span class="text-xs text-gray-600 font-medium ' . ($is_rtl ? 'mr-1' : 'ml-1') . '">';
    $html .= number_format($rating_data['average_rating'], 1);
    $html .= '</span>';
    $html .= '<span class="text-xs text-gray-400">(' . number_format($rating_data['total_ratings']) . ')</span>';
    $html .= '</div>';

    return $html;
}

/**
 * AJAX handler for rating submission
 */
function wpcoupon_submit_rating_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wpcoupon_rating_nonce')) {
        wp_die(__('Security check failed', 'wpcoupon'));
    }

    $id = (int) $_POST['id'];
    $type = sanitize_text_field($_POST['type']);
    $rating = (int) $_POST['rating'];
    $user_ip = $_SERVER['REMOTE_ADDR'];

    // Validate rating
    if ($rating < 1 || $rating > 5) {
        wp_send_json_error(__('تقييم غير صحيح', 'wpcoupon'));
    }

    // Validate type
    if (!in_array($type, array('store', 'coupon'))) {
        wp_send_json_error(__('نوع غير صحيح', 'wpcoupon'));
    }

    // Check if user already rated (by IP)
    $meta_key = '_wpcoupon_user_ratings';
    if ($type === 'store') {
        $user_ratings = get_term_meta($id, $meta_key, true);
    } else {
        $user_ratings = get_post_meta($id, $meta_key, true);
    }

    if (!is_array($user_ratings)) {
        $user_ratings = array();
    }

    if (isset($user_ratings[$user_ip])) {
        wp_send_json_error(__('لقد قمت بالتقييم مسبقاً', 'wpcoupon'));
    }

    // Add new rating
    $user_ratings[$user_ip] = array(
        'rating' => $rating,
        'timestamp' => current_time('timestamp')
    );

    // Update user ratings meta
    if ($type === 'store') {
        update_term_meta($id, $meta_key, $user_ratings);

        // Update totals for store
        $total_ratings = (int) get_term_meta($id, '_wpcoupon_total_ratings', true);
        $total_score = (float) get_term_meta($id, '_wpcoupon_total_score', true);

        $new_total_ratings = $total_ratings + 1;
        $new_total_score = $total_score + $rating;
        $new_average = round($new_total_score / $new_total_ratings, 1);

        update_term_meta($id, '_wpcoupon_total_ratings', $new_total_ratings);
        update_term_meta($id, '_wpcoupon_total_score', $new_total_score);
    } else {
        update_post_meta($id, $meta_key, $user_ratings);

        // Update totals for coupon
        $total_ratings = (int) get_post_meta($id, '_wpcoupon_total_ratings', true);
        $total_score = (float) get_post_meta($id, '_wpcoupon_total_score', true);

        $new_total_ratings = $total_ratings + 1;
        $new_total_score = $total_score + $rating;
        $new_average = round($new_total_score / $new_total_ratings, 1);

        update_post_meta($id, '_wpcoupon_total_ratings', $new_total_ratings);
        update_post_meta($id, '_wpcoupon_total_score', $new_total_score);
    }

    // Clear cache
    wp_cache_delete("wpcoupon_rating_{$type}_{$id}", 'wpcoupon_ratings');

    wp_send_json_success(array(
        'message' => __('شكراً لك! تم إرسال تقييمك بنجاح', 'wpcoupon'),
        'new_average' => $new_average,
        'new_count' => $new_total_ratings
    ));
}
add_action('wp_ajax_wpcoupon_submit_rating', 'wpcoupon_submit_rating_ajax');
add_action('wp_ajax_nopriv_wpcoupon_submit_rating', 'wpcoupon_submit_rating_ajax');

/**
 * Generate schema markup for ratings (SEO optimized)
 *
 * @param int $id Term ID for store or Post ID for coupon
 * @param string $type 'store' or 'coupon'
 * @return string Schema markup JSON-LD
 */
function wpcoupon_generate_rating_schema($id, $type = null) {
    $rating_data = wpcoupon_get_rating_data($id, $type);

    if (!$rating_data['has_ratings']) {
        return '';
    }

    $schema = array(
        '@context' => 'https://schema.org',
        '@type' => $rating_data['type'] === 'store' ? 'Store' : 'Offer',
        'name' => $rating_data['name'],
        'url' => $rating_data['url'],
        'aggregateRating' => array(
            '@type' => 'AggregateRating',
            'ratingValue' => $rating_data['average_rating'],
            'reviewCount' => $rating_data['total_ratings'],
            'bestRating' => 5,
            'worstRating' => 1
        )
    );

    // Add description if available
    if (!empty($rating_data['description'])) {
        $schema['description'] = wp_strip_all_tags($rating_data['description']);
    }

    // Add additional schema properties for coupons
    if ($rating_data['type'] === 'coupon') {
        $coupon_data = wpcoupon_setup_coupon($id);
        if (!empty($coupon_data['discount_value'])) {
            $schema['priceSpecification'] = array(
                '@type' => 'PriceSpecification',
                'price' => $coupon_data['discount_value'],
                'priceCurrency' => 'SAR'
            );
        }
    }

    return '<script type="application/ld+json">' . wp_json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . '</script>';
}

/**
 * Enqueue rating system scripts and styles
 */
function wpcoupon_enqueue_rating_scripts() {
    if (is_singular('coupon') || is_tax('store')) {
        wp_localize_script('jquery', 'wpcoupon_rating', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wpcoupon_rating_nonce'),
            'messages' => array(
                'select_rating' => __('يرجى اختيار تقييم', 'wpcoupon'),
                'error' => __('حدث خطأ، يرجى المحاولة مرة أخرى', 'wpcoupon'),
                'success' => __('تم إرسال تقييمك بنجاح', 'wpcoupon')
            )
        ));

        // Add inline JavaScript for enhanced automatic rating functionality
        wp_add_inline_script('jquery', '
        jQuery(document).ready(function($) {
            var selectedRating = 0;
            var isRTL = $("body").hasClass("rtl") || $("html").attr("dir") === "rtl";

            // Enhanced hover effects for stars
            $(document).on("mouseenter", ".wpcoupon-interactive-stars .wpcoupon-star-interactive", function() {
                var $star = $(this);
                var $container = $star.closest(".wpcoupon-interactive-rating");
                var starIndex = $star.data("star");

                if (!$container.hasClass("rating-submitted")) {
                    // Highlight stars up to hovered star
                    $container.find(".wpcoupon-star-interactive").each(function(index) {
                        var currentStarIndex = $(this).data("star");
                        if (currentStarIndex <= starIndex) {
                            $(this).removeClass("text-gray-300 hover:text-yellow-200").addClass("text-yellow-300");
                        } else {
                            $(this).removeClass("text-yellow-300 text-yellow-400").addClass("text-gray-300");
                        }
                    });
                }
            });

            // Reset hover effects when leaving star container
            $(document).on("mouseleave", ".wpcoupon-interactive-stars", function() {
                var $container = $(this).closest(".wpcoupon-interactive-rating");

                if (!$container.hasClass("rating-submitted")) {
                    // Reset to original state or selected rating
                    $container.find(".wpcoupon-star-interactive").each(function(index) {
                        var currentStarIndex = $(this).data("star");
                        if (selectedRating > 0 && currentStarIndex <= selectedRating) {
                            $(this).removeClass("text-gray-300 text-yellow-300").addClass("text-yellow-400");
                        } else {
                            $(this).removeClass("text-yellow-300 text-yellow-400").addClass("text-gray-300");
                        }
                    });
                }
            });

            // Handle automatic star clicks (immediate submission)
            $(document).on("click", ".wpcoupon-interactive-rating .wpcoupon-star-interactive", function() {
                var $star = $(this);
                var $container = $star.closest(".wpcoupon-interactive-rating");
                var $system = $container.closest(".wpcoupon-rating-system");

                // Prevent multiple submissions
                if ($container.hasClass("rating-submitted")) {
                    return;
                }

                selectedRating = parseInt($star.data("star"));
                var id = $container.data("id");
                var type = $container.data("type");

                // Update visual state immediately
                $container.find(".wpcoupon-star-interactive").each(function() {
                    var currentStarIndex = $(this).data("star");
                    if (currentStarIndex <= selectedRating) {
                        $(this).removeClass("text-gray-300 text-yellow-300").addClass("text-yellow-400");
                    } else {
                        $(this).removeClass("text-yellow-400 text-yellow-300").addClass("text-gray-300");
                    }
                });

                // Mark as submitted and disable further clicks
                $container.addClass("rating-submitted");
                $container.find(".wpcoupon-star-interactive").removeClass("cursor-pointer hover:scale-110").addClass("cursor-default");

                // Show loading indicator
                $system.find(".wpcoupon-rating-loading").removeClass("hidden");
                $system.find(".wpcoupon-rating-message").addClass("hidden");

                // Submit rating automatically
                $.ajax({
                    url: wpcoupon_rating.ajax_url,
                    type: "POST",
                    data: {
                        action: "wpcoupon_submit_rating",
                        id: id,
                        type: type,
                        rating: selectedRating,
                        nonce: wpcoupon_rating.nonce
                    },
                    success: function(response) {
                        $system.find(".wpcoupon-rating-loading").addClass("hidden");

                        if (response.success) {
                            $system.find(".wpcoupon-rating-message")
                                .removeClass("hidden text-red-600")
                                .addClass("text-green-600")
                                .html("<div class=\"flex items-center justify-center gap-2\"><svg class=\"w-4 h-4 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path></svg>" + response.data.message + "</div>");

                            // Update the display rating after 2 seconds
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        } else {
                            $system.find(".wpcoupon-rating-message")
                                .removeClass("hidden text-green-600")
                                .addClass("text-red-600")
                                .html("<div class=\"flex items-center justify-center gap-2\"><svg class=\"w-4 h-4 text-red-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path></svg>" + response.data + "</div>");

                            // Re-enable rating
                            $container.removeClass("rating-submitted");
                            $container.find(".wpcoupon-star-interactive").addClass("cursor-pointer hover:scale-110").removeClass("cursor-default");
                        }
                    },
                    error: function() {
                        $system.find(".wpcoupon-rating-loading").addClass("hidden");
                        $system.find(".wpcoupon-rating-message")
                            .removeClass("hidden text-green-600")
                            .addClass("text-red-600")
                            .html("<div class=\"flex items-center justify-center gap-2\"><svg class=\"w-4 h-4 text-red-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path></svg>" + wpcoupon_rating.messages.error + "</div>");

                        // Re-enable rating
                        $container.removeClass("rating-submitted");
                        $container.find(".wpcoupon-star-interactive").addClass("cursor-pointer hover:scale-110").removeClass("cursor-default");
                    }
                });
            });

            // Keyboard accessibility for stars
            $(document).on("keydown", ".wpcoupon-interactive-rating .wpcoupon-star-interactive", function(e) {
                if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    $(this).click();
                }
            });
        });
        ');
    }
}
add_action('wp_enqueue_scripts', 'wpcoupon_enqueue_rating_scripts');

/**
 * Output schema markup in head for single pages
 */
function wpcoupon_output_rating_schema() {
    if (is_singular('coupon')) {
        echo wpcoupon_generate_rating_schema(get_the_ID(), 'coupon');
    } elseif (is_tax('store')) {
        echo wpcoupon_generate_rating_schema(get_queried_object_id(), 'store');
    }
}
add_action('wp_head', 'wpcoupon_output_rating_schema');

/**
 * Clear rating cache when content is updated
 */
function wpcoupon_clear_rating_cache($id, $type = null) {
    if ($type === 'store' || is_tax('store')) {
        wp_cache_delete("wpcoupon_rating_store_{$id}", 'wpcoupon_ratings');
    } else {
        wp_cache_delete("wpcoupon_rating_coupon_{$id}", 'wpcoupon_ratings');
    }
}
add_action('save_post', 'wpcoupon_clear_rating_cache');
add_action('edited_term', 'wpcoupon_clear_rating_cache');

/**
 * Helper function to easily display rating in templates
 *
 * Usage:
 * - Single coupon page: echo wpcoupon_rating();
 * - Single store page: echo wpcoupon_rating();
 * - Loop coupon card: echo wpcoupon_rating($coupon_id, 'loop', 'coupon');
 * - Loop store card: echo wpcoupon_rating($store_id, 'loop', 'store');
 */
function wpcoupon_rating($id = null, $context = 'single', $type = null, $options = array()) {
    // Auto-detect current page if no ID provided
    if (!$id) {
        if (is_tax('store')) {
            $id = get_queried_object_id();
            $type = 'store';
        } elseif (is_singular('coupon')) {
            $id = get_the_ID();
            $type = 'coupon';
        } else {
            return '';
        }
    }

    // Auto-detect type if not provided
    if (!$type) {
        $type = (is_tax('store') || $context === 'store') ? 'store' : 'coupon';
    }

    if (!$id) {
        return '';
    }

    switch ($context) {
        case 'loop':
            return wpcoupon_display_loop_rating($id, $type, $options['size'] ?? 'small');

        case 'single':
        default:
            if ($type === 'store') {
                $options['type'] = 'store';
            }
            return wpcoupon_display_rating_system($id, $options);
    }
}

/**
 * ========================================
 * USAGE EXAMPLES:
 * ========================================
 *
 * 1. Display full rating system on single coupon page:
 *    echo wpcoupon_rating();
 *
 * 2. Display full rating system on single store page:
 *    echo wpcoupon_rating();
 *
 * 3. Display compact rating in coupon loop cards:
 *    echo wpcoupon_rating($coupon_id, 'loop', 'coupon');
 *
 * 4. Display compact rating in store loop cards:
 *    echo wpcoupon_rating($store_term_id, 'loop', 'store');
 *
 * 5. Display rating with custom options:
 *    echo wpcoupon_rating($id, 'single', 'coupon', array(
 *        'show_rating_button' => false,
 *        'size' => 'large'
 *    ));
 *
 * 6. Get rating data programmatically:
 *    $rating_data = wpcoupon_get_rating_data($id, 'coupon');
 *    $rating_data = wpcoupon_get_rating_data($term_id, 'store');
 *
 * 7. Generate schema markup:
 *    echo wpcoupon_generate_rating_schema($id, 'coupon');
 *    echo wpcoupon_generate_rating_schema($term_id, 'store');
 *
 * IMPORTANT NOTES:
 * - Store ratings use TERM META (get_term_meta/update_term_meta)
 * - Coupon ratings use POST META (get_post_meta/update_post_meta)
 * - Store data comes from wpcoupon_setup_store($term_id)
 * - Coupon data comes from wpcoupon_setup_coupon($post_id)
 * - Schema markup only outputs if ratings exist
 * - System is fully RTL/LTR compatible
 * - Caching is optimized for performance
 */

