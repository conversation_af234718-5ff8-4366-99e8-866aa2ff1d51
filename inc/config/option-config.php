<?php

/**
 * Theme Options Config
 */

if ( ! class_exists( 'WPCoupon_Theme_Options_Config' )  ) {

    class WPCoupon_Theme_Options_Config {

        public $args = array();
        public $sections = array();
        public $theme;
        public $ReduxFramework;

        public function __construct() {

            if ( ! class_exists( 'ReduxFramework' ) ) {
                return;
            }
            $this->initSettings();
        }


        public function initSettings() {

            // Set the default arguments
            $this->setArguments();

            // Set a few help tabs so you can see how it's done
            $this->setHelpTabs();

            // Create the sections and fields
            $this->setSections();

            if ( ! isset( $this->args['opt_name'] ) ) { // No errors please
                return;
            }

            $this->args = apply_filters( 'st_redux_theme_options_args', $this->args );

            $this->ReduxFramework = new ReduxFramework( $this->sections, $this->args );
        }

        public function setHelpTabs() {
            // Set the help sidebar
            $this->args['help_sidebar'] = esc_html__( '<p>This is the sidebar content, HTML is allowed.</p>', 'wp-coupon' );
        }

        /**
         * All the possible arguments for Redux.
         * For full documentation on arguments, please refer to: https://github.com/ReduxFramework/ReduxFramework/wiki/Arguments
         * */
        public function setArguments() {

            $theme = wp_get_theme(); // For use with some settings. Not necessary.

            $this->args = array(
                // TYPICAL -> Change these values as you need/desire
                'opt_name'           => 'st_options',
                // This is where your data is stored in the database and also becomes your global variable name.
                'display_name'       => $theme->get( 'Name' ),
                // Name that appears at the top of your panel
                'display_version'    => false,
                // Version that appears at the top of your panel
                'menu_type'          => 'menu', // submenu , menu
                //Specify if the admin menu should appear or not. Options: menu or submenu (Under appearance only)
                'allow_sub_menu'     => false,
                // Show the sections below the admin menu item or not
                'menu_title'         => esc_html__( 'اعدادات القالب', 'wp-coupon' ),
                'page_title'         => esc_html__( 'اعدادات القالب', 'wp-coupon' ),
                // You will need to generate a Google API key to use this feature.
                // Please visit: https://developers.google.com/fonts/docs/developer_api#Auth
                //'google_api_key'     => '',
                // Must be defined to add google fonts to the typography module


                'async_typography'   => false,
                // Use a asynchronous font on the front end or font string
                'admin_bar'          => false,
                // Show the panel pages on the admin bar
                'global_variable'    => 'st_option',
                // Set a different name for your global variable other than the opt_name
                'dev_mode'           => false,
                // Show the time the page took to load, etc
                'customizer'         => false,
                // Enable basic customizer support

                // OPTIONAL -> Give you extra features
                //'page_priority'      => 65,
                // Order where the menu appears in the admin area. If there is any conflict, something will not show. Warning.
                'page_parent'        => 'themes.php', // themes.php
                // For a full list of options, visit: http://codex.wordpress.org/Function_Reference/add_submenu_page#Parameters
                'page_permissions'   => 'manage_options',
                // Permissions needed to access the options panel.
                'menu_icon'          => '',
                // Specify a custom URL to an icon
                'last_tab'           => '',
                // Force your panel to always open to a specific tab (by id)
                'page_icon'          => 'icon-themes',
                // Icon displayed in the admin panel next to your menu_title
                'page_slug'          => 'wpcoupon_options',
                // Page slug used to denote the panel
                'save_defaults'      => true,
                // On load save the defaults to DB before user clicks save or not
                'default_show'       => false,
                // If true, shows the default value next to each field that is not the default value.
                'default_mark'       => '',
                // What to print by the field's title if the value shown is default. Suggested: *
                'show_import_export' => true,
                // Shows the Import/Export panel when not used as a field.

                // CAREFUL -> These options are for advanced use only
                'transient_time'     => 60 * MINUTE_IN_SECONDS,

                'output'             => true,
                // Global shut-off for dynamic CSS output by the framework. Will also disable google fonts output
                'output_tag'         => true,
                // Allows dynamic CSS to be generated for customizer and google fonts, but stops the dynamic CSS from going to the head
                'footer_credit'     => ' ',
                // Disable the footer credit of Redux. Please leave if you can help it.

                // FUTURE -> Not in use yet, but reserved or partially implemented. Use at your own risk.
                'database'           => '',
                // possible: options, theme_mods, theme_mods_expanded, transient. Not fully functional, warning!
                'system_info'        => false,
                // REMOVE

                // HINTS
                'hints'              => array(
                    'icon'          => 'icon-question-sign',
                    'icon_position' => 'right',
                    'icon_color'    => 'lightgray',
                    'icon_size'     => 'normal',
                    'tip_style'     => array(
                        'color'   => 'light',
                        'shadow'  => true,
                        'rounded' => false,
                        'style'   => '',
                    ),
                    'tip_position'  => array(
                        'my' => 'top left',
                        'at' => 'bottom right',
                    ),
                    'tip_effect'    => array(
                        'show' => array(
                            'effect'   => 'slide',
                            'duration' => '500',
                            'event'    => 'mouseover',
                        ),
                        'hide' => array(
                            'effect'   => 'slide',
                            'duration' => '500',
                            'event'    => 'click mouseleave',
                        ),
                    ),
                )
            );


            // Panel Intro text -> before the form
            if ( ! isset( $this->args['global_variable'] ) || $this->args['global_variable'] !== false ) {
                if ( ! empty( $this->args['global_variable'] ) ) {
                    $v = $this->args['global_variable'];
                } else {
                    $v = str_replace( '-', '_', $this->args['opt_name'] );
                }
                //$this->args['intro_text'] = sprintf( __( '<p>Did you know that Redux sets a global variable for you? To access any of your saved options from within your code you can use your global variable: <strong>$%1$s</strong></p>', 'wp-coupon' ), $v );
            } else {
                //$this->args['intro_text'] = __( '<p>This text is displayed above the options panel. It isn\'t required, but more info is always better! The intro_text field accepts all HTML.</p>', 'wp-coupon' );
            }

            // Add content after the form.
            //$this->args['footer_text'] = __( '<p>This text is displayed below the options panel. It isn\'t required, but more info is always better! The footer_text field accepts all HTML.</p>', 'wp-coupon' );
        }

        public function setSections() {


            /*--------------------------------------------------------*/
            /* GENERAL SETTINGS
            /*--------------------------------------------------------*/
            $this->sections[] = array(
                'title'  => esc_html__( 'اعدادات عامه', 'wp-coupon' ),
                'desc'   => sprintf( esc_html__( 'Redux Framework was created with the developer in mind. It allows for any theme developer to have an advanced theme panel with most of the features a developer would need. For more information check out the Github repo at: %d', 'wp-coupon' ), '<a href="' . 'https://' . 'github.com/ReduxFramework/Redux-Framework">' . 'https://' . 'github.com/ReduxFramework/Redux-Framework</a>' ),
                'desc'   => '',
                'icon'   => 'el-icon-cog el-icon-large',
                'submenu' => true, // Setting submenu to false on a given section will hide it from the WordPress sidebar menu!
                'fields' => array(

                    array(
                        'id'       =>'site_logo',
                        'url'      => false,
                        'type'     => 'media',
                        'title'    => esc_html__('اللوجو الخاص بك', 'wp-coupon'),
                        'default'  => array( 'url' => get_template_directory_uri() .'/assets/images/logo.png' ),
                        'subtitle' => esc_html__('قم برفع اللوجو', 'wp-coupon'),
                    ),

					array(
                        'id'       =>'retina_site_logo',
                        'url'      => false,
                        'type'     => 'media',
                        'title'    => esc_html__('ريتينا لوجو للشاشات عاليه الدقة', 'wp-coupon'),
                        'default'  => array( 'url' => get_template_directory_uri() .'/assets/images/logo.png' ),
                        'subtitle' => esc_html__('قم برفع اللوجو', 'wp-coupon'),
                    ),



                    array(
                        'id'       =>'coupons_listing_page',
                        'url'      => false,
                        'type'     => 'select',
                        'data'     => 'page',
                        'title'    => esc_html__(' صفحه عرض تصنيفات الكوبونات', 'wp-coupon'),
                        'default'  => '',
                    ),

                    array(
                        'id'       =>'stores_listing_page',
                        'url'      => false,
                        'type'     => 'select',
                        'data'     => 'page',
                        'title'    => esc_html__('صفحه عرض المتاجر', 'wp-coupon'),
                        'default'  => '',
                    ),


                    array(
                        'id'   =>'divider_r',
                        'desc' => '',
                        'type' => 'divide'
                    ),


                    array(
                        'id'       =>'rewrite_store_slug',
                        'url'      => false,
                        'type'     => 'text',
                        'title'    => esc_html__('رابط المتجر الدائم', 'wp-coupon'),
                        'subtitle'    => esc_html__('الافتراضي : store', 'wp-coupon'),
                        'default'  => 'store',
                        'desc'     => sprintf( esc_html__( 'If you change this option please go to Settings &#8594; %1$s and refresh your permalink structure before your custom post type will show the correct structure.', 'wp-coupon' ), '<a href="'.admin_url('options-permalink.php').'">'.esc_html__( 'Permalinks', 'wp-coupon' ).'</a>' ),
                    ),

                    array(
                        'id'       =>'rewrite_category_slug',
                        'url'      => false,
                        'type'     => 'text',
                        'title'    => esc_html__('رابط تصنيف الكوبون الدائم', 'wp-coupon'),
                        'subtitle'    => esc_html__('الافتراضي : coupon-category ', 'wp-coupon'),
                        'default'  => 'coupon-category',
                        'desc'     => sprintf( esc_html__( 'If you change this option please go to Settings &#8594; %1$s and refresh your permalink structure before your custom post type will show the correct structure.', 'wp-coupon' ), '<a href="'.admin_url('options-permalink.php').'">'.esc_html__( 'Permalinks', 'wp-coupon' ).'</a>' ),
                    ),

                    array(
                        'id'       =>'rewrite_tag_slug',
                        'url'      => false,
                        'type'     => 'text',
                        'title'    => esc_html__('رابط وسم الكوبون الدائم', 'wp-coupon'),
                        'subtitle'    => esc_html__('الافتراضي : coupon-tag', 'wp-coupon'),
                        'default'  => 'coupon-tag',
                        'desc'     => sprintf( esc_html__( 'If you change this option please go to Settings &#8594; %1$s and refresh your permalink structure before your custom post type will show the correct structure.', 'wp-coupon' ), '<a href="'.admin_url('options-permalink.php').'">'.esc_html__( 'Permalinks', 'wp-coupon' ).'</a>' ),
                    ),




                )
            );



            /*--------------------------------------------------------*/
            /* STORE
            /*--------------------------------------------------------*/
            $this->sections[] = array(
                'title'  => esc_html__( 'Single Store', 'wp-coupon' ),
                'desc'   => '',
                'icon'   => 'el-icon-shopping-cart',
                'submenu' => true,
                'fields' => array(

                    array(
                        'id'      => 'store_loop_tpl',
                        'title'   => esc_html__( 'Coupon Store template', 'wp-coupon' ),
                        'desc'    => esc_html__( 'Select template for store coupons.', 'wp-coupon' ),
                        'type'    => 'select',
                        'default' => 'full',
                        'options' => array(
                            'full'  => esc_html__( 'Full', 'wp-coupon' ),
                            'cat'   => esc_html__( 'Less', 'wp-coupon' ),
                        ),
                    ),

                    array(
                        'id'       => 'coupon_store_show_thumb',
                        'type'     => 'select',
                        'default' => 'default',
                        'title'    => esc_html__('Show coupon item thumbnails', 'wp-coupon'),
                        'options' => array(
                            'default'                   => esc_html__( 'Default, Show if has thumbnail else store thumbnail instead.', 'wp-coupon' ),
                            'hide_if_no_thumb'          => esc_html__( 'Show if has thumbnail', 'wp-coupon' ),
                            'save_value'                => esc_html__( 'Show discount value as coupon thumbnail', 'wp-coupon' ),
                            'hide'                      => esc_html__( 'Hide All', 'wp-coupon' ),
                        ),
                    ),



                    array(
                        'id'       =>'store_heading',
                        'type'     => 'textarea',
                        'default' => '%store_name%',
                        'title'    => esc_html__('Store custom heading', 'wp-coupon'),
                        'subtitle' => esc_html__('Custom heading text for display on single store page. Use %store_name% to replace with current store name.', 'wp-coupon'),
                    ),

                    array(
                        'id'       =>'go_store_slug',
                        'type'     => 'text',
                        'default' => 'go-store',
                        'title'    => esc_html__('Custom goto store slug', 'wp-coupon'),
                        'desc'    => sprintf( esc_html__('When you enable this option maybe the permalinks will effect, to resolve this go to %1$s and hit "Save Changes" button.', 'wp-coupon'), '<a href="'.esc_url( admin_url( 'options-permalink.php' )  ).'">'.esc_html__( 'Permalinks Settings', 'wp-coupon' ).'</a>' ),
                    ),


                )
            );

            /*--------------------------------------------------------*/
            /* COUPON CATEGORY
            /*--------------------------------------------------------*/
            $this->sections[] = array(
                'title'  => esc_html__( 'Coupon Category', 'wp-coupon' ),
                'desc'   => '',
                'icon'   => 'el-icon-tags',
                'submenu' => true,
                'fields' => array(

                    array(
                        'id'      => 'coupon_cate_tpl',
                        'title'   => esc_html__( 'Coupon Category template', 'wp-coupon' ),
                        'desc'    => esc_html__( 'Select template for coupon category.', 'wp-coupon' ),
                        'type'    => 'select',
                        'default' => 'cat',
                        'options' => array(
                            'cat'   => esc_html__( 'Less', 'wp-coupon' ),
                            'full'  => esc_html__( 'Full', 'wp-coupon' ),
                        ),
                    ),

                    array(
                        'id'       => 'coupon_cate_show_thumb',
                        'type'     => 'select',
                        'default' => 'default',
                        'title'    => esc_html__('Show coupon item thumbnails', 'wp-coupon'),
                        'options' => array(
                            'default'                   => esc_html__( 'Default, Show if has thumbnail else store thumbnail instead.', 'wp-coupon' ),
                            'hide_if_no_thumb'          => esc_html__( 'Show if has thumbnail', 'wp-coupon' ),
                            'save_value'                => esc_html__( 'Show discount value as coupon thumbnail', 'wp-coupon' ),
                            'hide'                      => esc_html__( 'Hide All', 'wp-coupon' ),
                        ),
                    ),


                    array(
                        'id'       =>'coupon_cate_heading',
                        'type'     => 'textarea',
                        'default' => '%coupon_cate%',
                        'title'    => esc_html__('Coupon category custom heading', 'wp-coupon'),
                        'subtitle' => esc_html__('Custom heading text for display on coupon category page. You can use %coupon_cate% to display category name.', 'wp-coupon'),
                    ),
                    array(
                        'id'       =>'coupon_cate_subheading',
                        'type'     => 'text',
                        'default'  => esc_html__('Newest %coupon_cate% Coupons', 'wp-coupon'),
                        'title'    => esc_html__('Coupon category sub-heading', 'wp-coupon'),
                        'subtitle' => esc_html__('You can use %coupon_cate% to display category name.', 'wp-coupon'),
                    ),

                )
            );

            /*--------------------------------------------------------*/
            /* COUPON ITEM
            /*--------------------------------------------------------*/
            $this->sections[] = array(
                'title'  => esc_html__( 'Coupons', 'wp-coupon' ),
                'desc'   => '',
                'icon'   => 'el-icon-tag',
                'submenu' => true,
                'fields' => array(

                    array(
                        'id'       => 'enable_single_coupon',
                        'type'     => 'checkbox',
                        'default' => false,
                        'title'    => esc_html__('Enable single page for coupon.', 'wp-coupon'),
                        'desc'    => sprintf( esc_html__('When you enable this option maybe the permalinks will effect, to resolve this go to %1$s and hit "Save Changes" button.', 'wp-coupon'), '<a href="'.esc_url( admin_url( 'options-permalink.php' )  ).'">'.esc_html__( 'Permalinks Settings', 'wp-coupon' ).'</a>' ),
                    ),

                    array(
                        'id'       => 'auto_open_coupon_modal',
                        'type'     => 'checkbox',
                        'default' => false,
                        'title'    => esc_html__('Auto open coupon modal on single coupon.', 'wp-coupon'),
                        'required' => array( 'enable_single_coupon','equals','1')
                    ),

                    array(
                        'id'       => 'enable_single_popular',
                        'type'     => 'checkbox',
                        'default' => true,
                        'title'    => esc_html__('Enable popular coupons on single page.', 'wp-coupon'),
                        'required' => array( 'enable_single_coupon','equals','1')
                    ),

                    array(
                        'id'       => 'single_popular_text',
                        'type'     => 'text',
                        'default'   => esc_html__('Most popular {store} coupons.', 'wp-coupon'),
                        'title'    => esc_html__('Custom popular text.', 'wp-coupon'),
                        'subtitle' => esc_html__('Use {store} to display store name.', 'wp-coupon'),
                        'required' => array( 'enable_single_popular','equals','1')
                    ),

                    array(
                        'id'       => 'single_popular_number',
                        'type'     => 'text',
                        'default' => 3,
                        'title'    => esc_html__('Number popular coupons on single page.', 'wp-coupon'),
                        'required' => array( 'enable_single_popular','equals','1' )
                    ),

                    array(
                        'id'       => 'coupon_item_logo',
                        'type'     => 'select',
                        'default' => 'default',
                        'title'    => esc_html__('Show coupon item thumbnails', 'wp-coupon'),
                        'options' => array(
                            'default'                   => esc_html__( 'Default, Show if has thumbnail else store thumbnail instead.', 'wp-coupon' ),
                            'hide_if_no_thumb'          => esc_html__( 'Show if has thumbnail', 'wp-coupon' ),
                            'save_value'                => esc_html__( 'Show discount value as coupon thumbnail', 'wp-coupon' ),
                            'hide'                      => esc_html__( 'Hide All', 'wp-coupon' ),
                        ),
                    ),

                    array(
                        'id'       => 'coupon_more_desc',
                        'type'     => 'checkbox',
                        'default' => 1,
                        'title'    => esc_html__('Show coupon read more description.', 'wp-coupon'),
                    ),

                    array(
                        'id'       => 'coupon_human_time',
                        'type'     => 'checkbox',
                        'default' => 0,
                        'title'    => esc_html__('Coupon human time diff', 'wp-coupon'),
                        'desc'    => esc_html__('Show human time diff such as 3 days left, 2 days left,...', 'wp-coupon'),
                    ),

                    array(
                        'id'       => 'coupon_item_exclusive',
                        'type'     => 'text',
                        'default'  => '<strong><i class="protect icon"></i>Exclusive:</strong> This coupon can only be found at our website.',
                        'title'    => esc_html__('Exclusive Coupon Message', 'wp-coupon'),
                    ),

                    array(
                        'id'      => 'coupon_expires_action',
                        'title'   => esc_html__( 'When coupon expires', 'wp-coupon' ),
                        'type'    => 'select',
                        'default' => 'do_nothing',
                        'options' => array(
                            'do_nothing' => esc_html__( 'Do Nothing', 'wp-coupon' ),
                            'set_status' => esc_html__( 'Disable', 'wp-coupon' ),
                            'remove'     => esc_html__( 'Remove', 'wp-coupon' ),
                        ),
                    ),

                    array(
                        'id'      => 'coupon_expires_time',
                        'title'   => esc_html__( 'Run expires action time', 'wp-coupon' ),
                        'desc'    => esc_html__( 'Run action after coupon expires x seconds., default: 604800 (1 week)', 'wp-coupon' ),
                        'type'    => 'text',
                        'default' => 604800, // 1 week
                    ),

                    array(
                        'id'       => 'print_prev_tab',
                        'type'     => 'checkbox',
                        //'required' => array( 'enable_single_coupon','!=','1'),
                        'default' => false,
                        'title'    => esc_html__('Open store website in previous tab when click on print button.', 'wp-coupon'),
                    ),

                    array(
                        'id'       => 'sale_prev_tab',
                        'type'     => 'checkbox',
                        //'required' => array( 'enable_single_coupon','!=','1'),
                        'default' => true,
                        'title'    => esc_html__('Open store website in previous tab when click on "Get Deal" button.', 'wp-coupon'),
                    ),

                    array(
                        'id'       => 'code_prev_tab',
                        'type'     => 'checkbox',
                        //'required' => array( 'enable_single_coupon','!=','1'),
                        'default' => true,
                        'title'    => esc_html__('Open store website in previous tab when click on "Get Code" button.', 'wp-coupon'),
                    ),


                    array(
                        'id'      => 'coupon_num_words_excerpt',
                        'title'   => esc_html__( 'Default coupon excerpt length', 'wp-coupon' ),
                        'type'    => 'text',
                        'default' => 10,
                    ),

                    array(
                        'id'       =>'go_out_slug',
                        'type'     => 'text',
                        'default' => 'out',
                        'title'    => esc_html__('Custom coupon go out slug', 'wp-coupon'),
                        'desc'    => sprintf( esc_html__('When you enable this option maybe the permalinks will effect, to resolve this go to %1$s and hit "Save Changes" button.', 'wp-coupon'), '<a href="'.esc_url( admin_url( 'options-permalink.php' )  ).'">'.esc_html__( 'Permalinks Settings', 'wp-coupon' ).'</a>' ),
                    ),

                    array(
                        'id'       =>'use_deal_txt',
                        'type'     => 'checkbox',
                        'default'  => 0,
                        'title'    => esc_html__('Use "Deal" text instead of "Sale"', 'wp-coupon'),
                    ),


                )
            );


            /*--------------------------------------------------------*/
            /* FOOTER
            /*--------------------------------------------------------*/
            $this->sections[] = array(
                'title'  => esc_html__( 'Footer', 'wp-coupon' ),
                'desc'   => '',
                'icon'   => 'el-icon-photo',
                'submenu' => true,
                'fields' => array(

                    array(
                        'id'       => 'before_footer',
                        'type'     => 'editor',
                        'title'    => esc_html__('Before footer', 'wp-coupon'),
                        'subtitle' => esc_html__( 'Note: This field only display on homepage', 'wp-coupon' ),
                        'default'  => '',
                    ),

                    array(
                        'id'      => 'before_footer_apply',
                        'type'    => 'radio',
                        'title'   => esc_html__( 'Before Footer Display', 'wp-coupon' ),
                        'desc'    => esc_html__( 'Note: Setting home page goto Settings -> Reading -> Front page displays -> Check static page -> Select a page', 'wp-coupon' ),
                        'default' => 'home',
                        'required' => array('footer_widgets','=',true, ),
                        'options' => array(
                            'home'   => esc_html__( 'Apply for home page only.', 'wp-coupon' ),
                            'all'   => esc_html__( 'Apply for all pages.', 'wp-coupon' ),
                        ),
                    ),



                    array(
                        'id'       =>'footer_copyright',
                        'type'     => 'textarea',
                        'title'    => esc_html__('Footer Copyright', 'wp-coupon'),
                        'subtitle' => esc_html__('Enter the copyright section text.', 'wp-coupon'),
                    ),

                )
            );


            /*--------------------------------------------------------*/
            /* EMAIL
            /*--------------------------------------------------------*/

        }

    }

    global $reduxConfig;
    function wpcoupon_options_init () {
        global $reduxConfig;
        //force remove sample redux demo option
        delete_option( 'ReduxFrameworkPlugin' );
        $reduxConfig = new WPCoupon_Theme_Options_Config();
    }
    add_action( 'init', 'wpcoupon_options_init' );

}


/**
 * Removes the demo link and the notice of integrated demo from the redux-framework plugin
 */
if ( ! function_exists( 'wp_coupon_remove_demo' ) ) {
    function wp_coupon_remove_demo() {
        // Used to hide the demo mode link from the plugin page. Only used when Redux is a plugin.
        if ( class_exists( 'ReduxFrameworkPlugin' ) ) {
            remove_filter( 'plugin_row_meta', array(
                ReduxFrameworkPlugin::instance(),
                'plugin_metalinks'
            ), null, 2 );

            // Used to hide the activation notice informing users of the demo panel. Only used when Redux is a plugin.
            remove_action( 'admin_notices', array( ReduxFrameworkPlugin::instance(), 'admin_notices' ) );
        }
    }
}
wp_coupon_remove_demo();


