<?php
/**
 * Modern Coupon Card System
 * Creative modern design with branded yellow colors
 * Uses functions from inc/core/coupon.php
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * THE ONLY COUPON CARD FUNCTION - Modern Creative Design
 * Uses existing functions from inc/core/coupon.php
 *
 * @param WP_Post|int $coupon Coupon post object or ID
 * @param string $style 'featured' or 'default'
 * @param array $options Card display options
 */
function render_coupon_card($coupon, $style = 'default', $options = array()) {
    // Simple error handling
    try {
        // Get coupon post
        if (is_numeric($coupon)) {
            $coupon = get_post($coupon);
        }

        if (!$coupon || $coupon->post_type !== 'coupon') {
            return;
        }

        // Default options
        $defaults = array(
            'show_image' => true,
            'show_store_logo' => true,
            'show_description' => true,
            'show_badges' => true,
            'show_voting' => true,
            'show_stats' => true,
            'show_rating' => true,
            'description_length' => 15
        );
        $options = wp_parse_args($options, $defaults);

        $coupon_type = get_post_meta($coupon->ID, '_wpc_coupon_type', true) ?: 'code';
        $coupon_code = get_post_meta($coupon->ID, '_wpc_coupon_type_code', true) ?: ''; // Correct field name
        $is_exclusive = get_post_meta($coupon->ID, '_wpc_exclusive', true) === 'on';
        $destination_url = get_post_meta($coupon->ID, '_wpc_destination_url', true) ?: get_permalink($coupon->ID);
        $coupon_href = get_permalink($coupon->ID);

        $coupon_save = get_post_meta($coupon->ID, '_wpc_coupon_save', true);
        $free_shipping = get_post_meta($coupon->ID, '_wpc_free_shipping', true) === 'on';
        $used_count = get_post_meta($coupon->ID, '_wpc_used', true) ?: 0;
        $vote_up = get_post_meta($coupon->ID, '_wpc_vote_up', true) ?: 0;
        $vote_down = get_post_meta($coupon->ID, '_wpc_vote_down', true) ?: 0;

        // Get store information 
        $store_name = '';
        $store_image = '';

        $store_terms = get_the_terms($coupon->ID, 'coupon_store');
        if ($store_terms && !is_wp_error($store_terms)) {
            $store_term = $store_terms[0];
            $store_name = $store_term->name;
            $store_image = get_term_meta($store_term->term_id, '_wpc_store_image', true);
        }

        // Enhanced Image fallback system: Featured Image -> Store Image -> Dynamic Placeholder
        $coupon_image = '';
        $image_type = 'placeholder'; // Track image source for styling



        if ($options['show_image']) {
            // Priority 1: Coupon featured image
            $featured_image = get_the_post_thumbnail_url($coupon->ID, 'medium');
            if ($featured_image) {
                $coupon_image = $featured_image;
                $image_type = 'featured';
            }
            // Priority 2: Store image
            elseif ($store_image) {
                $coupon_image = $store_image;
                $image_type = 'store';
            }
            // Priority 3: ALWAYS generate placeholder if no image found
            else {
                $coupon_image = generate_coupon_placeholder($coupon, $store_name, $coupon_type, $coupon_save);
                $image_type = 'placeholder';
            }
        }



        // Success rate calculation
        $total_votes = $vote_up + $vote_down;
        $success_rate = $total_votes > 0 ? round(($vote_up / $total_votes) * 100) : 95;

        // Card classes with modern design
        $card_classes = array(
            'modern-coupon-card',
            'group',
            'relative',
            'bg-white',
            'rounded-2xl',
            'border',
            'border-gray-100',
            'overflow-hidden',
            'transition-all',
            'duration-500',
            'hover:shadow-2xl',
            'hover:-translate-y-2',
            'hover:border-yellow-300'
        );

        // Add style-specific classes
        if ($style === 'featured') {
            $card_classes[] = 'featured-coupon';
            $card_classes[] = 'ring-3';
            $card_classes[] = 'ring-yellow-400';
            $card_classes[] = 'shadow-lg';
            $card_classes[] = 'bg-gradient-to-br';
            $card_classes[] = 'from-yellow-50';
            $card_classes[] = 'to-white';
        } else {
            $card_classes[] = 'default-coupon';
        }

        // Add coupon type class for CSS filtering
        $card_classes[] = 'coupon-type-' . $coupon_type;

        // Check if we're in AJAX context and use enhanced fallback
        if (defined('DOING_AJAX') && DOING_AJAX) {
            // Enhanced AJAX fallback - full modern design
            echo '<article class="' . esc_attr(implode(' ', $card_classes)) . '" data-coupon-id="' . $coupon->ID . '" data-coupon-type="' . esc_attr($coupon_type) . '">';

            // Animated Background Elements
            echo '<div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none">';
            echo '<div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-yellow-200 to-yellow-300 rounded-full group-hover:animate-pulse"></div>';
            echo '<div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-tr from-blue-200 to-blue-300 rounded-full group-hover:animate-bounce"></div>';
            echo '</div>';

            // Enhanced Coupon Image with dynamic styling based on image type
            if ($options['show_image']) {
                // Ensure we have an image - generate placeholder if needed
                if (empty($coupon_image)) {
                    $coupon_image = generate_coupon_placeholder($coupon, $store_name, $coupon_type, $coupon_save);
                    $image_type = 'placeholder';
                }

                echo '<div class="relative h-48 overflow-hidden">';

                // Add different styling based on image source
                $image_classes = 'w-full h-full transition-transform duration-700 group-hover:scale-110';
                if ($image_type === 'placeholder') {
                    $image_classes .= ' object-contain bg-gradient-to-br from-gray-50 to-gray-100';
                } else {
                    $image_classes .= ' object-cover';
                }

                $safe_src = (strpos($coupon_image, 'data:') === 0) ? $coupon_image : esc_url($coupon_image);
                echo '<img src="' . $safe_src . '" alt="' . esc_attr($coupon->post_title) . '" class="' . $image_classes . '">';

                // Creative badges with animations
                if ($options['show_badges']) {
                    echo '<div class="absolute bottom-4 left-4 flex flex-col space-y-2">';
                    if ($is_exclusive) echo '<div class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold shadow-lg transform rotate-3 hover:rotate-0 transition-transform">⭐ حصري</div>';
                    if ($free_shipping) echo '<div class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg transform -rotate-2 hover:rotate-0 transition-transform">🚚 شحن مجاني</div>';
                    if ($coupon_save) echo '<div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg transform rotate-1 hover:rotate-0 transition-transform">💰 ' . esc_html($coupon_save) . '</div>';
                    echo '</div>';
                }

                // Type Badge
                echo '<div class="absolute top-4 right-4">';
                if ($coupon_type === 'code') {
                    echo '<div class="bg-blue-600 text-white px-2 py-1 rounded-lg text-xs font-bold">🎫 كود</div>';
                } else {
                    echo '<div class="bg-purple-600 text-white px-2 py-1 rounded-lg text-xs font-bold">🔥 صفقة</div>';
                }
                echo '</div>';

                echo '</div>';
            }

            echo '<div class="relative z-10 p-6">';

            // Enhanced Store info with verified badge
            if ($options['show_store_logo'] && $store_name) {
                echo '<div class="flex items-center space-x-3 space-x-reverse mb-4">';
                echo '<div class="relative">';
                echo '<div class="w-12 h-12 rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex-shrink-0 shadow-md">';
                if ($store_image) {
                    echo '<img src="' . esc_url($store_image) . '" alt="' . esc_attr($store_name) . '" class="w-full h-full object-cover">';
                } else {
                    echo '<div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-sm">' . esc_html(substr($store_name, 0, 2)) . '</div>';
                }
                echo '</div>';
                // Verified badge
                echo '<div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">';
                echo '<svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
                echo '</div>';
                echo '</div>';
                echo '<div class="flex-1 min-w-0">';
                echo '<p class="text-sm font-bold text-gray-900 truncate">' . esc_html($store_name) . '</p>';
                if ($options['show_stats']) {
                    echo '<div class="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">';
                    echo '<span class="flex items-center"><svg class="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' . $success_rate . '% نجاح</span>';
                    echo '<span>•</span>';
                    echo '<span>' . $used_count . ' استخدام</span>';
                    echo '</div>';
                }
                echo '</div></div>';
            }

            // Enhanced Title with hover effect
            echo '<h3 class="text-lg font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">';
            echo '<a href="' . esc_url($coupon_href) . '" class="coupon-title-link coupon-button" data-type="' . esc_attr($coupon_type) . '" data-coupon-id="' . $coupon->ID . '" data-aff-url="' . esc_attr($destination_url) . '" data-code="' . esc_attr($coupon_code) . '">';
            echo esc_html($coupon->post_title);
            echo '</a></h3>';

            // Enhanced Description
            if ($options['show_description']) {
                $description = $coupon->post_excerpt ?: $coupon->post_content;
                echo '<p class="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">' . esc_html(wp_trim_words($description, $options['description_length'], '...')) . '</p>';
            }

            // Coupon Rating Display
            if ($options['show_rating'] && function_exists('wpcoupon_rating')) {
                echo '<div class="coupon-rating mb-4 flex items-center justify-center">';
                echo wpcoupon_rating($coupon->ID, 'loop', 'coupon', array('size' => 'small'));
                echo '</div>';
            }

            // Enhanced Action section
            echo '<div class="flex items-center justify-center mb-4">';

            // Enhanced Action Button with gradients and hover effects
            echo '<div class="coupon-button-wrapper">';
            if ($coupon_type === 'code' && $coupon_code) {
                
                echo '<a href="' . esc_url($coupon_href) . '" class="coupon-button coupon-code-btn show-peel group/btn relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-bold text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg inline-flex items-center overflow-hidden" data-coupon-id="' . $coupon->ID . '" data-type="code" data-aff-url="' . esc_attr($destination_url) . '" data-code="' . esc_attr($coupon_code) . '">';
                echo '<span class="coupon-button-peel">' . esc_attr($coupon_code) . '</span>';
                echo '<svg class="w-5 h-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>';
                echo '<span class="relative z-10">عرض الكود</span>';
                echo '</a>';
            } else {
                echo '<a href="' . esc_url($coupon_href) . '" class="coupon-button coupon-deal-btn group/btn relative bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl font-bold text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg inline-flex items-center overflow-hidden" data-coupon-id="' . $coupon->ID . '" data-type="sale" data-aff-url="' . esc_attr($destination_url) . '">';
                echo '<svg class="w-5 h-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path></svg>';
                echo '<span class="relative z-10"> عرض الصفقة </span>';
                echo '</a>';
            }
            echo '</div>';

            echo '</div>';

            // Bottom Stats Bar
            echo '<div class="flex items-center justify-between pt-4 border-t border-gray-100">';
            echo '<div class="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">';
            echo '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>';
            echo '<span>مشاهدة مؤخراً</span>';
            echo '</div>';
            echo '<div class="text-xs text-gray-500">صالح لفترة محدودة</div>';
            echo '</div>';

            echo '</div>';
            echo '</article>';

        } else {
            $template_path = get_template_directory() . '/template-parts/components/modern-coupon-card.php';

            if (file_exists($template_path)) {
                if (empty($coupon_image) || !isset($coupon_image)) {
                    $coupon_image = generate_coupon_placeholder($coupon, $store_name, $coupon_type, $coupon_save);
                    $image_type = 'placeholder';
                }

                // Make all variables available to the template
                include $template_path;
            } else {
                throw new Exception('Template file not found: ' . $template_path);
            }
        }

    } catch (Exception $e) {
        // Log the error for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Modern Coupon Card Error: ' . $e->getMessage());
        }

        // Fallback: Simple HTML output for AJAX
        echo '<div class="bg-white p-4 rounded-lg border">';
        echo '<h3 class="font-bold">' . esc_html($coupon->post_title) . '</h3>';
        echo '<p class="text-sm text-gray-600">' . esc_html(wp_trim_words($coupon->post_excerpt, 15)) . '</p>';
        echo '<a href="' . esc_url(get_permalink($coupon->ID)) . '" class="inline-block mt-2 bg-blue-500 text-white px-4 py-2 rounded">عرض الكوبون</a>';
        echo '</div>';
    }
}

/**
 * Legacy wrapper functions for backward compatibility
 */
function wpcoupon_render_coupon_card($coupon, $style = 'default', $args = array()) {
    // Handle legacy boolean style parameter
    if (is_bool($style)) {
        $style = $style ? 'featured' : 'default';
    }
    render_coupon_card($coupon, $style, $args);
}

function wpcoupon_render_featured_coupon_card($coupon) {
    render_coupon_card($coupon, 'featured');
}

function wpcoupon_render_default_coupon_card($coupon) {
    render_coupon_card($coupon, 'default');
}

function render_enhanced_coupon_card($coupon, $style = 'default', $args = array()) {
    render_coupon_card($coupon, $style, $args);
}

/**
 * Generate dynamic placeholder image for coupons
 * Creates beautiful gradient placeholders with coupon info
 *
 * @param WP_Post $coupon Coupon post object
 * @param string $store_name Store name
 * @param string $coupon_type Coupon type (code/sale)
 * @param string $coupon_save Savings amount
 * @return string Data URL for SVG placeholder
 */
function generate_coupon_placeholder($coupon, $store_name, $coupon_type, $coupon_save) {
    // Color schemes based on coupon type
    $color_schemes = array(
        'code' => array(
            'bg_start' => '#3B82F6', // Blue
            'bg_end' => '#1E40AF',
            'accent' => '#FCD34D', // Yellow
            'text' => '#FFFFFF',
            'icon' => '🎫'
        ),
        'sale' => array(
            'bg_start' => '#10B981', // Green
            'bg_end' => '#047857',
            'accent' => '#F59E0B', // Orange
            'text' => '#FFFFFF',
            'icon' => '🔥'
        )
    );

    $scheme = $color_schemes[$coupon_type] ?? $color_schemes['code'];

    // Prepare text content
    $title = esc_html(wp_trim_words($coupon->post_title, 4, '...'));
    $store_text = $store_name ? esc_html($store_name) : 'كوبون خصم';
    $save_text = $coupon_save ? esc_html($coupon_save) : 'خصم حصري';
    $icon = $scheme['icon'];

    // Create SVG placeholder
    $svg = '
    <svg width="400" height="240" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:' . $scheme['bg_start'] . ';stop-opacity:1" />
                <stop offset="100%" style="stop-color:' . $scheme['bg_end'] . ';stop-opacity:1" />
            </linearGradient>
            <filter id="shadow">
                <feDropShadow dx="0" dy="2" stdDeviation="4" flood-opacity="0.3"/>
            </filter>
        </defs>

        <!-- Background -->
        <rect width="100%" height="100%" fill="url(#bg)"/>

        <!-- Decorative circles -->
        <circle cx="350" cy="50" r="30" fill="' . $scheme['accent'] . '" opacity="0.2"/>
        <circle cx="50" cy="190" r="20" fill="' . $scheme['accent'] . '" opacity="0.3"/>
        <circle cx="320" cy="180" r="15" fill="white" opacity="0.1"/>

        <!-- Content container -->
        <rect x="20" y="20" width="360" height="200" rx="15" fill="white" opacity="0.1" filter="url(#shadow)"/>

        <!-- Icon -->
        <text x="200" y="80" text-anchor="middle" font-size="32" fill="' . $scheme['accent'] . '">' . $icon . '</text>

        <!-- Store name -->
        <text x="200" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="' . $scheme['text'] . '">' . $store_text . '</text>

        <!-- Coupon title -->
        <text x="200" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="' . $scheme['text'] . '" opacity="0.9">' . $title . '</text>

        <!-- Savings -->
        <rect x="150" y="150" width="100" height="30" rx="15" fill="' . $scheme['accent'] . '"/>
        <text x="200" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="' . $scheme['bg_end'] . '">' . $save_text . '</text>

        <!-- Decorative elements -->
        <path d="M 50 50 Q 100 30 150 50 T 250 50" stroke="' . $scheme['accent'] . '" stroke-width="2" fill="none" opacity="0.3"/>
        <path d="M 150 190 Q 200 210 250 190 T 350 190" stroke="white" stroke-width="1" fill="none" opacity="0.2"/>
    </svg>';

    // Convert SVG to data URL
    $svg_encoded = base64_encode($svg);
    return 'data:image/svg+xml;base64,' . $svg_encoded;
}
