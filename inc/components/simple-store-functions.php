<?php
/**
 * Enhanced Store Functions with Deduplication Logic
 * Clean and simple approach for store display with comprehensive duplicate prevention
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Global store tracking system to prevent duplicates across the entire page
 */
class AG_Store_Tracker {
    private static $instance = null;
    private $displayed_stores = array();
    private $page_sections = array();

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Track a store as displayed
     */
    public function track_store($store_id, $section = 'default') {
        $this->displayed_stores[] = intval($store_id);
        if (!isset($this->page_sections[$section])) {
            $this->page_sections[$section] = array();
        }
        $this->page_sections[$section][] = intval($store_id);
    }

    /**
     * Check if store is already displayed
     */
    public function is_store_displayed($store_id) {
        return in_array(intval($store_id), $this->displayed_stores);
    }

    /**
     * Get all displayed store IDs
     */
    public function get_displayed_stores() {
        return array_unique($this->displayed_stores);
    }

    /**
     * Get stores displayed in specific section
     */
    public function get_section_stores($section) {
        return isset($this->page_sections[$section]) ? $this->page_sections[$section] : array();
    }

    /**
     * Reset tracking (useful for testing or specific cases)
     */
    public function reset() {
        $this->displayed_stores = array();
        $this->page_sections = array();
    }

    /**
     * Get statistics
     */
    public function get_stats() {
        return array(
            'total_displayed' => count($this->displayed_stores),
            'unique_stores' => count(array_unique($this->displayed_stores)),
            'sections' => array_keys($this->page_sections),
            'duplicates_prevented' => count($this->displayed_stores) - count(array_unique($this->displayed_stores))
        );
    }
}

/**
 * Get featured stores with deduplication support
 *
 * @param int $number Number of stores to get
 * @param bool $exclude_displayed Whether to exclude already displayed stores
 * @param string $section Section identifier for tracking
 * @return array Array of store objects
 */
function get_featured_stores($number = 12, $exclude_displayed = true, $section = 'featured') {
    $tracker = AG_Store_Tracker::get_instance();

    // Get base featured stores
    $stores = wpcoupon_get_featured_stores($number * 2); // Get more to account for filtering

    if ($exclude_displayed && !empty($stores)) {
        $filtered_stores = array();
        foreach ($stores as $store) {
            if (!$tracker->is_store_displayed($store->term_id)) {
                $filtered_stores[] = $store;
                if (count($filtered_stores) >= $number) {
                    break;
                }
            }
        }
        $stores = $filtered_stores;
    }

    // Limit to requested number
    if (count($stores) > $number) {
        $stores = array_slice($stores, 0, $number);
    }

    return $stores;
}

/**
 * Get all stores (non-featured) with enhanced deduplication
 *
 * @param int $number Number of stores to get
 * @param array $exclude Array of store IDs to exclude
 * @param bool $exclude_displayed Whether to exclude already displayed stores
 * @param string $section Section identifier for tracking
 * @return array Array of store objects
 */
function get_all_stores($number = 12, $exclude = array(), $exclude_displayed = true, $section = 'popular') {
    $tracker = AG_Store_Tracker::get_instance();

    // Combine manual exclude list with displayed stores
    $exclude_ids = is_array($exclude) ? $exclude : array();

    if ($exclude_displayed) {
        $displayed_stores = $tracker->get_displayed_stores();
        $exclude_ids = array_merge($exclude_ids, $displayed_stores);
    }

    $args = array(
        'number' => $number * 2, // Get more to account for filtering
        'orderby' => 'count',
        'order' => 'DESC',
        'hide_empty' => true
    );

    if (!empty($exclude_ids)) {
        $args['exclude'] = array_unique($exclude_ids);
    }

    $stores = wpcoupon_get_stores($args);

    // Additional filtering to ensure no featured stores are included
    $featured_stores = wpcoupon_get_featured_stores(-1); // Get all featured stores
    $featured_ids = array();
    if (!empty($featured_stores)) {
        foreach ($featured_stores as $featured_store) {
            $featured_ids[] = $featured_store->term_id;
        }
    }

    if (!empty($featured_ids) && !empty($stores)) {
        $filtered_stores = array();
        foreach ($stores as $store) {
            if (!in_array($store->term_id, $featured_ids)) {
                $filtered_stores[] = $store;
                if (count($filtered_stores) >= $number) {
                    break;
                }
            }
        }
        $stores = $filtered_stores;
    }

    // Limit to requested number
    if (count($stores) > $number) {
        $stores = array_slice($stores, 0, $number);
    }

    return $stores;
}

/**
 * Get stores by IDs with deduplication support
 *
 * @param array $ids Array of store IDs
 * @param bool $exclude_displayed Whether to exclude already displayed stores
 * @param string $section Section identifier for tracking
 * @return array Array of store objects
 */
function get_stores_by_ids($ids = array(), $exclude_displayed = true, $section = 'custom') {
    if (empty($ids)) {
        return array();
    }

    $tracker = AG_Store_Tracker::get_instance();

    // Filter out displayed stores if requested
    if ($exclude_displayed) {
        $displayed_stores = $tracker->get_displayed_stores();
        $ids = array_diff($ids, $displayed_stores);
    }

    if (empty($ids)) {
        return array();
    }

    return wpcoupon_get_stores(array(
        'include' => $ids,
        'hide_empty' => false
    ));
}

/**
 * Render store card with enhanced tracking and deduplication
 *
 * @param object $store Store object
 * @param string $style Card style: 'featured', 'slider', 'grid', 'minimal'
 * @param array $args Additional arguments
 */
function render_store_card($store, $style = 'featured', $args = array()) {
    if (!$store) return;

    $tracker = AG_Store_Tracker::get_instance();

    // Check if store is already displayed (optional strict mode)
    $strict_mode = isset($args['strict_deduplication']) ? $args['strict_deduplication'] : false;
    if ($strict_mode && $tracker->is_store_displayed($store->term_id)) {
        return; // Skip rendering if already displayed
    }

    // Set up store context
    wpcoupon_setup_store($store);

    // Default arguments
    $defaults = array(
        'show_badge' => true,
        'badge_text' => '',
        'show_coupons_count' => true,
        'link_target' => '_self',
        'section' => 'default',
        'track_display' => true,
        'strict_deduplication' => false
    );

    $args = wp_parse_args($args, $defaults);

    // Track this store as displayed
    if ($args['track_display']) {
        $tracker->track_store($store->term_id, $args['section']);
    }

    // Set badge text based on style
    if (empty($args['badge_text'])) {
        switch ($style) {
            case 'featured':
                $args['badge_text'] = esc_html__('مميز', 'wp-coupon');
                break;
            case 'slider':
                $args['badge_text'] = esc_html__('مميز', 'wp-coupon');
                break;
            default:
                $args['badge_text'] = '';
                $args['show_badge'] = false;
                break;
        }
    }

    // Set query vars for the template
    set_query_var('store_card_style', $style);
    set_query_var('store_card_args', $args);

    // Load the appropriate template
    get_template_part('template-parts/components/simple-store-card');

    // Clean up
    set_query_var('store_card_style', null);
    set_query_var('store_card_args', null);
}

/**
 * Render featured stores slider with deduplication tracking
 *
 * @param array $stores Array of store objects
 * @param array $slider_args Slider configuration
 */
function render_featured_stores_slider($stores, $slider_args = array()) {
    if (empty($stores)) return;

    // Default slider configuration
    $defaults = array(
        'slides_per_view' => 4,
        'slides_per_view_mobile' => 1,
        'slides_per_view_tablet' => 2,
        'gap' => 24,
        'autoplay' => true,
        'autoplay_delay' => 4000,
        'loop' => true,
        'pagination' => true,
        'navigation' => true,
        'section' => 'featured-slider',
        'track_stores' => true
    );

    $slider_args = wp_parse_args($slider_args, $defaults);

    // Generate unique ID for this slider
    $slider_id = 'featured-stores-slider-' . uniqid();

    // Check if RTL
    $is_rtl = is_rtl();
    $direction = $is_rtl ? 'rtl' : 'ltr';
    ?>

    <!-- Featured Stores Slider -->
    <div class="featured-stores-slider-container" dir="<?php echo esc_attr($direction); ?>">
        <div id="<?php echo esc_attr($slider_id); ?>" class="splide featured-stores-slider <?php echo esc_attr($direction); ?>">
            <div class="splide__track ">
                <ul class="splide__list">
                    <?php foreach ($stores as $store) : ?>
                        <li class="splide__slide">
                            <?php
                            render_store_card($store, 'slider', array(
                                'section' => $slider_args['section'],
                                'track_display' => $slider_args['track_stores']
                            ));
                            ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Navigation arrows -->
            <div class="splide__arrows">
                <button class="splide__arrow splide__arrow--prev" aria-label="<?php echo $is_rtl ? esc_attr__('الشريحة التالية', 'wp-coupon') : esc_attr__('Previous slide', 'wp-coupon'); ?>">
                    <?php if ($is_rtl) : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    <?php else : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    <?php endif; ?>
                </button>
                <button class="splide__arrow splide__arrow--next" aria-label="<?php echo $is_rtl ? esc_attr__('الشريحة السابقة', 'wp-coupon') : esc_attr__('Next slide', 'wp-coupon'); ?>">
                    <?php if ($is_rtl) : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    <?php else : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    <?php endif; ?>
                </button>
            </div>

            <!-- Pagination dots -->
            <div class="splide__pagination"></div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof Splide !== 'undefined') {
            new Splide('#<?php echo esc_js($slider_id); ?>', {
                type: '<?php echo $slider_args['loop'] ? 'loop' : 'slide'; ?>',
                direction: '<?php echo $direction; ?>',
                perPage: <?php echo intval($slider_args['slides_per_view']); ?>,
                perMove: 1,
				Width: '1280px',
                gap: '<?php echo intval($slider_args['gap']); ?>px',
                autoplay: <?php echo $slider_args['autoplay'] ? 'true' : 'false'; ?>,
                interval: <?php echo intval($slider_args['autoplay_delay']); ?>,
                pagination: <?php echo $slider_args['pagination'] ? 'true' : 'false'; ?>,
                arrows: <?php echo $slider_args['navigation'] ? 'true' : 'false'; ?>,
                <?php if ($is_rtl) : ?>
                i18n: {
                    prev: '<?php echo esc_js(__('الشريحة السابقة', 'wp-coupon')); ?>',
                    next: '<?php echo esc_js(__('الشريحة التالية', 'wp-coupon')); ?>',
                    first: '<?php echo esc_js(__('الشريحة الأولى', 'wp-coupon')); ?>',
                    last: '<?php echo esc_js(__('الشريحة الأخيرة', 'wp-coupon')); ?>',
                    slideX: '<?php echo esc_js(__('الشريحة %s', 'wp-coupon')); ?>',
                    pageX: '<?php echo esc_js(__('الصفحة %s', 'wp-coupon')); ?>',
                    play: '<?php echo esc_js(__('تشغيل', 'wp-coupon')); ?>',
                    pause: '<?php echo esc_js(__('إيقاف مؤقت', 'wp-coupon')); ?>'
                },
                <?php endif; ?>
                breakpoints: {
                    768: {
                        perPage: <?php echo intval($slider_args['slides_per_view_mobile']); ?>,
                        gap: '16px'
                    },
                    1024: {
                        perPage: <?php echo intval($slider_args['slides_per_view_tablet']); ?>,
                        gap: '20px'
                    }
                }
            }).mount();
        }
    });
    </script>
    <?php
}

/**
 * Render stores grid with deduplication tracking
 *
 * @param array $stores Array of store objects
 * @param string $style Grid style: 'featured', 'grid', 'minimal'
 * @param array $grid_args Grid configuration
 */
function render_stores_grid($stores, $style = 'grid', $grid_args = array()) {
    if (empty($stores)) return;

    // Default grid configuration
    $defaults = array(
        'columns' => 4,
        'columns_tablet' => 2,
        'columns_mobile' => 1,
        'gap' => 6,
        'container_class' => 'stores-grid-container',
        'section' => 'grid',
        'track_stores' => true
    );

    $grid_args = wp_parse_args($grid_args, $defaults);

    $grid_classes = sprintf(
        'grid grid-cols-%d md:grid-cols-%d lg:grid-cols-%d gap-%d',
        $grid_args['columns_mobile'],
        $grid_args['columns_tablet'],
        $grid_args['columns'],
        $grid_args['gap']
    );
    ?>

    <div class="<?php echo esc_attr($grid_args['container_class']); ?>">
        <div class="<?php echo esc_attr($grid_classes); ?>">
            <?php foreach ($stores as $store) : ?>
                <div class="store-grid-item">
                    <?php
                    render_store_card($store, $style, array(
                        'section' => $grid_args['section'],
                        'track_display' => $grid_args['track_stores']
                    ));
                    ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}

/**
 * Shortcode for displaying stores
 *
 * Usage examples:
 * [stores type="featured" number="8"]
 * [stores type="all" number="12" exclude="1,2,3"]
 * [stores type="ids" ids="1,2,3,4,5"]
 * [stores type="featured" display="slider" slides="4"]
 * [stores type="all" display="grid" columns="3"]
 */
function stores_shortcode($atts) {
    $atts = shortcode_atts(array(
        'type' => 'featured',           // featured, all, ids
        'number' => 8,                  // number of stores
        'ids' => '',                    // comma-separated store IDs
        'exclude' => '',                // comma-separated store IDs to exclude
        'display' => 'grid',            // grid, slider
        'style' => 'featured',          // featured, grid, minimal
        'columns' => 4,                 // grid columns
        'slides' => 4,                  // slider slides per view
        'autoplay' => 'true',           // slider autoplay
        'loop' => 'true',               // slider loop
        'gap' => 24                     // gap between items
    ), $atts);

    // Get stores based on type with deduplication
    $stores = array();
    $section = 'shortcode-' . $atts['type'];

    switch ($atts['type']) {
        case 'featured':
            $stores = get_featured_stores(
                intval($atts['number']),
                true, // exclude displayed
                $section
            );
            break;

        case 'all':
            $exclude_ids = !empty($atts['exclude']) ?
                array_map('intval', explode(',', $atts['exclude'])) : array();
            $stores = get_all_stores(
                intval($atts['number']),
                $exclude_ids,
                true, // exclude displayed
                $section
            );
            break;

        case 'ids':
            if (!empty($atts['ids'])) {
                $store_ids = array_map('intval', explode(',', $atts['ids']));
                $stores = get_stores_by_ids(
                    $store_ids,
                    true, // exclude displayed
                    $section
                );
            }
            break;
    }

    if (empty($stores)) {
        return '<p class="no-stores-message">' . esc_html__('لا توجد متاجر للعرض', 'wp-coupon') . '</p>';
    }

    // Start output buffering
    ob_start();

    // Render based on display type
    if ($atts['display'] === 'slider') {
        render_featured_stores_slider($stores, array(
            'slides_per_view' => intval($atts['slides']),
            'autoplay' => $atts['autoplay'] === 'true',
            'loop' => $atts['loop'] === 'true',
            'gap' => intval($atts['gap']),
            'section' => $section,
            'track_stores' => true
        ));
    } else {
        render_stores_grid($stores, $atts['style'], array(
            'columns' => intval($atts['columns']),
            'gap' => intval($atts['gap']),
            'section' => $section,
            'track_stores' => true
        ));
    }

    return ob_get_clean();
}
add_shortcode('stores', 'stores_shortcode');

/**
 * Utility Functions for Store Deduplication Management
 */

/**
 * Get store tracker instance
 */
function ag_get_store_tracker() {
    return AG_Store_Tracker::get_instance();
}

/**
 * Reset store tracking (useful for testing)
 */
function ag_reset_store_tracking() {
    AG_Store_Tracker::get_instance()->reset();
}

/**
 * Get store tracking statistics
 */
function ag_get_store_stats() {
    return AG_Store_Tracker::get_instance()->get_stats();
}

/**
 * Check if store is already displayed
 */
function ag_is_store_displayed($store_id) {
    return AG_Store_Tracker::get_instance()->is_store_displayed($store_id);
}

/**
 * Debug function to display store tracking info (for development)
 */
function ag_debug_store_tracking() {
    if (!WP_DEBUG) return;

    $tracker = AG_Store_Tracker::get_instance();
    $stats = $tracker->get_stats();

    echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">';
    echo '<h4>Store Tracking Debug Info:</h4>';
    echo '<p><strong>Total Displayed:</strong> ' . $stats['total_displayed'] . '</p>';
    echo '<p><strong>Unique Stores:</strong> ' . $stats['unique_stores'] . '</p>';
    echo '<p><strong>Duplicates Prevented:</strong> ' . $stats['duplicates_prevented'] . '</p>';
    echo '<p><strong>Sections:</strong> ' . implode(', ', $stats['sections']) . '</p>';
    echo '<p><strong>Displayed Store IDs:</strong> ' . implode(', ', $tracker->get_displayed_stores()) . '</p>';
    echo '</div>';
}

/**
 * Enhanced store functions with backward compatibility
 * These maintain the old function signatures while adding deduplication
 */

/**
 * Backward compatible get_featured_stores (maintains old signature)
 */
function get_featured_stores_legacy($number = 12) {
    return get_featured_stores($number, false); // Don't exclude displayed for legacy calls
}

/**
 * Backward compatible get_all_stores (maintains old signature)
 */
function get_all_stores_legacy($number = 12, $exclude = array()) {
    return get_all_stores($number, $exclude, false); // Don't exclude displayed for legacy calls
}
