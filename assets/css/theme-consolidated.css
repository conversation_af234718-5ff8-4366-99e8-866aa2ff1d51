/*
 * Ag-Coupon Theme - Consolidated Styles
 *
 * This file combines all theme styles for optimal performance:
 * - Base Tailwind CSS
 * - Enhanced Components
 * - Mobile Optimizations
 * - Performance Enhancements
 *
 * Color Scheme: Yellow (#FCD34D) & Dark Blue (#1E40AF)
 */

/* ==========================================================================
   ENHANCED MEGA MENU STYLES
   ========================================================================== */

/* ==========================================================================
   MEGA MENU CRITICAL FIXES - MERGED
   ========================================================================== */

/* Navigation Container */
.desktop-navigation {
    position: relative;
    z-index: 40;
}

.nav-menu {
    position: relative;
    z-index: 40;
}

/* Navigation Items */
.nav-item {
    position: relative;
}

.nav-item.group {
    position: relative;
}

/* Mega Menu Container - Full Width with Critical Fixes */
.mega-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    border-top: 4px solid #FCD34D !important;
    border-radius: 0 0 1.5rem 1.5rem !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 50 !important;
    overflow: hidden !important;
    pointer-events: none !important;
    max-height: 0 !important;
}

/* Hover States - Multiple Selectors for Maximum Compatibility */
.nav-item:hover .mega-menu,
.nav-item.group:hover .mega-menu,
.group:hover .mega-menu,
li.group:hover .mega-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Keep mega menu open when hovering over it */
.mega-menu:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Navigation Link Hover Indicator */
.nav-item:hover > .nav-link,
.nav-item.group:hover > .nav-link {
    color: #1E40AF !important;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2)) !important;
}

/* Dropdown Arrow Animation */
.nav-item:hover .nav-link svg,
.nav-item.group:hover .nav-link svg {
    transform: rotate(180deg) !important;
}

/* ==========================================================================
   GENERAL THEME STYLES (NO MEGA MENU)
   ========================================================================== */



/* Item Badges */
.menu-item-enhanced .item-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2));
    color: #92400E;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.menu-item-enhanced a:hover .item-badge {
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.2), rgba(252, 211, 77, 0.3));
    transform: scale(1.05);
}

/* Arrow Indicators */
.menu-item-enhanced .arrow-indicator {
    margin-left: auto;
    margin-right: 0.5rem;
    opacity: 0;
    transform: translateX(0.5rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-enhanced a:hover .arrow-indicator {
    opacity: 1;
    transform: translateX(0);
}



/* ==========================================================================
   ENHANCED COUPON CARD SYSTEM - UNIFIED
   ========================================================================== */

/* Note: Old coupon card styles removed. Using only enhanced ag-coupon-card system. */

/* Coupon Button Gradients */
.coupon-button.btn-code {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
}

.coupon-button.btn-sale {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.coupon-button.btn-print {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* ==========================================================================
   COUPON TYPE FILTER SYSTEM
   ========================================================================== */

/* Filter Tabs Container */
.filter-coupons-by-type {
    position: relative;
    z-index: 10;
}

/* Filter Tab Buttons */
.filter-coupons-by-type .tab-button {
    position: relative;
    cursor: pointer;
    border: none;
    outline: none;
    user-select: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-coupons-by-type .tab-button:focus {
    outline: 2px solid #FCD34D;
    outline-offset: 2px;
}

/* Active Tab Button */
.filter-coupons-by-type .tab-button.active {
    position: relative;
    z-index: 2;
}

.filter-coupons-by-type .tab-button.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    border-radius: 0.5rem;
    opacity: 0.1;
    z-index: -1;
}

/* Coupon Grid Items - Filter Animation */
.coupon-grid-item {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
    transform: scale(1);
}

.coupon-grid-item.hidden {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
}

/* Smooth Grid Layout Adjustments */
.store-coupons-grid .st-list-coupons {
    transition: all 0.3s ease;
}

/* Filter Animation Keyframes */
@keyframes filterFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes filterFadeOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-10px) scale(0.98);
    }
}

/* Apply animations to filtered items */
.coupon-grid-item:not(.hidden) {
    animation: filterFadeIn 0.4s ease forwards;
}

.coupon-grid-item.hidden {
    animation: filterFadeOut 0.3s ease forwards;
}

/* Tab Button Count Badges */
.filter-coupons-by-type .tab-button span {
    transition: all 0.3s ease;
}

.filter-coupons-by-type .tab-button.active span {
    transform: scale(1.05);
}

/* Responsive Filter Tabs */
@media (max-width: 640px) {
    .filter-coupons-by-type .flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-coupons-by-type .tab-button {
        width: 100%;
        justify-content: center;
    }
}

/* ==========================================================================
   ENHANCED RATING SYSTEM STYLES
   ========================================================================== */

/* Rating System Container */
.wpcoupon-rating-system {
    position: relative;
    overflow: hidden;
}

.wpcoupon-rating-system::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.02), rgba(30, 64, 175, 0.02));
    border-radius: 0.5rem;
    z-index: -1;
}

/* Interactive Stars Container */
.wpcoupon-interactive-stars {
    position: relative;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.wpcoupon-interactive-stars:hover {
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.05), rgba(30, 64, 175, 0.05));
    transform: scale(1.02);
}

/* Enhanced Star Styling */
.wpcoupon-star-interactive {
    position: relative;
    margin: 0 2px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.wpcoupon-star-interactive:hover {
    filter: drop-shadow(0 4px 8px rgba(252, 211, 77, 0.3));
    transform: scale(1.15) rotate(5deg);
}

.wpcoupon-star-interactive:active {
    transform: scale(1.05) rotate(2deg);
}

/* RTL Support for Stars */
[dir="rtl"] .wpcoupon-star-interactive {
    margin: 0 2px;
}

[dir="rtl"] .wpcoupon-star-interactive:hover {
    transform: scale(1.15) rotate(-5deg);
}

[dir="rtl"] .wpcoupon-star-interactive:active {
    transform: scale(1.05) rotate(-2deg);
}

/* Star Color and Fill Transitions */
.wpcoupon-star-interactive.text-yellow-400 {
    color: #fbbf24 !important;
}

.wpcoupon-star-interactive.fill-yellow-400 {
    fill: #fbbf24 !important;
}

.wpcoupon-star-interactive.text-yellow-300 {
    color: #fcd34d !important;
}

.wpcoupon-star-interactive.fill-yellow-300 {
    fill: #fcd34d !important;
}

.wpcoupon-star-interactive.text-gray-300 {
    color: #d1d5db !important;
}

.wpcoupon-star-interactive.fill-gray-300 {
    fill: #d1d5db !important;
}

/* Hover State Colors and Fills */
.wpcoupon-star-interactive:hover.text-gray-300 {
    color: #fde68a !important;
}

.wpcoupon-star-interactive:hover.fill-gray-300 {
    fill: #fde68a !important;
}

/* Loop Rating Stars - Ensure proper fill colors */
.wpcoupon-loop-rating .wpcoupon-rating-stars svg.text-yellow-400 {
    color: #fbbf24 !important;
    fill: #fbbf24 !important;
}

.wpcoupon-loop-rating .wpcoupon-rating-stars svg.text-gray-300 {
    color: #d1d5db !important;
    fill: #d1d5db !important;
}

/* Display Rating Stars - Ensure proper fill colors */
.wpcoupon-rating-stars svg.text-yellow-400 {
    color: #fbbf24 !important;
    fill: #fbbf24 !important;
}

.wpcoupon-rating-stars svg.text-gray-300 {
    color: #d1d5db !important;
    fill: #d1d5db !important;
}

/* Rating Submitted State */
.rating-submitted .wpcoupon-star-interactive {
    cursor: default !important;
    pointer-events: none;
}

.rating-submitted .wpcoupon-star-interactive:hover {
    transform: none !important;
    filter: none !important;
}

/* Loading Animation */
.wpcoupon-rating-loading {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Success/Error Messages */
.wpcoupon-rating-message {
    animation: slideIn 0.4s ease;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-top: 0.75rem;
}

.wpcoupon-rating-message.text-green-600 {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.wpcoupon-rating-message.text-red-600 {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    border: 1px solid rgba(239, 68, 68, 0.2);
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Compact Rating for Loop Cards */
.wpcoupon-loop-rating {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid rgba(252, 211, 77, 0.2);
}

.wpcoupon-loop-rating .wpcoupon-rating-stars svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Responsive Rating System */
@media (max-width: 640px) {
    .wpcoupon-interactive-stars {
        padding: 0.75rem;
    }

    .wpcoupon-star-interactive {
        margin: 0 3px;
    }

    .wpcoupon-rating-system {
        padding: 1rem;
    }
}

/* Accessibility Enhancements */
.wpcoupon-star-interactive:focus {
    outline: 2px solid #FCD34D;
    outline-offset: 2px;
    border-radius: 0.25rem;
}

.wpcoupon-star-interactive:focus:not(:focus-visible) {
    outline: none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .wpcoupon-star-interactive.text-yellow-400 {
        color: #f59e0b !important;
    }

    .wpcoupon-star-interactive.text-gray-300 {
        color: #6b7280 !important;
    }
}

/* ==========================================================================
   ENHANCED FEATURED STORES SECTION
   ========================================================================== */

/* Featured Stores Section */
.featured-stores-section {
    position: relative;
    overflow: hidden;
}

.featured-stores-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.03), rgba(30, 64, 175, 0.03));
    z-index: 0;
}

.featured-stores-section > .container {
    position: relative;
    z-index: 1;
}

/* Enhanced Store Card */
.enhanced-store-card {
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-store-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.02), rgba(30, 64, 175, 0.02));
    border-radius: 1.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.enhanced-store-card:hover::before {
    opacity: 1;
}

/* Store Logo Container Animations */
.store-logo-container {
    perspective: 1000px;
}

.store-logo-container > div:first-child {
    transform-style: preserve-3d;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-store-card:hover .store-logo-container > div:first-child {
    transform: rotate3d(0, 1, 0, 15deg) rotate(12deg);
}

/* Store Logo Image Effects */
.enhanced-store-card img {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(1) saturate(1);
}

.enhanced-store-card:hover img {
    filter: brightness(1.1) saturate(1.2);
    transform: scale(1.1) rotate(2deg);
}

/* Featured Badge Animation */
.enhanced-store-card .absolute.top-4.right-4 > div {
    animation: pulse 2s infinite;
    transform-origin: center;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* Button Hover Effects */
.enhanced-store-card a[class*="bg-gradient-to-r"] {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-store-card a[class*="bg-gradient-to-r"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.enhanced-store-card:hover a[class*="bg-gradient-to-r"]::before {
    left: 100%;
}

/* Coupon Count Icon Animation */
.enhanced-store-card .w-8.h-8 {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-store-card:hover .w-8.h-8 {
    transform: rotate(10deg) scale(1.1);
    background: linear-gradient(135deg, #FCD34D, #3B82F6);
}

/* Rating Stars in Store Cards */
.enhanced-store-card .wpcoupon-rating-stars {
    transition: all 0.3s ease;
}

.enhanced-store-card:hover .wpcoupon-rating-stars {
    transform: scale(1.05);
}

.enhanced-store-card .wpcoupon-rating-stars svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.enhanced-store-card:hover .wpcoupon-rating-stars svg {
    filter: drop-shadow(0 2px 4px rgba(252, 211, 77, 0.3));
}

/* Grid Layout Enhancements */
.featured-stores-grid-container {
    position: relative;
}

.featured-stores-grid-container::before {
    content: '';
    position: absolute;
    top: -2rem;
    left: -2rem;
    right: -2rem;
    bottom: -2rem;
    background: radial-gradient(circle at 30% 20%, rgba(252, 211, 77, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%);
    z-index: -1;
    border-radius: 2rem;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .enhanced-store-card {
        margin-bottom: 1rem;
    }

    .enhanced-store-card:hover {
        transform: translateY(-1px);
    }

    .store-logo-container {
        width: 16rem;
        height: 4rem;
    }
}

@media (max-width: 640px) {
    .featured-stores-grid-container .grid {
        gap: 1.5rem;
    }

    .enhanced-store-card {
        border-radius: 1.5rem;
    }
}

/* Loading Animation for Store Cards */
.enhanced-store-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.enhanced-store-card:nth-child(1) { animation-delay: 0.1s; }
.enhanced-store-card:nth-child(2) { animation-delay: 0.2s; }
.enhanced-store-card:nth-child(3) { animation-delay: 0.3s; }
.enhanced-store-card:nth-child(4) { animation-delay: 0.4s; }
.enhanced-store-card:nth-child(5) { animation-delay: 0.5s; }
.enhanced-store-card:nth-child(6) { animation-delay: 0.6s; }
.enhanced-store-card:nth-child(7) { animation-delay: 0.7s; }
.enhanced-store-card:nth-child(8) { animation-delay: 0.8s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* RTL Support for Store Cards */
[dir="rtl"] .enhanced-store-card .absolute.top-4.right-4 {
    right: auto;
    left: 1rem;
}

[dir="rtl"] .enhanced-store-card:hover .store-logo-container > div:first-child {
    transform: rotate3d(0, 1, 0, -15deg) rotate(-12deg);
}

[dir="rtl"] .enhanced-store-card:hover img {
    transform: scale(1.1) rotate(-2deg);
}

/* ==========================================================================
   SPLIDE SLIDER CUSTOMIZATION FOR FEATURED STORES
   ========================================================================== */

/* Splide Container for Center Mode */
.featured-stores-slider-container {
    position: relative;
    padding: 0 3rem;
    overflow: hidden;
}

.featured-stores-slider-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(252, 211, 77, 0.05) 0%, transparent 70%);
    z-index: 1;
    pointer-events: none;
}

.featured-stores-slider {
    overflow: visible;
}

.featured-stores-slider .splide__track {
    overflow: visible;
}

.featured-stores-slider .splide__list {
    align-items: stretch;
}

.featured-stores-slider .splide__slide {
    display: flex;
    align-items: stretch;
    opacity: 0.6;
    transform: scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.featured-stores-slider .splide__slide.is-active {
    opacity: 1;
    transform: scale(1);
    z-index: 10;
}

.featured-stores-slider .splide__slide.is-next,
.featured-stores-slider .splide__slide.is-prev {
    opacity: 0.8;
    transform: scale(0.95);
}

/* Enhanced Active Center Card */
.featured-stores-slider .splide__slide.is-active .enhanced-store-card {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(252, 211, 77, 0.2) !important;
    border-color: rgba(252, 211, 77, 0.3) !important;
    position: relative;
}

.featured-stores-slider .splide__slide.is-active .enhanced-store-card::before {
    opacity: 1 !important;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.08), rgba(59, 130, 246, 0.08)) !important;
}

.featured-stores-slider .splide__slide.is-active .enhanced-store-card::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #fcd34d, #3b82f6);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.1;
}

/* Center Focus Indicator */
.featured-stores-slider .splide__slide.is-active .enhanced-store-card .store-logo-container::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.2), rgba(59, 130, 246, 0.2));
    border-radius: 50%;
    z-index: -1;
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

/* Splide Arrows with Branded Colors */
.featured-stores-slider .splide__arrow {
    background: linear-gradient(135deg, #fcd34d, #3b82f6) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 3rem !important;
    height: 3rem !important;
    opacity: 0.9 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px) !important;
}

.featured-stores-slider .splide__arrow:hover {
    opacity: 1 !important;
    transform: scale(1.1) !important;
    box-shadow: 0 6px 20px rgba(252, 211, 77, 0.4) !important;
}

.featured-stores-slider .splide__arrow:disabled {
    opacity: 0.3 !important;
}

.featured-stores-slider .splide__arrow svg {
    fill: white !important;
    width: 1.2rem !important;
    height: 1.2rem !important;
}

/* Arrow Positioning */
.featured-stores-slider .splide__arrow--prev {
    left: -1.5rem !important;
}

.featured-stores-slider .splide__arrow--next {
    right: -1.5rem !important;
}

/* RTL Arrow Positioning */
[dir="rtl"] .featured-stores-slider .splide__arrow--prev {
    right: -1.5rem !important;
    left: auto !important;
}

[dir="rtl"] .featured-stores-slider .splide__arrow--next {
    left: -1.5rem !important;
    right: auto !important;
}

/* Splide Pagination with Branded Colors */
.featured-stores-slider .splide__pagination {
    bottom: -3rem !important;
    padding: 0 !important;
}

.featured-stores-slider .splide__pagination__page {
    background: #d1d5db !important;
    border: none !important;
    border-radius: 50% !important;
    width: 0.75rem !important;
    height: 0.75rem !important;
    margin: 0 0.25rem !important;
    transition: all 0.3s ease !important;
    opacity: 0.6 !important;
}

.featured-stores-slider .splide__pagination__page.is-active {
    background: linear-gradient(135deg, #fcd34d, #3b82f6) !important;
    opacity: 1 !important;
    transform: scale(1.3) !important;
    box-shadow: 0 2px 8px rgba(252, 211, 77, 0.4) !important;
}

.featured-stores-slider .splide__pagination__page:hover {
    opacity: 0.8 !important;
    transform: scale(1.1) !important;
}

/* Responsive Slider Adjustments for Center Mode */
@media (max-width: 1200px) {
    .featured-stores-slider-container {
        padding: 0 2rem;
    }

    .featured-stores-slider .splide__slide {
        transform: scale(0.92);
    }

    .featured-stores-slider .splide__slide.is-active {
        transform: scale(1);
    }

    .featured-stores-slider .splide__slide.is-next,
    .featured-stores-slider .splide__slide.is-prev {
        transform: scale(0.96);
    }
}

@media (max-width: 1024px) {
    .featured-stores-slider-container {
        padding: 0 1.5rem;
    }

    .featured-stores-slider .splide__arrow--prev {
        left: -1rem !important;
    }

    .featured-stores-slider .splide__arrow--next {
        right: -1rem !important;
    }

    [dir="rtl"] .featured-stores-slider .splide__arrow--prev {
        right: -1rem !important;
        left: auto !important;
    }

    [dir="rtl"] .featured-stores-slider .splide__arrow--next {
        left: -1rem !important;
        right: auto !important;
    }

    .featured-stores-slider .splide__slide {
        transform: scale(0.94);
    }

    .featured-stores-slider .splide__slide.is-active {
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .featured-stores-slider-container {
        padding: 0 1rem;
    }

    .featured-stores-slider .splide__arrow {
        width: 2.5rem !important;
        height: 2.5rem !important;
    }

    .featured-stores-slider .splide__arrow svg {
        width: 1rem !important;
        height: 1rem !important;
    }

    .featured-stores-slider .splide__arrow--prev {
        left: -0.5rem !important;
    }

    .featured-stores-slider .splide__arrow--next {
        right: -0.5rem !important;
    }

    [dir="rtl"] .featured-stores-slider .splide__arrow--prev {
        right: -0.5rem !important;
        left: auto !important;
    }

    [dir="rtl"] .featured-stores-slider .splide__arrow--next {
        left: -0.5rem !important;
        right: auto !important;
    }
}

@media (max-width: 640px) {
    .featured-stores-slider-container {
        padding: 0;
    }

    .featured-stores-slider .splide__arrow {
        display: none !important;
    }

    .featured-stores-slider .splide__pagination {
        bottom: -2rem !important;
    }
}

/* Enhanced Store Card in Slider */
.featured-stores-slider .enhanced-store-card {
    height: 100%;
    min-height: 320px;
}

/* Slider Loading Animation */
.featured-stores-slider-container {
    animation: slideIn 0.8s ease forwards;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================================================================
   ENHANCED STORE HEADER & PROFILE CARD
   ========================================================================== */

/* Store Header Section */
.store-header-section {
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.store-header-section::before {
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Enhanced Store Profile Card */
.store-profile-card {
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.store-profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Store Logo Wrapper 3D Effects */
.store-logo-wrapper {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.store-logo-wrapper > div:first-child {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.store-logo-wrapper:hover > div:first-child {
    transform: rotate3d(1, 1, 0, 15deg) rotate(8deg);
}

.store-logo {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.store-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.store-logo:hover::before {
    left: 100%;
}

/* Store Rating Section */
.store-rating-section {
    animation: fadeInUp 0.8s ease 0.3s both;
}

.store-rating-section .bg-gradient-to-r {
    position: relative;
    overflow: hidden;
}

.store-rating-section .bg-gradient-to-r::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(252, 211, 77, 0.1), rgba(59, 130, 246, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.store-rating-section:hover .bg-gradient-to-r::before {
    opacity: 1;
}

/* Store Information Block */
.store-info-block {
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease 0.5s both;
}

.store-info-block::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.02), rgba(59, 130, 246, 0.02));
    z-index: 0;
}

.store-info-block > * {
    position: relative;
    z-index: 1;
}

/* Feature Items */
.feature-item {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(252, 211, 77, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-item:hover::before {
    left: 100%;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-item:hover .feature-icon {
    transform: rotate(10deg) scale(1.1);
}

/* Store Actions */
.store-actions {
    animation: fadeInUp 0.8s ease 0.4s both;
}

.store-actions .btn-primary {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #fcd34d, #3b82f6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.store-actions .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.store-actions .btn-primary:hover::before {
    left: 100%;
}

.store-actions .btn-primary:hover {
    box-shadow: 0 10px 30px rgba(252, 211, 77, 0.4);
}

/* Store Statistics */
.store-stats {
    animation: fadeInUp 0.8s ease 0.6s both;
}

.stat-item {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.05), rgba(59, 130, 246, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-item:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-item:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-value {
    transition: all 0.3s ease;
}

.stat-item:hover .stat-value {
    transform: scale(1.05);
}

/* Responsive Enhancements */
@media (max-width: 1024px) {
    .store-header-section {
        min-height: 50vh;
    }

    .store-profile-card {
        margin: 1rem;
        padding: 1.5rem;
    }

    .store-logo {
        width: 8rem;
        height: 8rem;
    }
}

@media (max-width: 768px) {
    .store-header-section {
        min-height: auto;
        padding: 2rem 0;
    }

    .store-profile-card {
        border-radius: 1.5rem;
        padding: 1rem;
    }

    .store-logo {
        width: 6rem;
        height: 6rem;
    }

    .store-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .feature-item {
        padding: 1rem;
    }
}

/* RTL Support */
[dir="rtl"] .store-logo::before {
    left: auto;
    right: -100%;
}

[dir="rtl"] .store-logo:hover::before {
    right: 100%;
    left: auto;
}

[dir="rtl"] .feature-item::before {
    left: auto;
    right: -100%;
}

[dir="rtl"] .feature-item:hover::before {
    right: 100%;
    left: auto;
}

[dir="rtl"] .store-actions .btn-primary::before {
    left: auto;
    right: -100%;
}

[dir="rtl"] .store-actions .btn-primary:hover::before {
    right: 100%;
    left: auto;
}





/* ==========================================================================
   MODERN CREATIVE STORE CARD WITH ANIMATED BACKGROUND
   Branded Colors: Yellow (#FCD34D) & Dark Blue (#1E40AF)
   ========================================================================== */

/* Base Store Card */
.ag-store-card {
    position: relative;
    background: #ffffff;
    border-radius: 24px;
    padding: 20px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    overflow: visible;
    cursor: pointer;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Animated Background Elements */
.ag-store-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(30, 64, 175, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(252, 211, 77, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 40% 60%, rgba(30, 64, 175, 0.04) 0%, transparent 30%);
    opacity: 0;
    transition: all 0.8s ease;
    animation: float 6s ease-in-out infinite;
    z-index: 1;
    border-radius: 24px;
    pointer-events: none;
}

.ag-store-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(252, 211, 77, 0.04) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(30, 64, 175, 0.04) 50%, transparent 70%);
    opacity: 0;
    transition: all 0.6s ease;
    z-index: 1;
    border-radius: 24px;
    pointer-events: none;
}

/* Hover Effects */
.ag-store-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(30, 64, 175, 0.15),
        0 15px 35px rgba(252, 211, 77, 0.1);
    border-color: rgba(252, 211, 77, 0.3);
}

.ag-store-card:hover::before {
    opacity: 1;
    animation: float 3s ease-in-out infinite;
}

.ag-store-card:hover::after {
    opacity: 1;
    animation: shimmer 2s ease-in-out infinite;
}

/* Featured Store Card */
.ag-store-card.featured {
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
    border: 2px solid #FCD34D;
    box-shadow: 0 8px 25px rgba(252, 211, 77, 0.2);
}

.ag-store-card.featured::before {
    background:
        radial-gradient(circle at 20% 20%, rgba(252, 211, 77, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(30, 64, 175, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 40% 60%, rgba(252, 211, 77, 0.08) 0%, transparent 30%);
}

.ag-store-card.featured:hover {
    transform: translateY(-16px) scale(1.03);
    box-shadow:
        0 30px 60px rgba(252, 211, 77, 0.25),
        0 20px 40px rgba(30, 64, 175, 0.15);
}

/* Store Logo - Rounded Style */
.ag-store-card .ag-store-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 16px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e5e7eb;
    transition: all 0.4s ease;
    z-index: 2;
    flex-shrink: 0;
}

.ag-store-card.featured .ag-store-logo {
    border-color: #FCD34D;
    background: linear-gradient(135deg, #fef3c7, #FCD34D);
}

.ag-store-card .ag-store-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.ag-store-card:hover .ag-store-logo {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.2);
}

.ag-store-card.featured:hover .ag-store-logo {
    box-shadow: 0 8px 25px rgba(252, 211, 77, 0.3);
}

.ag-store-card:hover .ag-store-logo img {
    transform: scale(1.05);
}

/* Store Logo Placeholder */
.ag-store-card .ag-store-logo .ag-logo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    text-transform: uppercase;
}

.ag-store-card.featured .ag-store-logo .ag-logo-placeholder {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
}

/* Store Content */
.ag-store-card .ag-store-content {
    text-align: center;
    position: relative;
    z-index: 2;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Store Name */
.ag-store-card .ag-store-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 6px;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.ag-store-card .ag-store-name a {
    text-decoration: none;
    color: inherit;
    position: relative;
}

.ag-store-card:hover .ag-store-name a {
    color: #1E40AF;
}

.ag-store-card.featured .ag-store-name a {
    color: #1E40AF;
}

/* Coupon Count */
.ag-store-card .ag-coupons-count {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: #6b7280;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 12px;
}

.ag-store-card .ag-coupons-count .ag-icon-wrapper {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.ag-store-card:hover .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    transform: scale(1.1);
}

.ag-store-card:hover .ag-coupons-count .ag-icon-wrapper svg {
    color: white;
}

.ag-store-card.featured .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #fef3c7, #FCD34D);
}

.ag-store-card.featured:hover .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
}

/* Action Buttons */
.ag-store-card .ag-store-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: auto;
}

.ag-store-card .ag-btn-primary {
    flex: 1;
    max-width: 160px;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.8rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ag-store-card .ag-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.ag-store-card:hover .ag-btn-primary::before {
    left: 100%;
}

.ag-store-card .ag-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 64, 175, 0.4);
}

.ag-store-card.featured .ag-btn-primary {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
}

.ag-store-card.featured .ag-btn-primary:hover {
    box-shadow: 0 8px 20px rgba(252, 211, 77, 0.4);
}

.ag-store-card .ag-btn-secondary {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #6b7280;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.ag-store-card .ag-btn-secondary:hover {
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    border-color: #1E40AF;
    color: white;
    transform: translateY(-2px) scale(1.05);
}

.ag-store-card.featured .ag-btn-secondary:hover {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    border-color: #FCD34D;
    color: #1E40AF;
}

/* Store Badge */
.ag-store-card .ag-store-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
    animation: pulse-badge 2s infinite;
}

.ag-store-card.featured .ag-store-badge {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
    box-shadow: 0 4px 12px rgba(252, 211, 77, 0.4);
}

/* ==========================================================================
   MODERN COUPON CARD SYSTEM
   Creative modern design with branded yellow colors
   ========================================================================== */

/* Base Modern Coupon Card */
.modern-coupon-card {
    position: relative;
    background: #fffdf2;
    border-radius: 1rem;
    border: 1px solid #f3f4f6;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    cursor: pointer;
    min-height: 320px;
    height: 100%; /* Equal height in grid */
    display: flex;
    flex-direction: column;
}

/* Modern Card Hover Effects */
.modern-coupon-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: #FCD34D;
}

/* Featured Coupon Card */
.modern-coupon-card.featured-coupon {
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
    border: 3px solid #FCD34D;
    box-shadow: 0 10px 30px rgba(252, 211, 77, 0.3);
}

.modern-coupon-card.featured-coupon:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 30px 60px rgba(252, 211, 77, 0.4);
}



/* Modern Animated Background Elements */
.modern-coupon-card .absolute.inset-0 {
    pointer-events: none;
    z-index: 1;
}

/* Modern Store Logo Effects */
.modern-coupon-card .w-12.h-12 {
    transition: all 0.3s ease;
}

.modern-coupon-card:hover .w-12.h-12 {
    transform: scale(1.05) rotate(2deg);
}

/* Modern Badges */
.modern-coupon-card .bg-gradient-to-r {
    animation: modern-badge-glow 3s infinite;
    transition: all 0.3s ease;
}

@keyframes modern-badge-glow {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.02) rotate(1deg);
        box-shadow: 0 4px 16px rgba(252, 211, 77, 0.3);
    }
}

/* Modern Coupon Title */
.modern-coupon-card h3 {
    transition: all 0.3s ease;
}

.modern-coupon-card h3 a {
    text-decoration: none;
}

.modern-coupon-card h3 a:hover {
    color: #1E40AF;
}

/* Modern Coupon Image */
.modern-coupon-card img {
    transition: transform 0.7s ease;
}

/* Modern Voting Section */
.modern-coupon-card .vote-up,
.modern-coupon-card .vote-down {
    transition: all 0.3s ease;
}

.modern-coupon-card .vote-up:hover,
.modern-coupon-card .vote-down:hover {
    transform: scale(1.1);
}

/* Modern Action Buttons */
.modern-coupon-card .coupon-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}



.modern-coupon-card .coupon-button:hover::before {
    left: 100%;
}

/* Responsive Design for Modern Cards */
@media (max-width: 768px) {
    .modern-coupon-card {
        min-height: 280px;
    }

    .modern-coupon-card:hover {
        transform: translateY(-4px) scale(1.01);
    }
}

/* Scratch-to-Reveal Effect */
.ag-scratch-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.ag-scratch-button {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.ag-scratch-button:hover {
    transform: scale(1.05);
}

.ag-scratch-button.scratching {
    animation: scratch-reveal 0.8s ease-out forwards;
}

@keyframes scratch-reveal {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1) rotateY(90deg);
        opacity: 0.5;
    }
    100% {
        transform: scale(1) rotateY(180deg);
        opacity: 0;
    }
}

.ag-coupon-code-hidden.revealed {
    opacity: 1;
    z-index: 3;
    animation: code-reveal 0.8s ease-out 0.4s forwards;
}

@keyframes code-reveal {
    0% {
        transform: scale(0.8) rotateY(-180deg);
        opacity: 0;
    }
    100% {
        transform: scale(1) rotateY(0deg);
        opacity: 1;
    }
}

/* Coupon Code Display Button (creative scratch coupon style) */
.ag-coupon-code-display {
    background: linear-gradient(135deg, #FCD34D 0%, #F59E0B 100%);
    border: 2px dashed #1E40AF;
    color: #1E40AF;
    padding: 0;
    border-radius: 12px;
    font-weight: bold;
    font-size: 14px;
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    text-decoration: none;
    display: flex;
    align-items: stretch;
    min-height: 56px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ag-coupon-code-display:hover {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4);
}

/* Scratch overlay effect */
.ag-coupon-code-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            45deg,
            rgba(30, 64, 175, 0.05) 0px,
            rgba(30, 64, 175, 0.05) 2px,
            transparent 2px,
            transparent 6px
        ),
        repeating-linear-gradient(
            -45deg,
            rgba(30, 64, 175, 0.05) 0px,
            rgba(30, 64, 175, 0.05) 2px,
            transparent 2px,
            transparent 6px
        );
    pointer-events: none;
    z-index: 1;
}

/* Left section with action text */
.ag-coupon-code-display .coupon-left-section {
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    position: relative;
    z-index: 2;
}

/* Right section with code */
.ag-coupon-code-display .coupon-right-section {
    background: rgba(30, 64, 175, 0.1);
    border-left: 2px dashed #1E40AF;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    position: relative;
    z-index: 2;
}

/* Scratch effect on code section */
.ag-coupon-code-display .coupon-right-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(30, 64, 175, 0.1) 20%,
        rgba(30, 64, 175, 0.2) 40%,
        rgba(30, 64, 175, 0.3) 60%,
        rgba(30, 64, 175, 0.2) 80%,
        transparent 100%
    );
    pointer-events: none;
    z-index: 1;
}

.ag-coupon-code-display .coupon-action-text {
    font-size: 10px;
    font-weight: 600;
    opacity: 0.9;
    color: #1E40AF;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
    margin-bottom: 2px;
    text-transform: none;
    letter-spacing: 0.5px;
}

.ag-coupon-code-display .coupon-code-text {
    font-size: 16px;
    font-weight: 900;
    letter-spacing: 2px;
    position: relative;
    z-index: 2;
    color: #1E40AF;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.3);
    /* Scratch effect - partially hidden */
    background: linear-gradient(
        90deg,
        #1E40AF 0%,
        #1E40AF 40%,
        rgba(30, 64, 175, 0.6) 60%,
        rgba(30, 64, 175, 0.3) 80%,
        rgba(30, 64, 175, 0.1) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Hover effect reveals more of the code */
.ag-coupon-code-display:hover .coupon-code-text {
    background: linear-gradient(
        90deg,
        #1E40AF 0%,
        #1E40AF 70%,
        rgba(30, 64, 175, 0.8) 85%,
        rgba(30, 64, 175, 0.5) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Fallback for browsers that don't support background-clip: text */
@supports not (-webkit-background-clip: text) {
    .ag-coupon-code-display .coupon-code-text {
        color: #1E40AF;
        opacity: 0.7;
    }

    .ag-coupon-code-display:hover .coupon-code-text {
        opacity: 1;
    }
}

/* Copy indicator */
.ag-coupon-code-display .copy-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(30, 64, 175, 0.8);
    color: white;
    font-size: 8px;
    padding: 2px 6px;
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
}

.ag-coupon-code-display:hover .copy-indicator {
    opacity: 1;
}

/* Deal Button */
.ag-deal-button {
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 48px;
    cursor: pointer;
}

.ag-deal-button:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Regular Coupon Button (fallback) */
.ag-coupon-button {
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ag-coupon-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.ag-coupon-button:hover::before {
    left: 100%;
}

/* Expiration Info */
.ag-coupon-expiry {
    font-size: 11px;
}

/* Expired Coupon Styling */
.ag-coupon-card.ag-coupon-expired {
    filter: grayscale(50%);
}

.ag-coupon-card.ag-coupon-expired .ag-coupon-title {
    text-decoration: line-through;
}

.ag-coupon-card.ag-coupon-expired .ag-coupon-button {
    background: #9ca3af !important;
    cursor: not-allowed;
}

/* Load More Button */
.ag-load-more-coupons {
    position: relative;
    overflow: hidden;
}

.ag-load-more-coupons::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.ag-load-more-coupons:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ag-coupon-card {
        min-height: 240px;
    }

    .ag-coupon-card:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .ag-coupon-title {
        font-size: 1rem;
    }

    .ag-coupon-description {
        font-size: 0.875rem;
    }
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

@keyframes pulse-badge {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ==========================================================================
   COUPON VOTING AND REVEAL CONTENT STYLES
   ========================================================================== */

/* Reveal Content */
.reveal-content {
    display: none;
}

.reveal-content.active {
    display: block !important;
}

/* Coupon Voting */
.coupon-vote.active {
    color: #10b981 !important;
    background-color: #d1fae5 !important;
}

.coupon-vote[data-vote-type="down"].active {
    color: #ef4444 !important;
    background-color: #fee2e2 !important;
}

.coupon-vote-wrapper.voted .coupon-vote:not(.active) {
    opacity: 0.5;
    pointer-events: none;
}

/* ==========================================================================
   READ MORE FUNCTIONALITY STYLES
   ========================================================================== */

/* Store Content with Read More */
.store-content {
    max-height: 8rem; /* 32 * 0.25rem = 8rem (128px) */
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.store-content.expanded {
    max-height: none;
}

/* Fade effect for collapsed content */
.store-content:not(.expanded)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rem;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.9));
    pointer-events: none;
}

/* Read More Button */
.read-more-btn {
    color: #0d9488 !important; /* secondary-600 */
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: none;
    background: transparent;
    padding: 0;
    text-decoration: underline;
    text-underline-offset: 2px;
    text-decoration-thickness: 1px;
}

.read-more-btn:hover {
    color: #0f766e !important; /* secondary-700 */
    text-decoration: none;
    transform: translateX(2px);
}

.read-more-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(13, 148, 136, 0.3);
    border-radius: 0.25rem;
}

/* ==========================================================================
   END OF THEME STYLES
   ========================================================================== */
