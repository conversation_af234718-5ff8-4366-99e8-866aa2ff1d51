/* ==========================================================================
   ACCESSIBLE MEGA MENU - 3 COLUMNS, CLICKABLE ITEMS, WCAG 2.1 AA COMPLIANT
   ========================================================================== */

/* Desktop Navigation - Hidden on Mobile */
.desktop-navigation {
    display: none !important;
}

@media (min-width: 1024px) {
    .desktop-navigation {
        display: block !important;
        background: linear-gradient(to right, rgba(249, 250, 251, 0.95), rgba(255, 255, 255, 0.95), rgba(249, 250, 251, 0.95)) !important;
        border-top: 1px solid rgba(229, 231, 235, 0.8) !important;
        position: relative !important;
        z-index: 40 !important;
    }
}

/* Navigation Menu */
.nav-menu {
    position: relative;
    z-index: 40;
}

.nav-menu ul {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    margin: 0;
    list-style: none;
}

/* Navigation Items */
.nav-item {
    position: relative;
}

/* Accessible Navigation Links */
.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    position: relative;
    outline: none;
    border: 2px solid transparent;
}

.nav-link:hover,
.nav-link:focus {
    color: #1E40AF;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2));
}

.nav-link:focus {
    border-color: #1E40AF;
    box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.2);
}

.nav-link svg {
    width: 1rem;
    height: 1rem;
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}

.nav-item.group:hover .nav-link svg,
.nav-item.group:focus-within .nav-link svg {
    transform: rotate(180deg);
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    background: #1E40AF;
    color: white;
    border-radius: 0.25rem;
}

/* ==========================================================================
   MEGA MENU - 3 COLUMN LAYOUT
   ========================================================================== */

/* Mega Menu Container */
.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: white;
    border-top: 4px solid #1E40AF;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    opacity: 0;
    visibility: hidden;
    transform: translateY(8px);
    transition: all 0.3s ease;
    z-index: 50;
}

.group:hover .mega-menu,
.group:focus-within .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 3 Column Grid Layout */
.mega-menu .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

.mega-menu .grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

/* Column Styling */
.mega-column {
    padding: 0.5rem;
}

/* Accessible Column Title Links */
.column-title-link {
    font-size: 0.75rem;
    font-weight: 600;
    color: #1E40AF;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    display: inline-block;
    text-decoration: none;
    transition: all 0.2s ease;
    outline: none;
}

.column-title-link:hover,
.column-title-link:focus {
    background: rgba(252, 211, 77, 0.2);
    color: #1E40AF;
    transform: translateY(-1px);
}

.column-title-link:focus {
    border-color: #1E40AF;
    box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.2);
}

/* Column Items Container */
.column-items {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* ==========================================================================
   CLICKABLE MENU ITEMS - NO WRAPPERS
   ========================================================================== */

/* Accessible Menu Items - Fully Clickable */
.column-items a {
    display: flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    text-decoration: none;
    color: #6B7280;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    outline: none;
    border: 2px solid transparent;
    min-height: 44px; /* WCAG 2.1 AA minimum touch target */
}

.column-items a:hover,
.column-items a:focus {
    color: #1E40AF;
    background: rgba(252, 211, 77, 0.1);
}

.column-items a:focus {
    border-color: #1E40AF;
    box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.2);
}

/* Icons in menu items */
.column-items a svg,
.column-items a img,
.column-items a i {
    width: 0.875rem;
    height: 0.875rem;
    margin-right: 0.5rem;
    flex-shrink: 0;
    color: #9CA3AF;
    transition: color 0.2s ease;
}

.column-items a:hover svg,
.column-items a:hover img,
.column-items a:hover i {
    color: #FCD34D;
}

/* Text in menu items */
.column-items a span {
    font-weight: 400;
    line-height: 1.2;
}

/* ==========================================================================
   MOBILE OVERLAY STYLES
   ========================================================================== */

/* Mobile Search Overlay */
#mobile-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 60;
}

/* Mobile Menu Overlay */
#mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 60;
}

#mobile-menu-panel {
    background: linear-gradient(to bottom, #ffffff, #f9fafb);
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.15);
	height: 100vh;
	overflow-y: auto;
	overflow-x: hidden;
}

/* Mobile Menu Items */
.mobile-item-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    text-decoration: none;
    color: #374151;
    transition: all 0.2s ease;
    border-radius: 0.375rem;
}

.mobile-item-link:hover {
    background: rgba(252, 211, 77, 0.1);
    color: #1E40AF;
}

.mobile-item-link img,
.mobile-item-link svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.mobile-item-link span {
    font-size: 0.875rem;
    font-weight: 400;
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

/* Tablet and smaller */
@media (max-width: 1024px) {
    .mega-menu .grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .mega-menu .grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .mega-menu .container {
        padding: 0.75rem;
    }

    .column-items a {
        padding: 0.375rem 0.5rem;
        font-size: 0.8125rem;
    }

    .column-items a svg,
    .column-items a img,
    .column-items a i {
        width: 0.75rem;
        height: 0.75rem;
        margin-right: 0.375rem;
    }
}

/* Extra small mobile */
@media (max-width: 480px) {
    .mega-menu .container {
        padding: 0.5rem;
    }

    .column-items a {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
}
