/**
 * Compatibility Fix for Ag-Coupon Theme
 * 
 * This file ensures that all existing global.js functions work properly
 * and fixes any issues with AJAX, voting, copy, and affiliate links
 */

jQuery(document).ready(function($) {
    'use strict';

    // Ensure ST object exists
    if (typeof ST === 'undefined') {
        console.warn('ST object not found. Some functionality may not work.');
        return;
    }

    /**
     * Fix Copy Functionality
     */
    function fixCopyFunctionality() {
        // Enhanced copyText function with modern clipboard API
        window.copyText = async function(text) {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    showCopySuccess();
                    return true;
                } else {
                    return fallbackCopy(text);
                }
            } catch (error) {
                console.warn('Copy failed:', error);
                return fallbackCopy(text);
            }
        };

        // Fallback copy method
        function fallbackCopy(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'absolute';
            textarea.style.opacity = '0';
            textarea.style.top = '-10000px';
            
            document.body.appendChild(textarea);
            textarea.select();
            
            let successful = false;
            try {
                successful = document.execCommand('copy');
                if (successful) showCopySuccess();
            } catch (e) {
                successful = false;
            }
            
            document.body.removeChild(textarea);
            return successful;
        }

        // Show copy success feedback
        function showCopySuccess() {
            // Find copy button and update it
            const copyBtns = document.querySelectorAll('.coupon-button, .get-code');
            copyBtns.forEach(btn => {
                if (btn.textContent.includes('عرض') || btn.textContent.includes('كود')) {
                    const originalText = btn.textContent;
                    btn.textContent = 'تم النسخ!';
                    btn.style.backgroundColor = '#10b981';
                    
                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.backgroundColor = '';
                    }, 2000);
                }
            });

            // Show toast notification
            showToast('تم نسخ الكود بنجاح!', 'success');
        }

        // Enhanced copyToClipBoard function
        window.copyToClipBoard = function() {
            const content = document.getElementById("code-text");
            if (!content) return false;
            
            const text = content.value || content.textContent;
            return window.copyText(text);
        };
    }

    /**
     * Fix Affiliate Links
     */
    function fixAffiliateLinks() {
        // Remove existing handlers to prevent conflicts
        $('body').off('click', '.coupon-button, .coupon-link');
        
        // Add enhanced click handler
        $('body').on('click', '.coupon-button, .coupon-link', function(e) {
            e.preventDefault();
            
            const $this = $(this);
            const couponId = $this.data('coupon-id') || $this.attr('data-coupon-id');
            const affUrl = $this.data('aff-url') || $this.attr('data-aff-url');
            const currentUrl = $this.attr('href');
            const type = $this.data('type') || $this.attr('data-type') || '';
            const code = $this.data('code') || $this.attr('data-code') || '';
            
            console.log('Coupon clicked:', { couponId, affUrl, currentUrl, type, code });
            
            // Copy code to clipboard if available
            if (code && code !== '') {
                window.copyText(code);
            }
            
            // Handle different coupon types
            const shouldPreventTab = (
                (type === 'print' && ST.print_prev_tab != 1) ||
                (type === 'code' && ST.code_prev_tab != 1) ||
                (type === 'sale' && ST.sale_prev_tab != 1)
            );
            
            // Open links based on configuration
            if (shouldPreventTab) {
                // Open modal or single page
                if (typeof openCouponModal === 'function' && couponId) {
                    openCouponModal(couponId);
                } else if (currentUrl) {
                    window.open(currentUrl, '_self');
                }
            } else {
                // Open affiliate URL and coupon page in new tab
                if (affUrl) {
                    window.open(affUrl, '_self');
                }
                if (currentUrl) {
                    window.open(currentUrl, '_blank');
                }
            }
        });
    }

    /**
     * Fix AJAX Functionality
     */
    function fixAjaxFunctionality() {
        // Add global AJAX error handler
        $(document).ajaxError(function(event, xhr, settings, error) {
            console.warn('AJAX Error:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });

        // Add loading states to AJAX buttons
        $('body').on('click', '.coupon-vote, .coupon-save, .load-more .button', function() {
            const $btn = $(this);
            if (!$btn.hasClass('loading')) {
                $btn.addClass('loading');
                
                // Remove loading state after timeout (fallback)
                setTimeout(() => {
                    $btn.removeClass('loading');
                }, 5000);
            }
        });

        // Enhance existing AJAX success handlers
        $(document).ajaxSuccess(function(event, xhr, settings, data) {
            // Remove loading states
            $('.loading').removeClass('loading');
            
            // Show success messages for specific actions
            if (settings.data && typeof settings.data === 'string') {
                if (settings.data.includes('st_doing=vote_coupon')) {
                    showToast('تم التصويت بنجاح', 'success');
                } else if (settings.data.includes('st_doing=save_coupon')) {
                    showToast('تم حفظ الكوبون', 'success');
                } else if (settings.data.includes('st_doing=remove_saved_coupon')) {
                    showToast('تم إلغاء حفظ الكوبون', 'info');
                }
            }
        });
    }

    /**
     * Fix Voting Functionality
     */
    function fixVotingFunctionality() {
        // Ensure voting initialization works
        if (typeof voteCouponInit === 'function') {
            voteCouponInit($('body'));
        }

        // Re-initialize voting for dynamically loaded content
        $(document).on('DOMNodeInserted', function(e) {
            if ($(e.target).find('.coupon-vote').length > 0) {
                if (typeof voteCouponInit === 'function') {
                    voteCouponInit($(e.target));
                }
            }
        });
    }

    /**
     * Show Toast Notification
     */
    function showToast(message, type = 'info') {
        // Remove existing toasts
        $('.ag-toast').remove();
        
        const toast = $('<div>', {
            class: `ag-toast ag-toast-${type}`,
            text: message,
            css: {
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: 9999,
                padding: '12px 20px',
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                maxWidth: '300px',
                wordWrap: 'break-word',
                transform: 'translateX(100%)',
                transition: 'transform 0.3s ease'
            }
        });
        
        // Set colors based on type
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            info: '#3b82f6',
            warning: '#f59e0b'
        };
        
        toast.css('backgroundColor', colors[type] || colors.info);
        
        $('body').append(toast);
        
        // Animate in
        setTimeout(() => {
            toast.css('transform', 'translateX(0)');
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            toast.css('transform', 'translateX(100%)');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }

    /**
     * Fix Load More Functionality
     */
    function fixLoadMoreFunctionality() {
        // Ensure load more buttons work properly
        $('body').on('click', '.load-more .button', function(e) {
            const $btn = $(this);
            
            // Prevent double clicks
            if ($btn.hasClass('loading')) {
                e.preventDefault();
                return false;
            }
        });
    }

    /**
     * Initialize All Fixes
     */
    function initializeAllFixes() {
        fixCopyFunctionality();
        fixAffiliateLinks();
        fixAjaxFunctionality();
        fixVotingFunctionality();
        fixLoadMoreFunctionality();
        
        console.log('✅ Ag-Coupon compatibility fixes initialized');
    }

    // Initialize fixes
    initializeAllFixes();

    // Re-initialize on AJAX content load
    $(document).on('st_coupon_content_loaded', function() {
        initializeAllFixes();
    });

    // Make functions globally available
    window.showToast = showToast;
    window.agCompatibilityFix = {
        fixCopyFunctionality,
        fixAffiliateLinks,
        fixAjaxFunctionality,
        fixVotingFunctionality,
        showToast
    };
});

// Ensure compatibility with global.js
document.addEventListener('DOMContentLoaded', function() {
    // Wait for global.js to load
    setTimeout(() => {
        if (typeof jQuery !== 'undefined' && typeof ST !== 'undefined') {
            console.log('🔧 Ag-Coupon compatibility layer active');
        }
    }, 500);
});
