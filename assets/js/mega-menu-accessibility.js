/**
 * Mega Menu Accessibility Enhancement
 * 
 * Provides keyboard navigation and ARIA support for the mega menu
 */

(function($) {
    'use strict';

    class MegaMenuAccessibility {
        constructor() {
            this.currentFocus = -1;
            this.menuItems = [];
            this.isMenuOpen = false;
            this.init();
        }

        init() {
            this.bindKeyboardEvents();
            this.setupARIA();
            this.setupFocusManagement();
        }

        bindKeyboardEvents() {
            // Main navigation keyboard events
            $('.nav-link').on('keydown', this.handleNavKeydown.bind(this));
            
            // Mega menu keyboard events
            $('.mega-menu a').on('keydown', this.handleMegaMenuKeydown.bind(this));
            
            // Focus events
            $('.nav-item').on('focusin', this.handleFocusIn.bind(this));
            $('.nav-item').on('focusout', this.handleFocusOut.bind(this));
        }

        setupARIA() {
            // Set up ARIA attributes for mega menu
            $('.nav-item').each((index, item) => {
                const $item = $(item);
                const $link = $item.find('.nav-link');
                const $megaMenu = $item.find('.mega-menu');
                
                if ($megaMenu.length) {
                    const menuId = 'mega-menu-' + index;
                    $megaMenu.attr('id', menuId);
                    $link.attr('aria-controls', menuId);
                    $link.attr('aria-expanded', 'false');
                    $megaMenu.attr('aria-hidden', 'true');
                }
            });

            // Set up menu items
            $('.mega-menu a').attr('tabindex', '-1');
        }

        setupFocusManagement() {
            // Focus trap for mega menu
            $(document).on('keydown', (e) => {
                if (e.key === 'Tab' && this.isMenuOpen) {
                    this.handleTabNavigation(e);
                }
            });
        }

        handleNavKeydown(e) {
            const $currentItem = $(e.currentTarget).closest('.nav-item');
            const $megaMenu = $currentItem.find('.mega-menu');
            
            switch (e.key) {
                case 'ArrowDown':
                case 'Enter':
                case ' ':
                    if ($megaMenu.length) {
                        e.preventDefault();
                        this.openMegaMenu($currentItem);
                        this.focusFirstMenuItem($megaMenu);
                    }
                    break;
                    
                case 'ArrowLeft':
                    e.preventDefault();
                    this.focusPreviousNavItem($currentItem);
                    break;
                    
                case 'ArrowRight':
                    e.preventDefault();
                    this.focusNextNavItem($currentItem);
                    break;
                    
                case 'Escape':
                    this.closeAllMenus();
                    break;
            }
        }

        handleMegaMenuKeydown(e) {
            const $currentItem = $(e.currentTarget);
            const $megaMenu = $currentItem.closest('.mega-menu');
            const $navItem = $megaMenu.closest('.nav-item');
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.focusNextMenuItem($currentItem, $megaMenu);
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    this.focusPreviousMenuItem($currentItem, $megaMenu);
                    break;
                    
                case 'ArrowLeft':
                    e.preventDefault();
                    this.focusPreviousColumn($currentItem, $megaMenu);
                    break;
                    
                case 'ArrowRight':
                    e.preventDefault();
                    this.focusNextColumn($currentItem, $megaMenu);
                    break;
                    
                case 'Escape':
                    e.preventDefault();
                    this.closeMegaMenu($navItem);
                    $navItem.find('.nav-link').focus();
                    break;
                    
                case 'Tab':
                    if (e.shiftKey) {
                        // Handle Shift+Tab
                        if ($currentItem.is($megaMenu.find('a').first())) {
                            e.preventDefault();
                            this.closeMegaMenu($navItem);
                            $navItem.find('.nav-link').focus();
                        }
                    } else {
                        // Handle Tab
                        if ($currentItem.is($megaMenu.find('a').last())) {
                            e.preventDefault();
                            this.closeMegaMenu($navItem);
                            this.focusNextNavItem($navItem);
                        }
                    }
                    break;
            }
        }

        handleFocusIn(e) {
            const $navItem = $(e.currentTarget);
            const $megaMenu = $navItem.find('.mega-menu');
            
            if ($megaMenu.length && !this.isMenuOpen) {
                this.openMegaMenu($navItem);
            }
        }

        handleFocusOut(e) {
            const $navItem = $(e.currentTarget);
            
            // Delay to check if focus moved to another element within the menu
            setTimeout(() => {
                if (!$navItem.find(':focus').length && !$navItem.is(':focus')) {
                    this.closeMegaMenu($navItem);
                }
            }, 100);
        }

        openMegaMenu($navItem) {
            const $link = $navItem.find('.nav-link');
            const $megaMenu = $navItem.find('.mega-menu');
            
            if ($megaMenu.length) {
                this.closeAllMenus();
                
                $megaMenu.removeClass('opacity-0 invisible translate-y-2')
                         .addClass('opacity-100 visible translate-y-0');
                
                $link.attr('aria-expanded', 'true');
                $megaMenu.attr('aria-hidden', 'false');
                $megaMenu.find('a').attr('tabindex', '0');
                
                this.isMenuOpen = true;
                this.currentMenuItems = $megaMenu.find('a');
            }
        }

        closeMegaMenu($navItem) {
            const $link = $navItem.find('.nav-link');
            const $megaMenu = $navItem.find('.mega-menu');
            
            if ($megaMenu.length) {
                $megaMenu.removeClass('opacity-100 visible translate-y-0')
                         .addClass('opacity-0 invisible translate-y-2');
                
                $link.attr('aria-expanded', 'false');
                $megaMenu.attr('aria-hidden', 'true');
                $megaMenu.find('a').attr('tabindex', '-1');
                
                this.isMenuOpen = false;
                this.currentMenuItems = [];
            }
        }

        closeAllMenus() {
            $('.nav-item').each((index, item) => {
                this.closeMegaMenu($(item));
            });
        }

        focusFirstMenuItem($megaMenu) {
            const $firstItem = $megaMenu.find('a').first();
            if ($firstItem.length) {
                $firstItem.focus();
            }
        }

        focusNextMenuItem($currentItem, $megaMenu) {
            const $items = $megaMenu.find('a');
            const currentIndex = $items.index($currentItem);
            const nextIndex = (currentIndex + 1) % $items.length;
            $items.eq(nextIndex).focus();
        }

        focusPreviousMenuItem($currentItem, $megaMenu) {
            const $items = $megaMenu.find('a');
            const currentIndex = $items.index($currentItem);
            const prevIndex = currentIndex === 0 ? $items.length - 1 : currentIndex - 1;
            $items.eq(prevIndex).focus();
        }

        focusNextColumn($currentItem, $megaMenu) {
            const $columns = $megaMenu.find('.mega-column');
            const $currentColumn = $currentItem.closest('.mega-column');
            const currentColumnIndex = $columns.index($currentColumn);
            const nextColumnIndex = (currentColumnIndex + 1) % $columns.length;
            const $nextColumn = $columns.eq(nextColumnIndex);
            const $firstItemInNextColumn = $nextColumn.find('a').first();
            
            if ($firstItemInNextColumn.length) {
                $firstItemInNextColumn.focus();
            }
        }

        focusPreviousColumn($currentItem, $megaMenu) {
            const $columns = $megaMenu.find('.mega-column');
            const $currentColumn = $currentItem.closest('.mega-column');
            const currentColumnIndex = $columns.index($currentColumn);
            const prevColumnIndex = currentColumnIndex === 0 ? $columns.length - 1 : currentColumnIndex - 1;
            const $prevColumn = $columns.eq(prevColumnIndex);
            const $firstItemInPrevColumn = $prevColumn.find('a').first();
            
            if ($firstItemInPrevColumn.length) {
                $firstItemInPrevColumn.focus();
            }
        }

        focusNextNavItem($currentItem) {
            const $navItems = $('.nav-item');
            const currentIndex = $navItems.index($currentItem);
            const nextIndex = (currentIndex + 1) % $navItems.length;
            $navItems.eq(nextIndex).find('.nav-link').focus();
        }

        focusPreviousNavItem($currentItem) {
            const $navItems = $('.nav-item');
            const currentIndex = $navItems.index($currentItem);
            const prevIndex = currentIndex === 0 ? $navItems.length - 1 : currentIndex - 1;
            $navItems.eq(prevIndex).find('.nav-link').focus();
        }

        handleTabNavigation(e) {
            // This method can be expanded for more complex tab navigation if needed
        }
    }

    // Initialize when DOM is ready
    $(document).ready(() => {
        new MegaMenuAccessibility();
    });

})(jQuery);
