/**
 * Enhanced Mega Menu JavaScript for Ag-Coupon Theme
 * 
 * Provides enhanced interactions, animations, and accessibility
 * for the mega menu system.
 */

(function($) {
    'use strict';

    // Mega Menu Class
    class MegaMenu {
        constructor() {
            this.init();
        }

        init() {
            this.bindEvents();
            this.setupAccessibility();
            this.setupAnimations();
            this.setupMobileHandling();
        }

        bindEvents() {
            const $navItems = $('.nav-item.group');
            
            // Desktop hover events
            $navItems.on('mouseenter', this.showMegaMenu.bind(this));
            $navItems.on('mouseleave', this.hideMegaMenu.bind(this));
            
            // Keyboard navigation
            $navItems.find('a').on('focus', this.handleFocus.bind(this));
            $navItems.find('a').on('blur', this.handleBlur.bind(this));
            
            // Click events for mobile
            $navItems.find('> a').on('click', this.handleMobileClick.bind(this));
            
            // Close mega menu on escape key
            $(document).on('keydown', this.handleEscapeKey.bind(this));
            
            // Close mega menu when clicking outside
            $(document).on('click', this.handleOutsideClick.bind(this));
        }

        showMegaMenu(event) {
            if (window.innerWidth < 1024) return; // Skip on mobile/tablet
            
            const $navItem = $(event.currentTarget);
            const $megaMenu = $navItem.find('.mega-menu');
            
            if ($megaMenu.length === 0) return;
            
            // Hide other mega menus
            $('.mega-menu').removeClass('animate-in').addClass('hidden');
            
            // Show current mega menu
            $megaMenu.removeClass('hidden').addClass('animate-in');
            
            // Add stagger animation to menu items
            $megaMenu.find('.menu-item-enhanced').each((index, item) => {
                $(item).css('animation-delay', `${index * 0.05}s`).addClass('animate-in');
            });
            
            // Update ARIA attributes
            $navItem.find('> a').attr('aria-expanded', 'true');
            $megaMenu.attr('aria-hidden', 'false');
        }

        hideMegaMenu(event) {
            if (window.innerWidth < 1024) return; // Skip on mobile/tablet
            
            const $navItem = $(event.currentTarget);
            const $megaMenu = $navItem.find('.mega-menu');
            
            if ($megaMenu.length === 0) return;
            
            // Hide mega menu
            $megaMenu.removeClass('animate-in').addClass('hidden');
            
            // Remove animation classes from menu items
            $megaMenu.find('.menu-item-enhanced').removeClass('animate-in').css('animation-delay', '');
            
            // Update ARIA attributes
            $navItem.find('> a').attr('aria-expanded', 'false');
            $megaMenu.attr('aria-hidden', 'true');
        }

        handleFocus(event) {
            const $link = $(event.currentTarget);
            const $navItem = $link.closest('.nav-item.group');
            
            if ($navItem.length && window.innerWidth >= 1024) {
                this.showMegaMenu({ currentTarget: $navItem[0] });
            }
        }

        handleBlur(event) {
            // Delay to check if focus moved to another element within the mega menu
            setTimeout(() => {
                const $activeElement = $(document.activeElement);
                const $navItem = $(event.currentTarget).closest('.nav-item.group');
                
                if (!$navItem.find($activeElement).length && !$navItem.is($activeElement)) {
                    this.hideMegaMenu({ currentTarget: $navItem[0] });
                }
            }, 100);
        }

        handleMobileClick(event) {
            if (window.innerWidth >= 1024) return; // Skip on desktop
            
            const $link = $(event.currentTarget);
            const $navItem = $link.closest('.nav-item.group');
            const $megaMenu = $navItem.find('.mega-menu');
            
            if ($megaMenu.length === 0) return;
            
            event.preventDefault();
            
            const isOpen = $megaMenu.hasClass('mobile-open');
            
            // Close all other mega menus
            $('.mega-menu').removeClass('mobile-open');
            $('.nav-item.group > a').attr('aria-expanded', 'false');
            
            if (!isOpen) {
                // Open current mega menu
                $megaMenu.addClass('mobile-open');
                $link.attr('aria-expanded', 'true');
            }
        }

        handleEscapeKey(event) {
            if (event.key === 'Escape') {
                $('.mega-menu').removeClass('animate-in mobile-open').addClass('hidden');
                $('.nav-item.group > a').attr('aria-expanded', 'false');
                $('.mega-menu').attr('aria-hidden', 'true');
            }
        }

        handleOutsideClick(event) {
            const $target = $(event.target);
            
            if (!$target.closest('.nav-item.group').length) {
                $('.mega-menu').removeClass('animate-in mobile-open').addClass('hidden');
                $('.nav-item.group > a').attr('aria-expanded', 'false');
                $('.mega-menu').attr('aria-hidden', 'true');
            }
        }

        setupAccessibility() {
            // Set initial ARIA attributes
            $('.nav-item.group > a').attr('aria-expanded', 'false');
            $('.mega-menu').attr('aria-hidden', 'true').attr('role', 'menu');
            $('.menu-item-enhanced a').attr('role', 'menuitem');
            
            // Add keyboard navigation within mega menu
            $('.mega-menu').on('keydown', this.handleMegaMenuKeydown.bind(this));
        }

        handleMegaMenuKeydown(event) {
            const $megaMenu = $(event.currentTarget);
            const $menuItems = $megaMenu.find('.menu-item-enhanced a');
            const currentIndex = $menuItems.index($(document.activeElement));
            
            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    const nextIndex = (currentIndex + 1) % $menuItems.length;
                    $menuItems.eq(nextIndex).focus();
                    break;
                    
                case 'ArrowUp':
                    event.preventDefault();
                    const prevIndex = currentIndex === 0 ? $menuItems.length - 1 : currentIndex - 1;
                    $menuItems.eq(prevIndex).focus();
                    break;
                    
                case 'Home':
                    event.preventDefault();
                    $menuItems.first().focus();
                    break;
                    
                case 'End':
                    event.preventDefault();
                    $menuItems.last().focus();
                    break;
            }
        }

        setupAnimations() {
            // Add CSS classes for animations
            const style = document.createElement('style');
            style.textContent = `
                .mega-menu.animate-in {
                    animation: megaMenuSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }
                
                .menu-item-enhanced.animate-in {
                    animation: megaMenuItemSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }
                
                @keyframes megaMenuSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                
                @keyframes megaMenuItemSlideIn {
                    from {
                        opacity: 0;
                        transform: translateX(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
                
                .mega-menu.mobile-open {
                    display: block !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    transform: translateY(0) !important;
                }
            `;
            document.head.appendChild(style);
        }

        setupMobileHandling() {
            // Handle window resize
            $(window).on('resize', this.handleResize.bind(this));
        }

        handleResize() {
            // Close all mega menus on resize
            $('.mega-menu').removeClass('animate-in mobile-open').addClass('hidden');
            $('.nav-item.group > a').attr('aria-expanded', 'false');
            $('.mega-menu').attr('aria-hidden', 'true');
        }
    }

    // Enhanced Menu Item Interactions
    class MenuItemEnhancer {
        constructor() {
            this.init();
        }

        init() {
            this.enhanceMenuItems();
            this.addHoverEffects();
        }

        enhanceMenuItems() {
            $('.menu-item-enhanced a').each((index, item) => {
                const $item = $(item);
                
                // Add loading state for external links
                if ($item.attr('href') && $item.attr('href').startsWith('http')) {
                    $item.on('click', this.handleExternalLink.bind(this));
                }
                
                // Add ripple effect
                $item.on('click', this.addRippleEffect.bind(this));
            });
        }

        handleExternalLink(event) {
            const $link = $(event.currentTarget);
            
            // Add loading indicator
            $link.addClass('loading').append('<span class="loading-spinner ml-2">⟳</span>');
            
            // Remove loading state after a short delay
            setTimeout(() => {
                $link.removeClass('loading').find('.loading-spinner').remove();
            }, 1000);
        }

        addRippleEffect(event) {
            const $button = $(event.currentTarget);
            const rect = $button[0].getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            const $ripple = $('<span class="ripple"></span>');
            $ripple.css({
                position: 'absolute',
                left: x + 'px',
                top: y + 'px',
                width: '0',
                height: '0',
                borderRadius: '50%',
                background: 'rgba(252, 211, 77, 0.3)',
                transform: 'translate(-50%, -50%)',
                animation: 'ripple 0.6s linear',
                pointerEvents: 'none'
            });
            
            $button.css('position', 'relative').append($ripple);
            
            setTimeout(() => {
                $ripple.remove();
            }, 600);
        }

        addHoverEffects() {
            // Add CSS for ripple animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        width: 100px;
                        height: 100px;
                        opacity: 0;
                    }
                }
                
                .menu-item-enhanced a.loading {
                    opacity: 0.7;
                    pointer-events: none;
                }
                
                .loading-spinner {
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Initialize when DOM is ready
    $(document).ready(() => {
        new MegaMenu();
        new MenuItemEnhancer();
    });

})(jQuery);
