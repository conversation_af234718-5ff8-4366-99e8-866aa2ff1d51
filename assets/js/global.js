/**
 * Ag-Coupon Theme - Minimal Global JavaScript
 * Only provides essential utility functions and ST object localization
 * All main functionality handled by ag-coupon-new.js
 */

/**
 * Cookie Management Functions
 */
function setCookie(cname, cvalue, exdays) {
  const d = new Date();
  d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
  const expires = "expires=" + d.toUTCString();
  document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/";
}

function getCookie(cname) {
  const name = cname + "=";
  const ca = document.cookie.split(";");
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === " ") c = c.substring(1);
    if (c.indexOf(name) === 0) return c.substring(name.length, c.length);
  }
  return "";
}

/**
 * Utility Functions
 */
function isEmail(email) {
  const re = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i;
  return re.test(email);
}

function string_to_number(string) {
  if (typeof string === "number") return string;
  if (typeof string === "string") {
    const n = string.match(/\d+$/);
    return n ? parseFloat(n[0]) : 0;
  }
  return 0;
}

/**
 * Modern Copy to Clipboard Function
 */
function copyText(text) {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(text).then(() => true).catch(() => false);
  }

  const $el = jQuery(
    '<textarea style="opacity: 0;position: absolute;top: -10000px;right: 0;" id="wp-coupon-input-copy"></textarea>'
  );
  $el.val(text);
  jQuery("body").append($el);

  const input = document.getElementById("wp-coupon-input-copy");
  input.select();
  let successful = false;

  try {
    successful = document.execCommand("copy");
  } catch (e) {
    successful = false;
  }

  $el.remove();
  return successful;
}

/**
 * Check if copy command is supported
 */
function is_support_copy_command() {
  return document.queryCommandSupported && document.queryCommandSupported('copy');
}

/**
 * Login Modal Helper
 */
function openLoginModal() {
  jQuery(".wpu-login-btn").trigger("click");
}

/**
 * Main Document Ready Function
 */
jQuery(document).ready(function ($) {
  "use strict";
  
  console.log('🚫 Old theme functions disabled - implementing new system');
  console.log('✅ Minimal global.js loaded - utility functions available');
  
  // Make functions globally available
  window.setCookie = setCookie;
  window.getCookie = getCookie;
  window.isEmail = isEmail;
  window.string_to_number = string_to_number;
  window.copyText = copyText;
  window.is_support_copy_command = is_support_copy_command;
  window.openLoginModal = openLoginModal;
});

// End of minimal global.js
