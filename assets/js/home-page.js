/**
 * Home Page Interactive Features
 *
 * Handles animations, interactions, and dynamic content for the Ag-Coupon home page
 */

(function($) {
    'use strict';

    class HomePageEnhancer {
        constructor() {
            this.init();
        }

        init() {
            this.setupAnimations();
            this.setupCounters();
            this.setupNewsletterForm();
            this.setupLazyLoading();
            this.setupScrollEffects();
        }

        setupAnimations() {
            // Intersection Observer for scroll animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');

                        // Trigger counter animation if it's a stat item
                        if (entry.target.classList.contains('stat-number')) {
                            this.animateCounter(entry.target);
                        }
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            const animateElements = document.querySelectorAll('.section-header, .store-card, .coupon-card, .category-card, .stat-item');
            animateElements.forEach(el => {
                observer.observe(el);
            });
        }

        setupCounters() {
            // Animate counters when they come into view - only on home page
            if (!document.body.classList.contains('home')) {
                return;
            }

            $('.stat-number').each((index, element) => {
                const $element = $(element);
                const finalValue = parseInt($element.text().replace(/[^\d]/g, ''));
                $element.data('final-value', finalValue);
                $element.text('0');
            });
        }

        animateCounter(element) {
            const $element = $(element);
            const finalValue = $element.data('final-value');
            const duration = 2000; // 2 seconds
            const startTime = Date.now();
            const suffix = $element.text().replace(/[\d]/g, ''); // Get non-numeric characters

            const updateCounter = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth animation
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(finalValue * easeOutQuart);

                $element.text(currentValue + suffix);

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            };

            requestAnimationFrame(updateCounter);
        }

        setupNewsletterForm() {
            $('.subscription-form').on('submit', (e) => {
                e.preventDefault();

                const $form = $(e.currentTarget);
                const $button = $form.find('.subscribe-btn');
                const $input = $form.find('input[type="email"]');
                const email = $input.val();

                // Basic email validation
                if (!this.isValidEmail(email)) {
                    this.showFormMessage($form, 'يرجى إدخال بريد إلكتروني صحيح', 'error');
                    return;
                }

                // Show loading state
                const originalText = $button.html();
                $button.html('<svg class="animate-spin w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clip-rule="evenodd"></path></svg>جاري الإرسال...');
                $button.prop('disabled', true);

                // Simulate API call (replace with actual newsletter subscription)
                setTimeout(() => {
                    $button.html(originalText);
                    $button.prop('disabled', false);
                    $input.val('');
                    this.showFormMessage($form, 'تم الاشتراك بنجاح! شكراً لك', 'success');
                }, 2000);
            });
        }

        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        showFormMessage($form, message, type) {
            // Remove existing messages
            $form.find('.form-message').remove();

            const messageClass = type === 'error' ? 'text-red-300' : 'text-green-300';
            const $message = $(`<p class="form-message ${messageClass} text-sm mt-2 text-center">${message}</p>`);

            $form.append($message);

            // Remove message after 5 seconds
            setTimeout(() => {
                $message.fadeOut(() => $message.remove());
            }, 5000);
        }

        setupLazyLoading() {
            // Lazy load images for better performance
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.classList.remove('lazy');
                                imageObserver.unobserve(img);
                            }
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        }

        setupScrollEffects() {
            // Parallax effect for hero section
            $(window).on('scroll', () => {
                const scrolled = $(window).scrollTop();
                const heroHeight = $('.hero-section').outerHeight();

                if (scrolled < heroHeight) {
                    const parallaxSpeed = 0.5;
                    $('.hero-visual').css('transform', `translateY(${scrolled * parallaxSpeed}px)`);
                }
            });

            // Smooth scroll for anchor links
            $('a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                const target = $(this.getAttribute('href'));
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 800);
                }
            });
        }
    }

    // Enhanced Store Card Interactions
    class StoreCardEnhancer {
        constructor() {
            this.init();
        }

        init() {
            this.setupHoverEffects();
            this.setupClickTracking();
        }

        setupHoverEffects() {
            $('.store-card').on('mouseenter', function() {
                $(this).find('.store-image img').css('transform', 'scale(1.1)');
            }).on('mouseleave', function() {
                $(this).find('.store-image img').css('transform', 'scale(1)');
            });

            // Floating animation for featured stores
            $('.featured-store').each(function(index) {
                const delay = index * 100;
                setTimeout(() => {
                    $(this).addClass('animate-float');
                }, delay);
            });
        }

        setupClickTracking() {
            $('.store-card, .coupon-card').on('click', function(e) {
                // Add click tracking analytics here
                const cardType = $(this).hasClass('store-card') ? 'store' : 'coupon';
                const cardTitle = $(this).find('.store-name, .coupon-title').text();

                // Example: Send to analytics
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'click', {
                        event_category: 'home_page',
                        event_label: `${cardType}_${cardTitle}`,
                        value: 1
                    });
                }
            });
        }
    }

    // Coupon Card Interactions
    class CouponCardEnhancer {
        constructor() {
            this.init();
        }

        init() {
            this.setupRevealAnimation();
            this.setupCopyCode();
        }

        setupRevealAnimation() {
            $('.coupon-card').on('mouseenter', function() {
                $(this).find('.discount-badge').addClass('animate-pulse');
            }).on('mouseleave', function() {
                $(this).find('.discount-badge').removeClass('animate-pulse');
            });
        }

        setupCopyCode() {
            // Add copy to clipboard functionality for coupon codes
            $(document).on('click', '.coupon-code', function(e) {
                e.preventDefault();
                const code = $(this).data('code');

                if (navigator.clipboard) {
                    navigator.clipboard.writeText(code).then(() => {
                        $(this).text('تم النسخ!').addClass('copied');
                        setTimeout(() => {
                            $(this).text(code).removeClass('copied');
                        }, 2000);
                    });
                }
            });
        }
    }

    // Initialize when DOM is ready
    $(document).ready(() => {
        new HomePageEnhancer();
        new StoreCardEnhancer();
        new CouponCardEnhancer();

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            .animate-in {
                animation: slideInUp 0.6s ease-out forwards;
            }

            .animate-float {
                animation: gentleFloat 4s ease-in-out infinite;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes gentleFloat {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }

            .store-image img {
                transition: transform 0.3s ease;
            }

            .lazy {
                opacity: 0;
                transition: opacity 0.3s;
            }

            .lazy.loaded {
                opacity: 1;
            }

            .copied {
                background: #10B981 !important;
                color: white !important;
            }
        `;
        document.head.appendChild(style);
    });

})(jQuery);
