/**
 * Ag-Coupon Theme - Modern Header & UI JavaScript
 *
 * Modern functionality for:
 * - Mobile menu toggle
 * - Header search
 * - Responsive navigation
 * - Smooth animations
 *
 * Compatible with Tailwind CSS design system
 */

document.addEventListener('DOMContentLoaded', function() {

    /**
     * Mobile Menu Toggle
     * Enhanced mobile menu with slide animation
     */
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    const mobileMenuPanel = document.getElementById('mobile-menu-panel');
    const closeMobileMenu = document.getElementById('close-mobile-menu');

    function openMobileMenu() {
        mobileMenuOverlay.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // Animate menu panel
        setTimeout(() => {
            mobileMenuPanel.classList.remove('translate-x-full');
        }, 10);

        // Animate hamburger to X
        const hamburgerLines = mobileMenuToggle.querySelectorAll('span');
        if (hamburgerLines.length === 3) {
            hamburgerLines[0].style.transform = 'rotate(45deg) translate(6px, 6px)';
            hamburgerLines[1].style.opacity = '0';
            hamburgerLines[2].style.transform = 'rotate(-45deg) translate(6px, -6px)';
        }
    }

    function closeMobileMenuFunc() {
        mobileMenuPanel.classList.add('translate-x-full');
        document.body.classList.remove('overflow-hidden');

        // Reset hamburger animation
        const hamburgerLines = mobileMenuToggle.querySelectorAll('span');
        if (hamburgerLines.length === 3) {
            hamburgerLines[0].style.transform = '';
            hamburgerLines[1].style.opacity = '';
            hamburgerLines[2].style.transform = '';
        }

        setTimeout(() => {
            mobileMenuOverlay.classList.add('hidden');
        }, 300);
    }

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', openMobileMenu);
    }

    if (closeMobileMenu) {
        closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
    }

    // Close menu when clicking overlay
    if (mobileMenuOverlay) {
        mobileMenuOverlay.addEventListener('click', function(e) {
            if (e.target === mobileMenuOverlay) {
                closeMobileMenuFunc();
            }
        });
    }

    /**
     * Mobile Search Toggle
     * Enhanced mobile search with overlay
     */
    const mobileSearchToggle = document.getElementById('mobile-search-toggle');
    const mobileSearchOverlay = document.getElementById('mobile-search-overlay');
    const closeMobileSearch = document.getElementById('close-mobile-search');

    function openMobileSearch() {
        mobileSearchOverlay.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // Focus on search input
        setTimeout(() => {
            const searchInput = mobileSearchOverlay.querySelector('input[name="s"]');
            if (searchInput) {
                searchInput.focus();
            }
        }, 100);
    }

    function closeMobileSearchFunc() {
        mobileSearchOverlay.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }

    if (mobileSearchToggle) {
        mobileSearchToggle.addEventListener('click', openMobileSearch);
    }

    if (closeMobileSearch) {
        closeMobileSearch.addEventListener('click', closeMobileSearchFunc);
    }

    // Close search when clicking overlay
    if (mobileSearchOverlay) {
        mobileSearchOverlay.addEventListener('click', function(e) {
            if (e.target === mobileSearchOverlay) {
                closeMobileSearchFunc();
            }
        });
    }

    /**
     * Close overlays on window resize (desktop)
     */
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            if (mobileMenuOverlay && !mobileMenuOverlay.classList.contains('hidden')) {
                closeMobileMenuFunc();
            }
            if (mobileSearchOverlay && !mobileSearchOverlay.classList.contains('hidden')) {
                closeMobileSearchFunc();
            }
        }
    });

    /**
     * Header Search Toggle
     * Modern search functionality
     */
    const searchToggle = document.getElementById('search-toggle');
    const searchForm = document.getElementById('header-search-form');
    const searchInput = document.getElementById('header-search-input');
    const searchClose = document.getElementById('search-close');

    if (searchToggle && searchForm) {
        searchToggle.addEventListener('click', function(e) {
            e.preventDefault();
            searchForm.classList.add('active');
            if (searchInput) {
                setTimeout(() => searchInput.focus(), 100);
            }
        });
    }

    if (searchClose && searchForm) {
        searchClose.addEventListener('click', function(e) {
            e.preventDefault();
            searchForm.classList.remove('active');
        });
    }

    // Close search on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && searchForm && searchForm.classList.contains('active')) {
            searchForm.classList.remove('active');
        }
    });

    /**
     * Smooth Scroll for Anchor Links
     */
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;

            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    /**
     * Header Scroll Effect
     * Add shadow on scroll
     */
    const header = document.querySelector('.site-header');
    if (header) {
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', function() {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 10) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            lastScrollY = currentScrollY;
        });
    }

    /**
     * Enhanced Mega Menu Functionality
     * Fixed width mega menu with icons and thumbnails
     */
    const megaMenuItems = document.querySelectorAll('.nav-item.group');

    megaMenuItems.forEach(item => {
        const megaMenu = item.querySelector('.mega-menu');
        let hoverTimeout;

        if (megaMenu) {
            // Position mega menu properly
            function positionMegaMenu() {
                const rect = item.getBoundingClientRect();
                const menuWidth = 800; // Fixed width from CSS
                const viewportWidth = window.innerWidth;

                // Calculate if menu would overflow viewport
                const menuLeft = rect.left + (rect.width / 2) - (menuWidth / 2);
                const menuRight = menuLeft + menuWidth;

                if (menuRight > viewportWidth - 20) {
                    // Adjust position to prevent overflow
                    megaMenu.style.transform = `translateX(${viewportWidth - menuRight - 20}px)`;
                } else if (menuLeft < 20) {
                    // Adjust position for left overflow
                    megaMenu.style.transform = `translateX(${20 - menuLeft}px)`;
                } else {
                    megaMenu.style.transform = 'translateX(-50%)';
                }
            }

            // Mouse enter
            item.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);

                // Close other mega menus
                document.querySelectorAll('.mega-menu').forEach(menu => {
                    if (menu !== megaMenu) {
                        menu.classList.add('opacity-0', 'invisible', 'translate-y-2');
                        menu.classList.remove('opacity-100', 'visible', 'translate-y-0');
                    }
                });

                // Position and open current mega menu
                positionMegaMenu();

                setTimeout(() => {
                    megaMenu.classList.remove('opacity-0', 'invisible', 'translate-y-2');
                    megaMenu.classList.add('opacity-100', 'visible', 'translate-y-0');
                }, 50);
            });

            // Mouse leave
            item.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(() => {
                    megaMenu.classList.add('opacity-0', 'invisible', 'translate-y-2');
                    megaMenu.classList.remove('opacity-100', 'visible', 'translate-y-0');
                }, 150);
            });

            // Keep menu open when hovering over mega menu itself
            megaMenu.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
            });

            megaMenu.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(() => {
                    megaMenu.classList.add('opacity-0', 'invisible', 'translate-y-2');
                    megaMenu.classList.remove('opacity-100', 'visible', 'translate-y-0');
                }, 150);
            });

            // Reposition on window resize
            window.addEventListener('resize', positionMegaMenu);
        }
    });

    // Close mega menus when clicking outside or pressing escape
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.nav-item.group')) {
            document.querySelectorAll('.mega-menu').forEach(menu => {
                menu.classList.add('opacity-0', 'invisible', 'translate-y-2');
                menu.classList.remove('opacity-100', 'visible', 'translate-y-0');
            });
        }
    });

    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.mega-menu').forEach(menu => {
                menu.classList.add('opacity-0', 'invisible', 'translate-y-2');
                menu.classList.remove('opacity-100', 'visible', 'translate-y-0');
            });
        }
    });

    /**
     * Back to Top Button
     */
    const backToTop = document.getElementById('back-to-top');
    if (backToTop) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });

        backToTop.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    /**
     * Coupon Type Filter Functionality
     */
    function initializeCouponTypeFilter() {
        const filterButtons = document.querySelectorAll('.filter-coupons-by-type .tab-button');
        const couponItems = document.querySelectorAll('.coupon-grid-item[data-coupon-type]');

        if (filterButtons.length === 0 || couponItems.length === 0) return;

        filterButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const filter = this.getAttribute('data-filter');

                // Update active button
                filterButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-primary-300', 'text-primary-900');
                    btn.classList.add('text-gray-600', 'hover:bg-gray-100');
                });

                this.classList.add('active', 'bg-primary-300', 'text-primary-900');
                this.classList.remove('text-gray-600', 'hover:bg-gray-100');

                // Filter coupons
                couponItems.forEach(item => {
                    const couponType = item.getAttribute('data-coupon-type');

                    if (filter === 'all') {
                        // Show all coupons
                        item.style.display = 'block';
                        item.classList.remove('hidden');
                    } else if (couponType === filter) {
                        // Show matching coupons
                        item.style.display = 'block';
                        item.classList.remove('hidden');
                    } else {
                        // Hide non-matching coupons
                        item.style.display = 'none';
                        item.classList.add('hidden');
                    }
                });

                // Add smooth transition effect
                setTimeout(() => {
                    couponItems.forEach(item => {
                        if (item.style.display !== 'none') {
                            item.style.opacity = '1';
                            item.style.transform = 'scale(1)';
                        }
                    });
                }, 50);
            });
        });
    }

    /**
     * Initialize tooltips and other UI enhancements
     */
    function initializeUIEnhancements() {
        // Add loading states to buttons
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                if (!this.classList.contains('loading')) {
                    this.classList.add('loading');
                    setTimeout(() => {
                        this.classList.remove('loading');
                    }, 2000);
                }
            });
        });

        // Add hover effects to cards
        const cards = document.querySelectorAll('.coupon-card, .store-card, .blog-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('hovered');
            });

            card.addEventListener('mouseleave', function() {
                this.classList.remove('hovered');
            });
        });
    }

    // Initialize UI enhancements
    initializeUIEnhancements();

    // Initialize coupon type filter
    initializeCouponTypeFilter();

    /**
     * Fix Image Visibility Issues
     * Ensure all images are visible by default
     */
    function fixImageVisibility() {
        // Make sure all lazy-loaded images are visible
        const lazyImages = document.querySelectorAll('img[loading="lazy"]');
        lazyImages.forEach(img => {
            // Remove any opacity hiding
            img.style.opacity = '1';

            // Add loaded class if it doesn't exist
            if (!img.classList.contains('loaded')) {
                img.classList.add('loaded');
            }
        });

        // Handle images that might be added dynamically
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const newImages = node.querySelectorAll ? node.querySelectorAll('img[loading="lazy"]') : [];
                        newImages.forEach(img => {
                            img.style.opacity = '1';
                            img.classList.add('loaded');
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Fix image visibility immediately
    fixImageVisibility();

    /**
     * Console log for debugging
     */
    console.log('Ag-Coupon Theme JavaScript loaded successfully! 🎉');
});

/**
 * Utility Functions
 */
window.AgCouponTheme = {
    /**
     * Show notification
     */
    showNotification: function(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Hide notification
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    },

    /**
     * Toggle loading state
     */
    toggleLoading: function(element, loading = true) {
        if (loading) {
            element.classList.add('loading');
            element.disabled = true;
        } else {
            element.classList.remove('loading');
            element.disabled = false;
        }
    }
};
