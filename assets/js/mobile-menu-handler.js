/**
 * Enhanced Mobile Menu Handler for Ag-Coupon Theme
 *
 * Handles mobile search and menu overlays with accessibility and smooth animations
 */

(function($) {
    'use strict';

    class MobileMenuHandler {
        constructor() {
            this.isMenuOpen = false;
            this.isSearchOpen = false;
            this.isAnimating = false;
            this.init();
        }

        init() {
            this.bindEvents();
            this.setupAnimations();
            this.setupAccessibility();
        }

        bindEvents() {
            // Mobile search toggle
            $(document).on('click', '#mobile-search-toggle', this.openMobileSearch.bind(this));
            $(document).on('click', '#close-mobile-search', this.closeMobileSearch.bind(this));

            // Mobile menu toggle
            $(document).on('click', '#mobile-menu-toggle', this.toggleMobileMenu.bind(this));
            $(document).on('click', '#close-mobile-menu', this.closeMobileMenu.bind(this));

            // Mobile submenu toggles
            $(document).on('click', '.mobile-toggle-btn', this.toggleMobileSubmenu.bind(this));

            // Close overlays when clicking outside
            $(document).on('click', '#mobile-search-overlay, #mobile-menu-overlay', (e) => {
                if (e.target === e.currentTarget) {
                    this.closeMobileSearch();
                    this.closeMobileMenu();
                }
            });

            // Close overlays on escape key
            $(document).on('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeMobileSearch();
                    this.closeMobileMenu();
                }
            });

            // Handle window resize
            $(window).on('resize', this.handleResize.bind(this));
        }

        setupAccessibility() {
            // Add ARIA attributes
            $('#mobile-menu-toggle').attr({
                'aria-expanded': 'false',
                'aria-controls': 'mobile-menu-overlay',
                'aria-label': 'Toggle mobile menu'
            });

            $('#mobile-search-toggle').attr({
                'aria-expanded': 'false',
                'aria-controls': 'mobile-search-overlay',
                'aria-label': 'Toggle mobile search'
            });
        }

        openMobileSearch() {
            if (this.isSearchOpen || this.isAnimating) return;

            this.isAnimating = true;
            this.isSearchOpen = true;

            const $overlay = $('#mobile-search-overlay');
            const $toggle = $('#mobile-search-toggle');

            $overlay.removeClass('hidden').addClass('flex');
            $('body').addClass('overflow-hidden');

            // Update ARIA
            $toggle.attr('aria-expanded', 'true');
            $overlay.attr('aria-hidden', 'false');

            // Focus on search input
            setTimeout(() => {
                $('#mobile-search-overlay input[name="s"]').focus();
                this.isAnimating = false;
            }, 100);
        }

        closeMobileSearch() {
            if (!this.isSearchOpen || this.isAnimating) return;

            this.isAnimating = true;
            this.isSearchOpen = false;

            const $overlay = $('#mobile-search-overlay');
            const $toggle = $('#mobile-search-toggle');

            $overlay.addClass('hidden').removeClass('flex');
            $('body').removeClass('overflow-hidden');

            // Update ARIA
            $toggle.attr('aria-expanded', 'false');
            $overlay.attr('aria-hidden', 'true');

            setTimeout(() => {
                this.isAnimating = false;
            }, 100);
        }

        toggleMobileMenu() {
            if (this.isAnimating) return;

            if (this.isMenuOpen) {
                this.closeMobileMenu();
            } else {
                this.openMobileMenu();
            }
        }

        openMobileMenu() {
            if (this.isMenuOpen || this.isAnimating) return;

            this.isAnimating = true;
            this.isMenuOpen = true;

            const $overlay = $('#mobile-menu-overlay');
            const $panel = $('#mobile-menu-panel');
            const $toggle = $('#mobile-menu-toggle');

            // Show overlay
            $overlay.removeClass('hidden').addClass('flex');
            $('body').addClass('overflow-hidden');

            // Update ARIA
            $toggle.attr('aria-expanded', 'true');
            $overlay.attr('aria-hidden', 'false');

            // Animate panel slide in
            setTimeout(() => {
                $panel.removeClass('translate-x-full');
                this.animateHamburger($toggle, true);

                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }, 50);
        }

        closeMobileMenu() {
            if (!this.isMenuOpen || this.isAnimating) return;

            this.isAnimating = true;
            this.isMenuOpen = false;

            const $overlay = $('#mobile-menu-overlay');
            const $panel = $('#mobile-menu-panel');
            const $toggle = $('#mobile-menu-toggle');

            // Animate panel slide out
            $panel.addClass('translate-x-full');
            this.animateHamburger($toggle, false);

            // Hide overlay after animation
            setTimeout(() => {
                $overlay.addClass('hidden').removeClass('flex');
                $('body').removeClass('overflow-hidden');

                // Update ARIA
                $toggle.attr('aria-expanded', 'false');
                $overlay.attr('aria-hidden', 'true');

                // Close all submenus
                $('.mobile-submenu-content').addClass('hidden');
                $('.mobile-toggle-btn svg').removeClass('rotate-180');

                this.isAnimating = false;
            }, 300);
        }

        toggleMobileSubmenu(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);
            const targetId = $button.data('toggle');
            const $submenu = $('#' + targetId);
            const $icon = $button.find('svg');

            if ($submenu.hasClass('hidden')) {
                $submenu.removeClass('hidden').slideDown(200);
                $icon.addClass('rotate-180');
            } else {
                $submenu.slideUp(200, () => {
                    $submenu.addClass('hidden');
                });
                $icon.removeClass('rotate-180');
            }
        }

        animateHamburger($hamburger, toX) {
            const $spans = $hamburger.find('.hamburger-menu span');

            if (toX) {
                // Transform to X
                $spans.eq(0).css('transform', 'rotate(45deg) translate(5px, 5px)');
                $spans.eq(1).css('opacity', '0');
                $spans.eq(2).css('transform', 'rotate(-45deg) translate(7px, -6px)');
            } else {
                // Transform back to hamburger
                $spans.eq(0).css('transform', 'rotate(0) translate(0, 0)');
                $spans.eq(1).css('opacity', '1');
                $spans.eq(2).css('transform', 'rotate(0) translate(0, 0)');
            }
        }

        handleResize() {
            // Close mobile overlays on desktop
            if ($(window).width() >= 1024) {
                if (this.isSearchOpen) {
                    this.closeMobileSearch();
                }
                if (this.isMenuOpen) {
                    this.closeMobileMenu();
                }
            }
        }

        setupAnimations() {
            // Add smooth transitions for mobile elements
            const style = document.createElement('style');
            style.textContent = `
                .hamburger-menu span {
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                #mobile-menu-panel {
                    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .mobile-toggle-btn svg {
                    transition: transform 0.2s ease;
                }

                .mobile-submenu-content {
                    transition: all 0.2s ease;
                }

                .mobile-item-link {
                    transition: all 0.2s ease;
                }

                .mobile-item-link:hover {
                    transform: translateX(4px);
                }

                /* RTL support */
                .rtl .mobile-item-link:hover {
                    transform: translateX(-4px);
                }

                /* Mobile search overlay animations */
                #mobile-search-overlay {
                    animation: fadeIn 0.3s ease;
                }

                #mobile-menu-overlay {
                    animation: fadeIn 0.3s ease;
                }

                @keyframes fadeIn {
                    from {
                        opacity: 0;
                    }
                    to {
                        opacity: 1;
                    }
                }

                /* Prevent body scroll when overlays are open */
                body.overflow-hidden {
                    overflow: hidden;
                    position: fixed;
                    width: 100%;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Enhanced Mobile Search
    class MobileSearchHandler {
        constructor() {
            this.init();
        }

        init() {
            this.bindSearchEvents();
        }

        bindSearchEvents() {
            // Mobile search form submission
            $('#mobile-search-overlay form').on('submit', this.handleMobileSearch.bind(this));

            // Real-time search suggestions (if needed)
            $('#mobile-search-overlay input[name="s"]').on('input', this.debounce(this.handleSearchInput.bind(this), 300));
        }

        handleMobileSearch(e) {
            const $form = $(e.currentTarget);
            const searchTerm = $form.find('input[name="s"]').val().trim();

            if (searchTerm.length < 2) {
                e.preventDefault();
                this.showSearchError('يرجى إدخال كلمة بحث أطول');
                return;
            }

            // Add loading state
            const $button = $form.find('button[type="submit"]');
            const originalHtml = $button.html();
            $button.html('<svg class="w-5 h-5 animate-spin" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clip-rule="evenodd"></path></svg>');

            // Restore button after a delay (form will submit)
            setTimeout(() => {
                $button.html(originalHtml);
            }, 1000);
        }

        handleSearchInput(e) {
            const searchTerm = $(e.target).val().trim();

            if (searchTerm.length >= 2) {
                // Could implement live search suggestions here
                console.log('Search term:', searchTerm);
            }
        }

        showSearchError(message) {
            const $input = $('#mobile-search-overlay input[name="s"]');
            $input.addClass('border-red-300 focus:border-red-400');

            // Remove error styling after 3 seconds
            setTimeout(() => {
                $input.removeClass('border-red-300 focus:border-red-400');
            }, 3000);

            // Could show toast notification here
            console.warn(message);
        }

        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }

    // Initialize when DOM is ready
    $(document).ready(() => {
        new MobileMenuHandler();
        new MobileSearchHandler();
    });

})(jQuery);
