/**
 * Component Integration for Ag-Coupon Theme
 *
 * This file demonstrates how to integrate the new class-based JavaScript
 * with existing template components and AJAX functions
 */

/**
 * Integration with Header Search Component
 */
class HeaderSearchIntegration {
    constructor() {
        this.searchManager = window.agSearchManager;
        this.init();
    }

    init() {
        // Enhance existing header search with new functionality
        this.enhanceSearchInput();
        this.addSearchSuggestions();
    }

    enhanceSearchInput() {
        const searchInput = document.querySelector('.header-search-input .prompt');
        if (!searchInput) return;

        // Add modern search enhancements
        searchInput.setAttribute('autocomplete', 'off');
        searchInput.setAttribute('spellcheck', 'false');

        // Add search icon animation
        const searchIcon = searchInput.parentElement.querySelector('.search.icon');
        if (searchIcon) {
            searchInput.addEventListener('focus', () => {
                searchIcon.classList.add('animate-pulse');
            });

            searchInput.addEventListener('blur', () => {
                searchIcon.classList.remove('animate-pulse');
            });
        }
    }

    addSearchSuggestions() {
        // Add recent searches functionality
        const recentSearches = this.getRecentSearches();

        if (recentSearches.length > 0) {
            this.displayRecentSearches(recentSearches);
        }
    }

    getRecentSearches() {
        const searches = localStorage.getItem('ag_recent_searches');
        return searches ? JSON.parse(searches) : [];
    }

    saveRecentSearch(query) {
        let searches = this.getRecentSearches();
        searches = searches.filter(s => s !== query); // Remove duplicates
        searches.unshift(query); // Add to beginning
        searches = searches.slice(0, 5); // Keep only 5 recent searches

        localStorage.setItem('ag_recent_searches', JSON.stringify(searches));
    }

    displayRecentSearches(searches) {
        // Implementation for displaying recent searches
        console.log('Recent searches:', searches);
    }
}

/**
 * Integration with Coupon Cards
 */
class CouponCardIntegration {
    constructor() {
        this.couponManager = window.agCouponManager;
        this.init();
    }

    init() {
        this.enhanceCouponCards();
        this.addProgressiveEnhancement();
    }

    enhanceCouponCards() {
        const couponCards = document.querySelectorAll('.coupon-card');

        couponCards.forEach(card => {
            this.enhanceSingleCard(card);
        });
    }

    enhanceSingleCard(card) {
        // Add hover effects
        card.addEventListener('mouseenter', () => {
            card.classList.add('hover:shadow-coupon-hover');
        });

        // Add loading states for buttons
        const buttons = card.querySelectorAll('.coupon-button, .coupon-vote, .coupon-save');
        buttons.forEach(button => {
            this.addLoadingState(button);
        });

        // Add copy feedback
        const copyButtons = card.querySelectorAll('[data-code]');
        copyButtons.forEach(button => {
            this.addCopyFeedback(button);
        });
    }

    addLoadingState(button) {
        const originalHandler = button.onclick;

        button.addEventListener('click', (e) => {
            if (button.classList.contains('loading')) {
                e.preventDefault();
                return false;
            }

            button.classList.add('loading');

            // Remove loading state after 3 seconds (fallback)
            setTimeout(() => {
                button.classList.remove('loading');
            }, 3000);
        });
    }

    addCopyFeedback(button) {
        button.addEventListener('click', () => {
            // Visual feedback for copy action
            const ripple = document.createElement('div');
            ripple.className = 'absolute inset-0 bg-white bg-opacity-20 rounded animate-ping';
            button.style.position = 'relative';
            button.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
        });
    }

    addProgressiveEnhancement() {
        // Add intersection observer for lazy loading
        if ('IntersectionObserver' in window) {
            this.setupLazyLoading();
        }
    }

    setupLazyLoading() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadCouponDetails(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '50px'
        });

        const couponCards = document.querySelectorAll('.coupon-card[data-lazy]');
        couponCards.forEach(card => observer.observe(card));
    }

    loadCouponDetails(card) {
        // Load additional coupon details when card comes into view
        const couponId = card.dataset.couponId;
        if (couponId) {
            // Implement lazy loading logic here
            console.log('Loading details for coupon:', couponId);
        }
    }
}

/**
 * Integration with Store Pages
 */
class StorePageIntegration {
    constructor() {
        this.filterManager = window.agFilterManager;
        this.contentManager = window.agContentManager;
        this.init();
    }

    init() {
        this.enhanceStoreFilters();
        this.addStoreStats();
        this.setupInfiniteScroll();
    }

    enhanceStoreFilters() {
        const filterTabs = document.querySelectorAll('.filter-coupons-by-type a');

        filterTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                this.updateStoreStats(tab.dataset.filter);
            });
        });
    }

    updateStoreStats(filter) {
        // Update visible coupon count based on filter
        const container = document.querySelector('.store-listings');
        if (!container) return;

        let visibleCount = 0;
        const items = container.querySelectorAll('.store-listing-item');

        items.forEach(item => {
            if (filter === 'all' || item.classList.contains(`c-type-${filter}`)) {
                if (item.style.display !== 'none') {
                    visibleCount++;
                }
            }
        });

        // Update stats display
        const statsElement = document.querySelector('.coupon-count-display');
        if (statsElement) {
            statsElement.textContent = `${visibleCount} كوبون`;
        }
    }

    addStoreStats() {
        // Add real-time statistics only if not on store taxonomy page
        const statsContainer = document.querySelector('.store-stats');
        if (statsContainer && !document.body.classList.contains('tax-coupon_store')) {
            this.displayStoreStats(statsContainer);
        }
    }

    displayStoreStats(container) {
        const totalCoupons = document.querySelectorAll('.coupon-card').length;
        const activeCoupons = document.querySelectorAll('.coupon-card:not(.coupon-expired)').length;
        const expiredCoupons = totalCoupons - activeCoupons;

        const statsHTML = `
            <div class="stats-grid grid grid-cols-3 gap-4 text-center">
                <div class="stat-item">
                    <div class="stat-number text-2xl font-bold text-primary-600">${totalCoupons}</div>
                    <div class="stat-label text-sm text-gray-600">إجمالي الكوبونات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-2xl font-bold text-green-600">${activeCoupons}</div>
                    <div class="stat-label text-sm text-gray-600">كوبونات نشطة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-2xl font-bold text-red-600">${expiredCoupons}</div>
                    <div class="stat-label text-sm text-gray-600">كوبونات منتهية</div>
                </div>
            </div>
        `;

        container.innerHTML = statsHTML;
    }

    setupInfiniteScroll() {
        // Setup infinite scroll for store coupons
        const loadMoreBtn = document.querySelector('.load-more .button');
        if (!loadMoreBtn) return;

        // Convert load more button to infinite scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !loadMoreBtn.classList.contains('loading')) {
                    loadMoreBtn.click();
                }
            });
        }, {
            rootMargin: '200px'
        });

        observer.observe(loadMoreBtn);
    }
}

/**
 * Integration with Modal Components
 */
class ModalIntegration {
    constructor() {
        this.modalManager = window.agModalManager;
        this.init();
    }

    init() {
        this.enhanceExistingModals();
        this.addModalAnimations();
    }

    enhanceExistingModals() {
        const modals = document.querySelectorAll('.coupon-modal');

        modals.forEach(modal => {
            this.enhanceSingleModal(modal);
        });
    }

    enhanceSingleModal(modal) {
        // Add backdrop blur effect
        modal.addEventListener('show', () => {
            document.body.classList.add('modal-backdrop-blur');
        });

        modal.addEventListener('hide', () => {
            document.body.classList.remove('modal-backdrop-blur');
        });

        // Add keyboard navigation
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.trapFocus(e, modal);
            }
        });
    }

    trapFocus(e, modal) {
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    addModalAnimations() {
        // Add CSS animations for modal transitions
        const style = document.createElement('style');
        style.textContent = `
            .modal.active {
                animation: modalFadeIn 0.3s ease-out;
            }

            .modal.closing {
                animation: modalFadeOut 0.3s ease-in;
            }

            @keyframes modalFadeIn {
                from { opacity: 0; transform: scale(0.9); }
                to { opacity: 1; transform: scale(1); }
            }

            @keyframes modalFadeOut {
                from { opacity: 1; transform: scale(1); }
                to { opacity: 0; transform: scale(0.9); }
            }

            .modal-backdrop-blur {
                backdrop-filter: blur(4px);
            }
        `;

        document.head.appendChild(style);
    }
}

/**
 * Performance Monitoring Integration
 */
class PerformanceIntegration {
    constructor() {
        this.metrics = {
            ajaxRequests: 0,
            copyActions: 0,
            modalOpens: 0,
            searchQueries: 0
        };
        this.init();
    }

    init() {
        this.setupPerformanceMonitoring();
        this.trackUserInteractions();
    }

    setupPerformanceMonitoring() {
        // Monitor AJAX performance
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            this.metrics.ajaxRequests++;
            return originalFetch.apply(this, args);
        };
    }

    trackUserInteractions() {
        // Track copy actions
        document.addEventListener('copy', () => {
            this.metrics.copyActions++;
        });

        // Track modal opens
        document.addEventListener('modalOpen', () => {
            this.metrics.modalOpens++;
        });

        // Track search queries
        document.addEventListener('search', () => {
            this.metrics.searchQueries++;
        });
    }

    getMetrics() {
        return this.metrics;
    }
}

// Initialize all integrations when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for core classes to be initialized
    setTimeout(() => {
        window.headerSearchIntegration = new HeaderSearchIntegration();
        window.couponCardIntegration = new CouponCardIntegration();
        window.storePageIntegration = new StorePageIntegration();
        window.modalIntegration = new ModalIntegration();
        window.performanceIntegration = new PerformanceIntegration();

        console.log('🚀 Ag-Coupon component integrations initialized');
    }, 100);
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        HeaderSearchIntegration,
        CouponCardIntegration,
        StorePageIntegration,
        ModalIntegration,
        PerformanceIntegration
    };
}
