/**
 * Enhanced AJAX functionality for Ag-Coupon Theme
 * Modern JavaScript with Tailwind CSS integration
 */

class WPCouponAjax {
    constructor() {
        this.searchTimeout = null;
        this.searchCache = new Map();
        this.init();
    }

    init() {
        this.initSearch();
        this.initCouponActions();
        this.initLoadMore();
        this.initModals();
    }

    /**
     * Initialize Enhanced Search
     */
    initSearch() {
        const searchInputs = document.querySelectorAll('.enhanced-search-input');
        const searchContainers = document.querySelectorAll('.search-results-container');

        searchInputs.forEach(input => {
            const container = input.closest('.search-container')?.querySelector('.search-results-container');
            if (!container) return;

            // Input event with debouncing
            input.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                const searchTerm = e.target.value.trim();

                if (searchTerm.length < 2) {
                    this.hideSearchResults(container);
                    return;
                }

                this.searchTimeout = setTimeout(() => {
                    this.performSearch(searchTerm, container, input);
                }, 300);
            });

            // Focus and blur events
            input.addEventListener('focus', () => {
                if (input.value.trim().length >= 2) {
                    this.showSearchResults(container);
                }
            });

            input.addEventListener('blur', (e) => {
                // Delay hiding to allow clicking on results
                setTimeout(() => {
                    if (!container.matches(':hover')) {
                        this.hideSearchResults(container);
                    }
                }, 150);
            });

            // Keyboard navigation
            input.addEventListener('keydown', (e) => {
                this.handleSearchKeyboard(e, container);
            });
        });

        // Close search results when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                searchContainers.forEach(container => {
                    this.hideSearchResults(container);
                });
            }
        });
    }

    /**
     * Perform AJAX Search
     */
    async performSearch(searchTerm, container, input) {
        const searchType = input.dataset.searchType || 'all';
        const cacheKey = `${searchTerm}-${searchType}`;

        // Check cache first
        if (this.searchCache.has(cacheKey)) {
            this.displaySearchResults(this.searchCache.get(cacheKey), container);
            return;
        }

        // Show loading state
        this.showSearchLoading(container);

        try {
            const formData = new FormData();
            formData.append('action', 'wpcoupon_enhanced_search');
            formData.append('s', searchTerm);
            formData.append('type', searchType);
            formData.append('_wpnonce', wpCouponAjax.search_nonce);

            const response = await fetch(wpCouponAjax.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                // Cache the results
                this.searchCache.set(cacheKey, data.data);
                this.displaySearchResults(data.data, container);
            } else {
                this.showSearchError(container, data.data?.message || 'Search failed');
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showSearchError(container, 'Network error occurred');
        }
    }

    /**
     * Display Search Results
     */
    displaySearchResults(data, container) {
        container.innerHTML = data.html;
        this.showSearchResults(container);

        // Add result count if available
        if (data.count !== undefined) {
            const countElement = container.querySelector('.search-count');
            if (countElement) {
                countElement.textContent = `${data.count} results found`;
            }
        }

        // Animate results
        const results = container.querySelectorAll('.search-results-stores a, .search-results-coupons a');
        results.forEach((result, index) => {
            result.style.opacity = '0';
            result.style.transform = 'translateY(10px)';
            setTimeout(() => {
                result.style.transition = 'all 0.2s ease';
                result.style.opacity = '1';
                result.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }

    /**
     * Show Search Loading
     */
    showSearchLoading(container) {
        container.innerHTML = `
            <div class="p-6 text-center">
                <div class="inline-flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-gray-600">Searching...</span>
                </div>
            </div>
        `;
        this.showSearchResults(container);
    }

    /**
     * Show Search Error
     */
    showSearchError(container, message) {
        container.innerHTML = `
            <div class="p-6 text-center">
                <svg class="w-12 h-12 text-red-300 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <p class="text-red-600">${message}</p>
            </div>
        `;
        this.showSearchResults(container);
    }

    /**
     * Show/Hide Search Results
     */
    showSearchResults(container) {
        container.classList.remove('hidden', 'opacity-0', 'scale-95');
        container.classList.add('opacity-100', 'scale-100');
    }

    hideSearchResults(container) {
        container.classList.add('opacity-0', 'scale-95');
        setTimeout(() => {
            container.classList.add('hidden');
        }, 150);
    }

    /**
     * Handle Search Keyboard Navigation
     */
    handleSearchKeyboard(e, container) {
        const results = container.querySelectorAll('a');
        if (results.length === 0) return;

        let currentIndex = -1;
        results.forEach((result, index) => {
            if (result.classList.contains('keyboard-focus')) {
                currentIndex = index;
            }
        });

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.focusSearchResult(results, currentIndex + 1);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.focusSearchResult(results, currentIndex - 1);
                break;
            case 'Enter':
                if (currentIndex >= 0) {
                    e.preventDefault();
                    results[currentIndex].click();
                }
                break;
            case 'Escape':
                this.hideSearchResults(container);
                break;
        }
    }

    /**
     * Focus Search Result
     */
    focusSearchResult(results, index) {
        // Remove previous focus
        results.forEach(result => {
            result.classList.remove('keyboard-focus', 'bg-primary-50');
        });

        // Add focus to new result
        if (index >= 0 && index < results.length) {
            results[index].classList.add('keyboard-focus', 'bg-primary-50');
            results[index].scrollIntoView({ block: 'nearest' });
        }
    }

    /**
     * Initialize Coupon Actions
     */
    initCouponActions() {
        document.addEventListener('click', (e) => {
            // Coupon code reveal
            if (e.target.matches('.reveal-coupon-code')) {
                e.preventDefault();
                this.revealCouponCode(e.target);
            }

            // Add to favorites
            if (e.target.matches('.add-to-favorites')) {
                e.preventDefault();
                this.addToFavorites(e.target);
            }

            // Vote coupon
            if (e.target.matches('.vote-coupon')) {
                e.preventDefault();
                this.voteCoupon(e.target);
            }
        });
    }

    /**
     * Reveal Coupon Code
     */
    async revealCouponCode(button) {
        const couponId = button.dataset.couponId;
        if (!couponId) return;

        // Show loading state
        const originalText = button.innerHTML;
        button.innerHTML = `
            <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        `;
        button.disabled = true;

        try {
            const formData = new FormData();
            formData.append('action', 'wpcoupon_coupon_ajax');
            formData.append('st_doing', 'tracking_coupon');
            formData.append('coupon_id', couponId);
            formData.append('_wpnonce', wpCouponAjax.nonce);

            await fetch(wpCouponAjax.ajax_url, {
                method: 'POST',
                body: formData
            });

            // Reveal the code
            const codeElement = button.closest('.coupon-card').querySelector('.coupon-code');
            if (codeElement) {
                codeElement.classList.remove('blurred');
                button.style.display = 'none';
            }

        } catch (error) {
            console.error('Error revealing coupon code:', error);
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    /**
     * Initialize Load More
     */
    initLoadMore() {
        const loadMoreButtons = document.querySelectorAll('.load-more-coupons');
        
        loadMoreButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.loadMoreCoupons(button);
            });
        });
    }

    /**
     * Initialize Modals
     */
    initModals() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.open-coupon-modal')) {
                e.preventDefault();
                this.openCouponModal(e.target.dataset.couponId);
            }
        });
    }

    /**
     * Open Coupon Modal
     */
    async openCouponModal(couponId) {
        if (!couponId) return;

        try {
            const formData = new FormData();
            formData.append('action', 'wpcoupon_coupon_ajax');
            formData.append('st_doing', 'get_coupon_modal');
            formData.append('hash', `#coupon-id-${couponId}`);
            formData.append('_wpnonce', wpCouponAjax.nonce);

            const response = await fetch(wpCouponAjax.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.displayModal(data.data);
            }
        } catch (error) {
            console.error('Error opening modal:', error);
        }
    }

    /**
     * Display Modal
     */
    displayModal(content) {
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        overlay.innerHTML = `
            <div class="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                ${content}
            </div>
        `;

        document.body.appendChild(overlay);

        // Close modal on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                document.body.removeChild(overlay);
            }
        });

        // Close modal on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                document.body.removeChild(overlay);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new WPCouponAjax();
});
