/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./**/*.php",
    "./assets/js/**/*.js",
    "./inc/**/*.php",
    "./loop/**/*.php",
    "./templates/**/*.php"
  ],
  theme: {
    extend: {
      colors: {
        // Yellow and Dark Blue Color Scheme for Ag-Coupon Theme
        primary: {
          50: '#fefce8',   // lightest yellow
          100: '#fef3c7',  // light yellow
          200: '#fde68a',  // medium light yellow
          300: '#fcd34d',  // main yellow
          400: '#f59e0b',  // darker yellow
          500: '#d97706',  // darkest yellow
          600: '#b45309',
          700: '#92400e',
          800: '#78350f',
          900: '#451a03'
        },
        secondary: {
          50: '#eff6ff',   // lightest blue
          100: '#dbeafe',  // light blue
          200: '#bfdbfe',  // medium light blue
          300: '#93c5fd',  // medium blue
          400: '#60a5fa',  // main blue
          500: '#3b82f6',  // primary blue
          600: '#2563eb',  // darker blue
          700: '#1d4ed8',  // dark blue
          800: '#1e40af',  // darker blue
          900: '#1e3a8a'   // darkest blue (main dark blue)
        },
        // Coupon specific colors
        coupon: {
          code: '#fcd34d',     // yellow for code coupons
          sale: '#3b82f6',     // blue for sale coupons
          print: '#10b981',    // green for printable coupons
          expired: '#ef4444',  // red for expired
          featured: '#f59e0b'  // orange for featured
        }
      },
      fontFamily: {
        'rtl': ['Readex Pro', 'Tahoma', 'Arial', 'sans-serif'],
        'ltr': ['Jost', 'Inter', 'system-ui', 'sans-serif']
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem'
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem'
      },
      boxShadow: {
        'coupon': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'coupon-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'store': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio')
  ],
}
