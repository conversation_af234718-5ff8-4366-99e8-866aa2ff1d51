<?php

/**
 * Single Store Page Template
 * Modern Tailwind CSS design for Ag-Coupon Theme
 *
 * @package Ag-Coupon
 */
get_header();

$term = get_queried_object();
wpcoupon_setup_store($term);
$current_link = get_permalink($term);
$store_obj = wpcoupon_store();
$store_name = $store_obj->name;
$layout = wpcoupon_get_option('store_layout', 'left-sidebar');

// Store statistics using proper template functions
$total_coupons = $store_obj->count;
$is_featured = $store_obj->is_featured();
$store_url = $store_obj->get_website_url();
$store_page_url = $store_obj->get_url();
$store_thumbnail = $store_obj->get_thumbnail('medium');
$store_content = $store_obj->get_content();
$store_extra_info = $store_obj->get_extra_info();


?>

<!-- Store Header Section -->
<section class="store-header-section bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-12">
	<div class="container mx-auto px-4">
		<!-- Store Profile Card -->
		<div class="store-profile-card bg-white rounded-2xl shadow-coupon p-8 mb-8">
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">

				<!-- Store Logo & Basic Info -->
				<div class="lg:col-span-1 text-center lg:text-right">
					<div class="store-logo-container relative inline-block">
						<?php if ($is_featured) { ?>
							<div class="absolute -top-2 -right-2 z-10">
								<span class="coupon-badge-featured">
									<svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
										<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
									</svg>
									<?php esc_html_e('متجر مميز', 'wp-coupon'); ?>
								</span>
							</div>
						<?php } ?>

						<div class="store-logo w-32 h-32 mx-auto lg:mx-0 rounded-2xl overflow-hidden bg-gray-100 shadow-lg">
							<a rel="nofollow" target="_blank"
							   title="<?php printf(esc_attr__('تسوق الآن من %s', 'wp-coupon'), $store_obj->get_display_name()); ?>"
							   href="<?php echo esc_url($store_url); ?>"
							   class="block hover:opacity-80 transition-opacity duration-200">
								<?php
								// Robust store image retrieval with multiple fallbacks
								$store_image = '';

								// Method 1: Try to get image from _wpc_store_image_id meta field
								$image_id = get_term_meta($store_obj->term_id, '_wpc_store_image_id', true);
								if ($image_id && intval($image_id) > 0) {
									$store_image = wp_get_attachment_image_url($image_id, 'medium');
								}

								// Method 2: If no attachment, try direct URL from _wpc_store_image meta field
								if (!$store_image) {
									$image_url = get_term_meta($store_obj->term_id, '_wpc_store_image', true);
									if ($image_url && filter_var($image_url, FILTER_VALIDATE_URL)) {
										$store_image = $image_url;
									}
								}

								// Method 3: Try the store object properties as fallback
								if (!$store_image) {
									if ($store_obj->_wpc_store_image_id && intval($store_obj->_wpc_store_image_id) > 0) {
										$store_image = wp_get_attachment_image_url($store_obj->_wpc_store_image_id, 'medium');
									} elseif ($store_obj->_wpc_store_image && filter_var($store_obj->_wpc_store_image, FILTER_VALIDATE_URL)) {
										$store_image = $store_obj->_wpc_store_image;
									}
								}

								// Method 4: If still no image and we have a store URL, use website screenshot
								if (!$store_image) {
									$store_url_meta = get_term_meta($store_obj->term_id, '_wpc_store_url', true);
									$store_url_obj = $store_obj->_wpc_store_url;

									$website_url = $store_url_meta ? $store_url_meta : $store_url_obj;

									if ($website_url && filter_var($website_url, FILTER_VALIDATE_URL)) {
										$store_image = 'https://s.wordpress.com/mshots/v1/'.urlencode($website_url).'?w=200&h=115';
									}
								}

								if ($store_image) : ?>
									<img src="<?php echo esc_url($store_image); ?>"
									     alt="<?php echo esc_attr($store_obj->get_display_name()); ?>"
									     class="w-full h-full object-cover"
									     loading="lazy">
								<?php else : ?>
									<div class="w-full h-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white font-bold text-2xl">
										<?php echo esc_html(substr($store_name, 0, 2)); ?>
									</div>
								<?php endif; ?>
							</a>
						</div>
					</div>

					<!-- Store Actions -->
					<div class="store-actions mt-6 space-y-3">
						<a href="<?php echo esc_url($store_url); ?>"
						   target="_blank" rel="nofollow"
						   class="btn-primary w-full max-w-xs mx-auto lg:mx-0 inline-flex items-center justify-center py-3 px-6 text-lg font-bold rounded-xl transition-all duration-200 hover:transform hover:scale-105">
							<?php esc_html_e('زيارة المتجر', 'wp-coupon'); ?>
							<svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
							</svg>
						</a>



						<!-- Share Section -->
						<div class="share-section">
							<p class="text-sm text-gray-600 mb-2"><?php esc_html_e('شارك المتجر:', 'wp-coupon'); ?></p>
							<div class="share-buttons">
								<?php do_action('wpcoupon_store_content'); ?>
							</div>
						</div>

						<div class="rate-store">
							<?php echo wpcoupon_rating(); ?>
						</div>
					</div>
				</div>

				<!-- Store Information -->
				<div class="lg:col-span-2">
					<div class="store-info">
						<h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
							<?php echo esc_html($store_obj->get_single_store_name()); ?>
						</h1>

						<!-- Store Statistics -->
						<div class="store-stats grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
							<div class="stat-item bg-primary-50 rounded-xl p-6 text-center transform hover:scale-105 transition-all duration-200">
								<div class="stat-icon text-primary-500 mb-3">
									<svg class="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
									</svg>
								</div>
								<div class="stat-value text-3xl font-bold text-primary-600 mb-1"><?php echo $total_coupons ? $total_coupons : 0; ?></div>
								<div class="stat-label text-sm font-medium text-gray-600"><?php esc_html_e('إجمالي الكوبونات', 'wp-coupon'); ?></div>
							</div>

							<div class="stat-item bg-secondary-50 rounded-xl p-6 text-center transform hover:scale-105 transition-all duration-200">
								<div class="stat-icon text-secondary-500 mb-3">
									<svg class="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z" clip-rule="evenodd"></path>
									</svg>
								</div>
								<?php
								$coupon_counts = $store_obj->count_coupon();
								// Ensure we have default values for our coupon types
								$code_count = isset($coupon_counts['code']) ? $coupon_counts['code'] : 0;
								$sale_count = isset($coupon_counts['sale']) ? $coupon_counts['sale'] : 0;
								?>
								<div class="stat-value text-3xl font-bold text-secondary-600 mb-1"><?php echo esc_html($code_count); ?></div>
								<div class="stat-label text-sm font-medium text-gray-600"><?php esc_html_e('كوبونات خصم', 'wp-coupon'); ?></div>
							</div>

							<div class="stat-item bg-green-50 rounded-xl p-6 text-center transform hover:scale-105 transition-all duration-200">
								<div class="stat-icon text-green-500 mb-3">
									<svg class="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
									</svg>
								</div>
								<div class="stat-value text-3xl font-bold text-green-600 mb-1"><?php echo esc_html($sale_count); ?></div>
								<div class="stat-label text-sm font-medium text-gray-600"><?php esc_html_e('عروض خاصة', 'wp-coupon'); ?></div>
							</div>
						</div>

						<!-- Store Description -->
						<?php if ($store_content) : ?>
						<div class="store-description">
							<div class="store-content-wrapper">
								<div class="store-content prose prose-gray max-w-none" id="store-content">
									<?php echo wp_kses_post($store_content); ?>
								</div>
								<button class="read-more-btn text-secondary-600 hover:text-secondary-700 font-semibold text-sm mt-2 transition-colors duration-200" id="expandbtn">
									<?php esc_html_e('إقرأ المزيد', 'wp-coupon'); ?>
								</button>
							</div>
						</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>



<!-- Store Coupons Section -->
<section class="store-coupons-section py-12">
	<div class="container mx-auto px-4">
		<?php
		// Get store coupons data
		global $wp_query;
		$term_id = get_queried_object_id();
		$current_year = date('Y');

		// Get all coupons from store (combine all types)
		$number_total = absint(wpcoupon_get_option('store_number_active', 50)); // Increased to get more coupons
		$all_coupons = wpcoupon_get_store_coupons($term_id, $number_total, 1, 'active');

		// Count coupons by type
		$code_coupons = array();
		$sale_coupons = array();

		foreach ($all_coupons as $coupon) {
			$coupon_type = get_post_meta($coupon->ID, '_wpc_coupon_type', true) ?: 'code';
			if ($coupon_type === 'code') {
				$code_coupons[] = $coupon;
			} else {
				$sale_coupons[] = $coupon;
			}
		}

		if ($all_coupons) {
		?>

		<!-- Section Header -->
		<div class="section-header text-center mb-12">
			<h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
				<?php printf(esc_html__('كوبونات %s لعام %s', 'wp-coupon'), single_term_title('', false), $current_year); ?>
			</h2>
			<p class="text-lg text-gray-600 max-w-2xl mx-auto">
				<?php printf(esc_html__('اكتشف أحدث كوبونات الخصم وأفضل العروض من %s', 'wp-coupon'), wpcoupon_store()->get_display_name()); ?>
			</p>
		</div>

		<!-- Coupon Type Filter Tabs -->
		<div class="coupon-filter-tabs mb-8">
			<div class="filter-coupons-by-type bg-white rounded-xl shadow-sm p-2">
				<div class="flex flex-wrap gap-2">
					<button data-filter="all" class="tab-button active px-6 py-3 text-sm font-semibold rounded-lg transition-all duration-200 bg-primary-300 text-primary-900">
						<?php esc_html_e('الكل', 'wp-coupon'); ?>
						<span class="ml-2 bg-primary-400 text-primary-900 px-2 py-1 rounded-full text-xs"><?php echo count($all_coupons); ?></span>
					</button>
					<?php if ($code_coupons) { ?>
					<button data-filter="code" class="tab-button px-6 py-3 text-sm font-semibold rounded-lg transition-all duration-200 text-gray-600 hover:bg-gray-100">
						<?php esc_html_e('الكوبونات', 'wp-coupon'); ?>
						<span class="ml-2 bg-blue-200 text-blue-700 px-2 py-1 rounded-full text-xs"><?php echo count($code_coupons); ?></span>
					</button>
					<?php } ?>
					<?php if ($sale_coupons) { ?>
					<button data-filter="sale" class="tab-button px-6 py-3 text-sm font-semibold rounded-lg transition-all duration-200 text-gray-600 hover:bg-gray-100">
						<?php esc_html_e('العروض', 'wp-coupon'); ?>
						<span class="ml-2 bg-green-200 text-green-700 px-2 py-1 rounded-full text-xs"><?php echo count($sale_coupons); ?></span>
					</button>
					<?php } ?>
				</div>
			</div>
		</div>

		<!-- All Coupons Grid with Type Filtering -->
		<div class="store-coupons-grid">
			<div class="store-listings st-list-coupons grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="all-coupons-grid">
				<?php
				foreach ($all_coupons as $coupon) {
					$coupon_type = get_post_meta($coupon->ID, '_wpc_coupon_type', true) ?: 'code';

					// Use modern coupon card system with data-coupon-type attribute for filtering
					echo '<div class="coupon-grid-item" data-coupon-type="' . esc_attr($coupon_type) . '">';
					render_coupon_card($coupon, 'default', array(
						'show_store_logo' => false, // Don't show store logo on store page
						'show_badges' => true,
						'show_description' => true,
						'description_length' => 20,
						'show_image' => true
					));
					echo '</div>';
				}
				?>
			</div>
		</div>


		
		<!-- Store Extra Information -->
		<?php if ($store_extra_info) : ?>
		<div class="store-extra-info bg-white rounded-2xl shadow-coupon p-6 mt-8">
			<h3 class="text-xl font-bold text-gray-900 mb-4"><?php esc_html_e('معلومات إضافية', 'wp-coupon'); ?></h3>
			<div class="prose prose-gray max-w-none">
				<?php echo wp_kses_post($store_extra_info); ?>
			</div>
		</div>
		<?php endif; ?>

		<?php } else { // no coupons found ?>

		<!-- No Coupons Found -->
		<div class="no-coupons-found text-center py-16">
			<div class="max-w-md mx-auto">
				<div class="mb-6">
					<svg class="w-20 h-20 mx-auto text-gray-300" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
					</svg>
				</div>
				<h3 class="text-2xl font-bold text-gray-900 mb-4">
					<?php esc_html_e('لا توجد كوبونات متاحة حالياً', 'wp-coupon'); ?>
				</h3>
				<p class="text-gray-600 mb-6">
					<?php printf(esc_html__('لا توجد كوبونات متاحة لمتجر %s في الوقت الحالي. تابعنا للحصول على أحدث الكوبونات والعروض!', 'wp-coupon'), $store_obj->get_display_name()); ?>
				</p>
				<a href="<?php echo esc_url(home_url('/')); ?>" class="btn-primary inline-flex items-center px-6 py-3 rounded-lg font-semibold">
					<?php esc_html_e('تصفح المتاجر الأخرى', 'wp-coupon'); ?>
					<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
					</svg>
				</a>
			</div>
		</div>

		<?php } ?>

		<?php wp_reset_postdata(); ?>

	</div>
</section>

<!-- Store Sidebar Section -->
<section class="store-sidebar-section py-12 bg-gray-50">
	<div class="container mx-auto px-4">
		<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
			<div class="lg:col-span-3">
				<!-- Main content area placeholder -->
			</div>
			<div class="lg:col-span-1">
				<?php get_sidebar('store'); ?>
			</div>
		</div>
	</div>
</section>

<?php get_footer(); ?>