# Enhanced Coupon Card System - Unified

## Overview

A unified, enhanced coupon card system that replaces all old coupon loop templates. Features modern design, deduplication tracking, AJAX integration, and consistent styling across the entire theme.

## Files Structure

```
template-parts/components/
├── coupon-card-enhanced.php          # Main enhanced card component
└── enhanced-coupon-card.php          # Alternative enhanced card

template-parts/loop/
└── loop-coupon.php                   # Unified AJAX loop template

inc/components/
├── coupon-card-helper.php            # Helper functions and template overrides
├── enhanced-coupon-functions.php     # Enhanced card functions and tracking
└── coupon-tracker.php                # Deduplication tracking system
```

## Basic Usage

### 1. Simple Coupon Card

```php
// Basic usage with default settings
wpcoupon_render_coupon_card($coupon);
```

### 2. Using Presets

```php
// Home page coupon card
wpcoupon_render_home_coupon_card($coupon);

// Featured coupon card
wpcoupon_render_featured_coupon_card($coupon);

// Minimal coupon card
wpcoupon_render_minimal_coupon_card($coupon);

// Grid coupon card (for archive pages)
wpcoupon_render_grid_coupon_card($coupon);
```

### 3. Custom Configuration

```php
// Custom coupon card with specific options
wpcoupon_render_coupon_card($coupon, array(
    'style' => 'featured',
    'show_store_logo' => true,
    'show_badges' => true,
    'show_description' => true,
    'description_length' => 20,
    'button_style' => 'full'
));
```

## Configuration Options

### Style Options
- `home` - Home page style (compact, clean)
- `featured` - Featured coupon with full details
- `minimal` - Minimal design for sidebars
- `grid` - Grid layout for archives
- `default` - Basic coupon card

### Display Options
- `show_store_logo` - Show/hide store logo/thumbnail
- `show_badges` - Show/hide featured/expired badges
- `show_description` - Show/hide coupon description
- `show_expiry` - Show/hide expiry date
- `show_save_button` - Show/hide save/favorite button
- `description_length` - Description word limit (default: 15)
- `button_style` - Button style (full, compact, icon)

## Real-World Examples

### 1. Home Page Latest Coupons

```php
// In template-parts/home/<USER>
<div class="coupons-grid" id="latest-coupons-grid">
    <?php foreach ($latest_coupons as $coupon) : ?>
        <?php wpcoupon_render_home_coupon_card($coupon); ?>
    <?php endforeach; ?>
</div>

<!-- Load More Button with AJAX -->
<button class="load-more-coupons btn-secondary"
        data-page="2"
        data-posts-per-page="8"
        data-preset="home"
        data-nonce="<?php echo wp_create_nonce('load_more_coupons'); ?>">
    <?php esc_html_e('تحميل المزيد من الكوبونات', 'wp-coupon'); ?>
</button>
```

### 2. Sidebar Widget

```php
// In sidebar widget
<?php foreach ($sidebar_coupons as $coupon) : ?>
    <?php wpcoupon_render_coupon_card($coupon, array(
        'style' => 'minimal',
        'show_store_logo' => false,
        'show_description' => false,
        'show_save_button' => false,
        'button_style' => 'compact',
        'additional_classes' => 'sidebar-coupon'
    )); ?>
<?php endforeach; ?>
```

### 3. Coupon Archive Page

```php
// In archive-coupon.php
<div class="coupons-archive-grid">
    <?php while (have_posts()) : the_post(); ?>
        <?php wpcoupon_render_grid_coupon_card(get_post()); ?>
    <?php endwhile; ?>
</div>
```

### 4. Featured Coupons Section

```php
// In featured coupons section
<div class="featured-coupons">
    <h3><?php esc_html_e('كوبونات مميزة', 'wp-coupon'); ?></h3>
    <div class="featured-coupons-grid">
        <?php foreach ($featured_coupons as $coupon) : ?>
            <?php wpcoupon_render_featured_coupon_card($coupon); ?>
        <?php endforeach; ?>
    </div>
</div>
```

### 5. Search Results

```php
// In search results
<?php foreach ($coupon_results as $coupon) : ?>
    <?php wpcoupon_render_coupon_card($coupon, array(
        'style' => 'grid',
        'show_store_logo' => true,
        'show_badges' => true,
        'additional_classes' => 'search-result'
    )); ?>
<?php endforeach; ?>
```

## AJAX Load More Functionality

### 1. Setup Load More Button

```php
<button class="load-more-coupons btn-secondary"
        data-page="2"
        data-posts-per-page="8"
        data-preset="home"
        data-category=""
        data-store=""
        data-type=""
        data-nonce="<?php echo wp_create_nonce('load_more_coupons'); ?>">
    <span class="btn-text"><?php esc_html_e('Load More', 'wp-coupon'); ?></span>
    <span class="btn-loading" style="display: none;"><?php esc_html_e('Loading...', 'wp-coupon'); ?></span>
</button>
```

### 2. JavaScript Handler

```javascript
document.addEventListener('DOMContentLoaded', function() {
    const loadMoreBtn = document.querySelector('.load-more-coupons');
    const couponsGrid = document.getElementById('coupons-grid');

    if (loadMoreBtn && couponsGrid) {
        loadMoreBtn.addEventListener('click', function() {
            // Show loading state
            this.disabled = true;

            // AJAX request to load more coupons
            fetch(ajaxurl, {
                method: 'POST',
                body: new URLSearchParams({
                    action: 'load_more_coupons',
                    page: this.dataset.page,
                    posts_per_page: this.dataset.postsPerPage,
                    preset: this.dataset.preset,
                    nonce: this.dataset.nonce
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    couponsGrid.insertAdjacentHTML('beforeend', data.data.html);
                    this.dataset.page = parseInt(this.dataset.page) + 1;

                    if (!data.data.has_more) {
                        this.style.display = 'none';
                    }
                }
            });
        });
    }
});
```

### 3. AJAX Handler (Already Included)

The AJAX handler `wpcoupon_ajax_load_more_coupons()` is automatically included in the helper file and supports:

- **Pagination** - Load specific page of coupons
- **Filtering** - Filter by category, store, type
- **Presets** - Use different card styles
- **Security** - Nonce verification
- **Performance** - Optimized queries

## Advanced Usage

### 1. Multiple Cards with Wrapper

```php
// Render multiple cards with consistent styling
wpcoupon_render_coupon_cards($coupons, array(
    'style' => 'featured',
    'show_badges' => true
), 'custom-coupons-grid');
```

### 2. Getting HTML Output

```php
// Get card HTML instead of echoing
$card_html = wpcoupon_get_coupon_card($coupon, array(
    'style' => 'minimal'
));

// Use in AJAX responses, emails, etc.
echo $card_html;
```

### 3. Using Presets with Overrides

```php
// Use preset but override specific options
wpcoupon_render_coupon_card_preset($coupon, 'home', array(
    'description_length' => 25,
    'show_save_button' => true
));
```

### 4. Dynamic Configuration

```php
// Dynamic configuration based on conditions
$card_config = array(
    'style' => $is_featured ? 'featured' : 'home',
    'show_badges' => $is_featured,
    'show_store_logo' => $show_logos,
    'button_style' => $is_mobile ? 'compact' : 'full'
);

wpcoupon_render_coupon_card($coupon, $card_config);
```

## CSS Classes Generated

The component generates various CSS classes for styling:

### Base Classes
- `.coupon-card` - Base card class
- `.{style}-coupon` - Style-specific class (home-coupon, featured-coupon, etc.)
- `.has-thumb` / `.no-thumb` - Thumbnail availability
- `.coupon-live` / `.coupon-expired` - Expiry status

### Badge Classes
- `.coupon-badge-expired` - Expired badge
- `.coupon-badge-featured` - Featured badge
- `.coupon-badge-code` - Code type badge
- `.coupon-badge-sale` - Sale type badge
- `.coupon-badge-print` - Print type badge

### Content Classes
- `.coupon-card-header` - Header section
- `.coupon-card-body` - Main content
- `.coupon-card-footer` - Footer section
- `.coupon-title` - Coupon title
- `.coupon-description` - Description text
- `.discount-badge` - Discount code display
- `.coupon-meta` - Meta information

### Button Classes
- `.coupon-button` - Main action button
- `.coupon-save` - Save/favorite button
- `.btn-primary` / `.btn-secondary` - Button styles

## Customization Examples

### 1. Custom Card Styles

```php
// Add custom style in CSS
.coupon-card.premium-coupon {
    border: 2px solid gold;
    background: linear-gradient(135deg, #fff, #fffbf0);
}

// Use in PHP
wpcoupon_render_coupon_card($coupon, array(
    'style' => 'premium',
    'additional_classes' => 'premium-highlight'
));
```

### 2. Custom Button Styles

```php
// Different button styles based on coupon type
$button_style = 'compact';
if ($coupon_type === 'code') {
    $button_style = 'full'; // Show full code
}

wpcoupon_render_coupon_card($coupon, array(
    'button_style' => $button_style
));
```

### 3. Conditional Features

```php
// Show different features based on user role
$show_save = is_user_logged_in();
$show_description = !wp_is_mobile();

wpcoupon_render_coupon_card($coupon, array(
    'show_save_button' => $show_save,
    'show_description' => $show_description,
    'description_length' => $show_description ? 20 : 10
));
```

## Performance Considerations

### 1. Caching

```php
// Cache coupon cards for better performance
$cache_key = 'coupon_card_' . $coupon->ID . '_home';
$cached_card = wp_cache_get($cache_key, 'coupon_cards');

if (false === $cached_card) {
    $cached_card = wpcoupon_get_coupon_card($coupon, array('style' => 'home'));
    wp_cache_set($cache_key, $cached_card, 'coupon_cards', HOUR_IN_SECONDS);
}

echo $cached_card;
```

### 2. Lazy Loading

```php
// Enable lazy loading for images
wpcoupon_render_coupon_card($coupon, array(
    'additional_classes' => 'lazy-load'
));
```

### 3. AJAX Optimization

- Use pagination instead of loading all coupons
- Implement proper caching for AJAX responses
- Use nonce verification for security
- Optimize database queries with proper indexes

## Migration from Old Code

### Before (Old Way)
```php
<div class="coupon-card">
    <div class="coupon-header">
        <div class="store-logo">...</div>
        <div class="coupon-type">...</div>
    </div>
    <div class="coupon-content">
        <h3><?php echo $coupon_title; ?></h3>
        <p><?php echo $coupon_description; ?></p>
    </div>
    <div class="coupon-footer">
        <a href="<?php echo $coupon_url; ?>">Get Coupon</a>
    </div>
</div>
```

### After (New Way)
```php
<?php wpcoupon_render_home_coupon_card($coupon); ?>
```

This component approach provides:
- ✅ **Consistency** across the theme
- ✅ **Maintainability** with centralized code
- ✅ **Flexibility** with multiple configurations
- ✅ **Reusability** in different contexts
- ✅ **AJAX functionality** built-in
- ✅ **Performance** with optimized rendering
