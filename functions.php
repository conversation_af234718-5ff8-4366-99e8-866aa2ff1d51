<?php
/**
 * WP Coupon functions and definitions
 *
 * @package WP Coupon
 */

/**
 * Define theme constants
 */
$theme_data   = wp_get_theme();
if ( $theme_data->exists() ) {
	define( 'ST_THEME_NAME', $theme_data->get( 'Name' ) );
	define( 'ST_THEME_VERSION', $theme_data->get( 'Version' ) );
}

/**
 * Check if WooCommerce is active
 * @return bool
 */
function wpcoupon_is_wc(){
	return class_exists( 'WooCommerce' );
}

/**
 * Set the content width based on the theme's design and stylesheet.
 */
if ( ! isset( $content_width ) ) {
	$content_width = 950; /* pixels */
}

//remove_filter('template_redirect', 'redirect_canonical');

if ( ! function_exists( 'wpcoupon_theme_setup' ) ) :
function wpcoupon_theme_setup() {

	/*
	 * Make theme available for translation.
	 * Translations can be filed in the /languages/ directory.
	 * If you're building a theme based on wp-coupon, use a find and replace
	 * to change 'wp-coupon' to the name of your theme in all the template files
	 */
	load_theme_textdomain( 'wp-coupon', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	// Use shortcodes in text widgets.
	add_filter( 'widget_text', 'do_shortcode' );

	/*
	 * Let WordPress manage the document title.
	 * By adding theme support, we declare that this theme does not use a
	 * hard-coded <title> tag in the document head, and expect WordPress to
	 * provide it for us.
	 */
	add_theme_support( 'title-tag' );

	/*
	 * Enable support for Post Thumbnails on posts and pages.
	 *
	 * @link http://codex.wordpress.org/Function_Reference/add_theme_support#Post_Thumbnails
	 */
	add_theme_support( 'post-thumbnails' );
	add_image_size( 'wpcoupon_small_thumb', 200, 200, false );
	add_image_size( 'wpcoupon_medium-thumb', 480, 480, false );
	add_image_size( 'wpcoupon_blog_medium', 620, 300, true );

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus( array(
		'primary' => esc_html__( 'Primary', 'wp-coupon' ),
		'footer' => esc_html__( 'Footer', 'wp-coupon' ),
	) );

	// This theme styles the visual editor to resemble the theme style.
	//add_editor_style( 'assets/css/editor-style.css' );

	/*
	 * Switch default core markup for search form, comment form, and comments
	 * to output valid HTML5.
	 */
	add_theme_support( 'html5', array(
		'search-form', 'comment-form', 'comment-list', 'gallery', 'caption',
	) );

}
endif; // wpcoupon_theme_setup
add_action( 'after_setup_theme', 'wpcoupon_theme_setup' );

/**
 * Register widget area.
 *
 * @link http://codex.wordpress.org/Function_Reference/register_sidebar
 */
function wpcoupon_widgets_init() {
	register_sidebar( array(
		'name'          => esc_html__( 'الشريط الجانبي - المدونه', 'wp-coupon' ),
		'id'            => 'sidebar',
		'description'   => '',
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h4 class="widget-title">',
		'after_title'   => '</h4>',
	) );
	register_sidebar( array(
		'name'          => esc_html__( 'الشريط الجانبي - الصفحات', 'wp-coupon' ),
		'id'            => 'sidebar-2',
		'description'   => '',
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h4 class="widget-title">',
		'after_title'   => '</h4>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'الشريط الجانبي - صفحه تصنيف الكوبون', 'wp-coupon' ),
		'id'            => 'sidebar-coupon-category',
		'description'   => esc_html__( 'The sidebar will display on coupon category, tag page.', 'wp-coupon' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h4 class="widget-title">',
		'after_title'   => '</h4>',
	) );

    register_sidebar( array(
        'name'          => esc_html__( 'الشريط الجانبي - صفحه المتجر', 'wp-coupon' ),
        'id'            => 'sidebar-store',
        //'description'   => esc_html__( 'The sidebar will display on single store, coupon category.', 'wp-coupon' ),
        'before_widget' => '<aside id="%1$s" class="widget %2$s">',
        'after_widget'  => '</aside>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ) );
	register_sidebar( array(
		'name'          => esc_html__( 'الشريط الجانبي - صفحه الكوبون', 'wp-coupon' ),
		'id'            => 'sidebar-coupon',
		'description'   => esc_html__( 'الشريط الجانبي صفحه الكوبون', 'wp-coupon' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h4 class="widget-title">',
		'after_title'   => '</h4>',
	) );






	register_sidebar( array(
		'name'          => esc_html__( 'عمود 1 فوتر', 'wp-coupon' ),
		'id'            => 'footer-1',
		'description'   => wpcoupon_sidebar_desc( 'footer-1' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );
	register_sidebar( array(
		'name'          => esc_html__( 'عمود 2 فوتر', 'wp-coupon' ),
		'id'            => 'footer-2',
		'description'   => wpcoupon_sidebar_desc( 'footer-2' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );
	register_sidebar( array(
		'name'          => esc_html__( 'عمود 3 فوتر', 'wp-coupon' ),
		'id'            => 'footer-3',
		'description'   => wpcoupon_sidebar_desc( 'footer-3' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );
	register_sidebar( array(
		'name'          => esc_html__( 'عمود 4 فوتر', 'wp-coupon' ),
		'id'            => 'footer-4',
		'description'   => wpcoupon_sidebar_desc( 'footer-4' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	// Frontpage sidebar
	register_sidebar( array(
		'name'          => esc_html__( 'الرئيسيه - قبل المحتوي', 'wp-coupon' ),
		'id'            => 'frontpage-before-main',
		'description'   => esc_html__( 'This sidebar display on frontpage template', 'wp-coupon' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'محتوي الصفحه الرئيسية', 'wp-coupon' ),
		'id'            => 'frontpage-main',
		'description'   => esc_html__( 'This sidebar display on frontpage template', 'wp-coupon' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'الرئيسية - بعد المحتوي', 'wp-coupon' ),
		'id'            => 'frontpage-after-main',
		'description'   => esc_html__( 'This sidebar display on frontpage template', 'wp-coupon' ),
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

}
add_action( 'widgets_init', 'wpcoupon_widgets_init' );



function register_custom_widget_area() {
	register_sidebar(
	array(
	'id' => 'new-widget-area',
	'name' => esc_html__( 'My new widget area', 'theme-domain' ),
	'description' => esc_html__( 'A new widget area made for testing purposes', 'theme-domain' ),
	'before_widget' => '<div id="%1$s" class="widget %2$s">',
	'after_widget' => '</div>',
	'before_title' => '<div class="widget-title-holder"><h3 class="widget-title">',
	'after_title' => '</h3></div>'
	)
	);
	}
	add_action( 'widgets_init', 'register_custom_widget_area' );


/**
 * Enqueue Google Fonts based on language direction
 */
function wpcoupon_enqueue_fonts() {
    $is_rtl = is_rtl();

    if ($is_rtl) {
        // RTL: Readex Pro font
        wp_enqueue_style(
            'wpcoupon-google-fonts-rtl',
            'https://fonts.googleapis.com/css2?family=Readex+Pro:wght@300;400;500;600;700&display=swap',
            array(),
            null
        );
    } else {
        // LTR: Jost font
        wp_enqueue_style(
            'wpcoupon-google-fonts-ltr',
            'https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700&display=swap',
            array(),
            null
        );
    }
}

/**
 * Enqueue directional CSS based on language
 */
function wpcoupon_enqueue_directional_css() {
    $version = wp_get_theme()->get('Version');
    $is_rtl = is_rtl();

    if ($is_rtl) {
        wp_enqueue_style( 'wpcoupon-rtl', get_template_directory_uri() . '/assets/css/rtl.css', array('wpcoupon_tailwind', 'wpcoupon_theme_consolidated'), $version );
    } else {
        wp_enqueue_style( 'wpcoupon-ltr', get_template_directory_uri() . '/assets/css/ltr.css', array('wpcoupon_tailwind', 'wpcoupon_theme_consolidated'), $version );
    }
}

/**
 * Enqueue scripts and styles.
 */
function wpcoupon_theme_scripts() {

    $theme = wp_get_theme();
    $version =  $theme->get( 'Version' );
    $version = apply_filters( 'wpcoupon_script_version', $version );

    // Google Fonts (conditional based on language direction)
    wpcoupon_enqueue_fonts();

    // Main Tailwind CSS (compiled styles) - Load first
    wp_enqueue_style( 'wpcoupon_tailwind', get_template_directory_uri() .'/assets/css/style.css', array(), $version );

    // Consolidated theme styles (includes enhancements, mobile, mega menu fixes)
    wp_enqueue_style( 'wpcoupon_theme_consolidated', get_template_directory_uri() .'/assets/css/theme-consolidated.css', array('wpcoupon_tailwind'), $version );

    // Clean Mega Menu CSS (No duplicates, 3 columns, clickable items)
    wp_enqueue_style( 'wpcoupon_mega_menu_clean', get_template_directory_uri() . '/assets/css/mega-menu-clean.css', array( 'wpcoupon_tailwind' ), $version );

    // Directional CSS (RTL or LTR) - Consolidated directional styles
    wpcoupon_enqueue_directional_css();

	// Stylesheet - WordPress theme header (required) - Load last for overrides
    wp_enqueue_style( 'wpcoupon_style', get_template_directory_uri().'/style.css', array('wpcoupon_tailwind', 'wpcoupon_theme_consolidated'), $version );

    // Modern JavaScript - Optimized functionality
    wp_enqueue_script( 'wpcoupon_theme', get_template_directory_uri() . '/assets/js/theme.js', array(), $version, true );

    // OLD GLOBAL.JS - Disabled main functionality, utility functions only (Load first for ST object)
    wp_enqueue_script( 'wpcoupon_global', get_template_directory_uri() . '/assets/js/global.js', array( 'jquery' ), $version, true );

    // NEW CLEAN IMPLEMENTATION - Load after global.js to access ST object
    wp_enqueue_script( 'wpcoupon_new_system', get_template_directory_uri() . '/assets/js/ag-coupon-new.js', array( 'jquery', 'wpcoupon_global' ), $version, true );

    // Compatibility fix for AJAX, voting, copy, and affiliate links (CRITICAL - Load after global.js)
    wp_enqueue_script( 'wpcoupon_compatibility_fix', get_template_directory_uri() . '/assets/js/compatibility-fix.js', array( 'jquery', 'wpcoupon_global' ), $version, true );

    // Enhanced core functionality is now integrated into ag-coupon-new.js

    // Component integration (Load after new system)
    wp_enqueue_script( 'wpcoupon_component_integration', get_template_directory_uri() . '/assets/js/component-integration.js', array( 'wpcoupon_new_system' ), $version, true );

    // Enhanced Mega Menu JavaScript
    wp_enqueue_script( 'wpcoupon_mega_menu', get_template_directory_uri() . '/assets/js/mega-menu.js', array( 'jquery' ), $version, true );

    // Mobile Menu Handler JavaScript
    wp_enqueue_script( 'wpcoupon_mobile_menu', get_template_directory_uri() . '/assets/js/mobile-menu-handler.js', array( 'jquery' ), $version, true );

    // Mega Menu Accessibility JavaScript
    wp_enqueue_script( 'wpcoupon_mega_menu_accessibility', get_template_directory_uri() . '/assets/js/mega-menu-accessibility.js', array( 'jquery' ), $version, true );

    // Enhanced Coupon Interactions (Scratch effects, voting, tracking)
    wp_enqueue_script( 'wpcoupon_enhanced_coupon_interactions', get_template_directory_uri() . '/assets/js/enhanced-coupon-interactions.js', array( 'jquery', 'wpcoupon_global' ), $version, true );

    // Splide Slider CSS and JS (for store sliders)
    wp_enqueue_style( 'splide-css', 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css', array(), '4.1.4' );
    wp_enqueue_script( 'splide-js', 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js', array(), '4.1.4', true );

    // Home Page Specific CSS and JS (only on home page)
    if (is_front_page() || is_page_template('templates/NewHome.php')) {
        wp_enqueue_style( 'wpcoupon_home_page', get_template_directory_uri() . '/assets/css/home-page.css', array( 'wpcoupon_tailwind' ), $version );
        wp_enqueue_script( 'wpcoupon_home_page_js', get_template_directory_uri() . '/assets/js/home-page.js', array( 'jquery' ), $version, true );
    }

    // Enhanced AJAX functionality is now integrated into ag-coupon-new.js

    // WordPress comment reply script
    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }

    $localize =  array(
        'ajax_url'        => admin_url( 'admin-ajax.php' ),
        'home_url'        => home_url('/'),
        'enable_single'   =>  wpcoupon_is_single_enable(),
        'auto_open_coupon_modal'   =>  wpcoupon_get_option( 'auto_open_coupon_modal' ) ?  1 : '',
        'vote_expires'    => apply_filters( 'st_coupon_vote_expires', 7 ), // 7 days
        '_wpnonce'        => wp_create_nonce('wpcoupon_ajax_nonce'),
        'user_logedin'    => is_user_logged_in(),
        'added_favorite'  => esc_html__( 'Favorited', 'wp-coupon' ),
        'add_favorite'    => esc_html__( 'Favorite This Store', 'wp-coupon' ),
        'login_warning'   => esc_html__( 'Please login to continue...', 'wp-coupon' ),
        'save_coupon'     => esc_html__( 'Save this coupon', 'wp-coupon' ),
        'saved_coupon'    => esc_html__( 'Coupon Saved', 'wp-coupon' ),
        'no_results'      => esc_html__( 'No results...', 'wp-coupon' ),
        'copied'          => esc_html__( 'تم النسخ', 'wp-coupon' ),
        'copy'            => esc_html__( 'Copy', 'wp-coupon' ),
        'print_prev_tab'  => wpcoupon_get_option( 'print_prev_tab', false ) ?  1 : 0, // open store website in previous tab.
        'sale_prev_tab'   => wpcoupon_get_option( 'sale_prev_tab', true ) ?  1 : 0, // open store website in previous tab.
        'code_prev_tab'   => wpcoupon_get_option( 'code_prev_tab', true ) ?  1 : 0, // open store website in previous tab.
        'share_id'        => 0, // open store website in previous tab.
        'header_sticky'   => wpcoupon_get_option( 'header_sticky', false ), // open store website in previous tab.
    ) ;
    $list = '';
    if ( is_user_logged_in() ){
        $user = wp_get_current_user();
        $list  = get_user_meta( $user->ID, '_wpc_saved_coupons' , true );
        $stores = get_user_meta( $user->ID, '_wpc_favorite_stores' , true );
        $localize['my_saved_coupons'] =  explode( ',', $list );
        $localize['my_favorite_stores'] =  explode( ',', $stores );
    } else {
        $localize['my_saved_coupons'] = array();
        $localize['my_favorite_stores'] = array();
    }

    if ( is_tax( 'coupon_store' ) ) {
        global $wp_rewrite;
        if ( $wp_rewrite->using_permalinks() ){
            $share_id = get_query_var( 'share_id' );
            $coupon_id = get_query_var( 'coupon_id' );
        } else {
            $share_id = absint( $_GET['share_id'] );
            $coupon_id = absint( $_GET['coupon_id'] );
        }
        $localize['share_id'] = $share_id;
        $localize['coupon_id'] = $coupon_id;
    }

    if(  $localize['enable_single'] ) {
        if ( is_singular( 'coupon' ) ) {
            global $post;
            $localize['current_coupon_id'] = $post->ID;
        }
    }

	$localize['my_saved_coupons'] = explode( ',', $list );
    wp_localize_script( 'wpcoupon_global', 'ST', apply_filters( 'wp_coupon_localize_script', $localize ) );

    // Enhanced AJAX localization is now handled by ag-coupon-new.js through ST object

}
add_action( 'wp_enqueue_scripts', 'wpcoupon_theme_scripts' );


/**
 * Helper lib
 */
require_once get_template_directory() . '/inc/core/helper.php';

/**
 * Theme Options
 */
if ( class_exists( 'ReduxFramework' ) ) {
    require_once( get_template_directory() . '/inc/config/option-config.php' );
}



// Retrieve theme option values
if ( ! function_exists('wpcoupon_get_option') ) {
	function wpcoupon_get_option($id, $fallback = false, $key = false ) {
		global $st_option;
		if ( ! $st_option ) {
			$st_option = get_option('st_options');
		}
        if ( ! is_array( $st_option ) ) {
            return $fallback;
        }
		if ( $fallback == false ) $fallback = '';
		$output = ( isset($st_option[$id]) && $st_option[$id] !== '' ) ? $st_option[$id] : $fallback;
		if ( !empty($st_option[$id]) && $key ) {
			$output = $st_option[$id][$key];
		}
		return $output;
	}
}


/**
 * Support coupon type
 * @return array
 */
function wpcoupon_get_coupon_types( $plural = false ){

    $deal = wpcoupon_get_option( 'use_deal_txt', false );

    if ( $plural ) {
        $types = array(
            'code'       => esc_html__( 'Codes', 'wp-coupon' ),
            'sale'       => esc_html__( 'Sales', 'wp-coupon' ),
        );
        if ( $deal ) {
            $types[ 'sale' ] = esc_html__( 'Deals', 'wp-coupon' );
        }
    } else {
        $types = array(
            'code'       => esc_html__( 'Code', 'wp-coupon' ),
            'sale'       => esc_html__( 'Sale', 'wp-coupon' ),
        );
        if ( $deal ) {
            $types[ 'sale' ] = esc_html__( 'Deal', 'wp-coupon' );
        }
    }
    return apply_filters( 'wpcoupon_get_coupon_types', $types, $plural );
}

/**
 * Recommend plugins via TGM activation class
 */
require_once get_template_directory() . '/inc/tgmpa/tgmpa-config.php';


/**
 * Post type
 */
require_once get_template_directory() . '/inc/post-type.php';


/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/coupon.php';

/**
 * Store functions.
 */
require_once get_template_directory() . '/inc/core/store.php';

/**
 * Enhanced Store Functions with Deduplication
 */
require_once get_template_directory() . '/inc/components/simple-store-functions.php';

/**
 * Modern Coupon Card System - THE ONLY coupon card system
 * Creative modern design with branded yellow colors
 */
require_once get_template_directory() . '/inc/components/modern-coupon-card.php';

/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/sharing.php';

/**
 * Search functions.
 */
require_once get_template_directory() . '/inc/core/search.php';


/**
 * Ajax handle
 */
require_once get_template_directory() . '/inc/core/ajax.php';




/**
 * Schedule event
 */
require_once get_template_directory() . '/inc/core/schedule-event.php';

/**
 * Auto update - Removed for theme optimization
 * Theme updates are handled through WordPress.org or manual updates
 */



/**
 * Load user functions
 */
require_once get_template_directory() . '/inc/user/user.php';


/**
 *Theme Hooks
 */
require_once get_template_directory() . '/inc/core/hooks.php';

/**
 * Custom template tags for this theme.
 */
require_once get_template_directory() . '/inc/template-tags.php';

/**
 * Template parts are now organized in template-parts/ folder
 * Following WordPress standard structure
 */

/**
 * Custom CSS, JS, .. code
 */
require_once get_template_directory() . '/inc/custom-code.php';


/**
 * Custom functions that act independently of the theme templates.
 */
require_once get_template_directory() . '/inc/extras.php';

/**
 * Load custom metaboxes config.
 */
require_once get_template_directory() . '/inc/config/metabox-config.php';



/**
 * Theme Support Widget
 */
add_action('wp_dashboard_setup', 'my_custom_dashboard_widgets');

function my_custom_dashboard_widgets() {
global $wp_meta_boxes;

wp_add_dashboard_widget('custom_help_widget', 'الدعم الفني للقالب', 'custom_dashboard_help');
}

function custom_dashboard_help() {
echo '<p>انت الان تستخدم قالب Ag Coupon ! إذا كنت تحتاج مساعده ؟ تواصل مع المطور <a href="https://www.facebook.com/Mr.LaLa.Man">Abdullah Gamal</a>. </p>';
}

/**
 * Development helper file
 */
require_once get_template_directory() . '/out.php';

/**
 * Enhanced AJAX functionality is now integrated into ag-coupon-new.js
 */

/**
 * SEO and Performance Enhancements
 */
require_once get_template_directory() . '/inc/seo-enhancements.php';

/**
 * Production CSS and AJAX status
 */
function wpcoupon_production_status() {
    if (WP_DEBUG) {
        echo '<!-- Ag-Coupon Theme: Production Ready -->';
        echo '<!-- Header: Modular Template Parts System -->';
        echo '<!-- Template Parts: site-branding, search-form, header-actions, desktop-navigation, mobile-overlays -->';
        echo '<!-- CSS System: Fully Optimized (3 files total) -->';
        echo '<!-- 1. Tailwind CSS (compiled base) -->';
        echo '<!-- 2. Theme Consolidated (all enhancements + mega menu + mobile) -->';
        echo '<!-- 3. Directional CSS (LTR/RTL) -->';
        echo '<!-- Desktop Menu: Hidden on Mobile (lg:hidden) -->';
        echo '<!-- Mobile Menu: Hamburger Popup with Same Desktop Menu Structure -->';
        echo '<!-- Mega Menu Items: Simplified DOM - Icon + Title (clickable) -->';
        echo '<!-- No Styling: Removed shadows, borders, padding, margins -->';
        echo '<!-- JavaScript: Mobile menu handler + Mega menu interactions -->';
        echo '<!-- AJAX Enhanced: Active -->';
        echo '<!-- SEO Optimized: Active -->';
        echo '<!-- Accessibility: WCAG 2.1 AA Compliant -->';
        echo '<!-- RTL/LTR: ' . (is_rtl() ? 'RTL' : 'LTR') . ' -->';
    }
}
add_action('wp_head', 'wpcoupon_production_status');

/**
 * Calculate reading time for content
 */
if (!function_exists('wpcoupon_get_reading_time')) {
    function wpcoupon_get_reading_time($content) {
        $word_count = str_word_count(strip_tags($content));
        $reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute
        return $reading_time;
    }
}

/**
 * Get store thumbnail URL
 */
if (!function_exists('wpcoupon_get_store_thumbnail')) {
    function wpcoupon_get_store_thumbnail($term_id) {
        $thumbnail = get_term_meta($term_id, 'store_thumb', true);
        if ($thumbnail) {
            return wp_get_attachment_url($thumbnail);
        }
        return false;
    }
}

/**
 * Get category icon
 */
if (!function_exists('wpcoupon_get_category_icon')) {
    function wpcoupon_get_category_icon($term_id) {
        $icon = get_term_meta($term_id, 'category_icon', true);
        if ($icon) {
            return $icon;
        }
        return false;
    }
}

/**
 * Get menu item icon or thumbnail
 */
if (!function_exists('wpcoupon_get_menu_item_media')) {
    function wpcoupon_get_menu_item_media($menu_item) {
        $media = array(
            'type' => 'none',
            'url' => '',
            'icon' => ''
        );

        if ($menu_item->object === 'coupon_store') {
            $thumbnail = wpcoupon_get_store_thumbnail($menu_item->object_id);
            if ($thumbnail) {
                $media['type'] = 'thumbnail';
                $media['url'] = $thumbnail;
            }
        } elseif ($menu_item->object === 'coupon_category') {
            $icon = wpcoupon_get_category_icon($menu_item->object_id);
            if ($icon) {
                $media['type'] = 'icon';
                $media['icon'] = $icon;
            }
        }

        return $media;
    }
}

/**
 * Add body classes for RTL/LTR direction
 */
function wpcoupon_body_classes($classes) {
    if (is_rtl()) {
        $classes[] = 'rtl';
        $classes[] = 'readex-pro-font';
    } else {
        $classes[] = 'ltr';
        $classes[] = 'jost-font';
    }

    // Add page-specific classes
    if (is_singular('coupon')) {
        $classes[] = 'single-coupon';
    }

    if (is_tax('coupon_store')) {
        $classes[] = 'store-archive';
    }

    if (is_front_page()) {
        $classes[] = 'homepage';
    }

    return $classes;
}
add_filter('body_class', 'wpcoupon_body_classes');

/**
 * Add HTML dir attribute
 */
function wpcoupon_html_dir_attribute() {
    if (is_rtl()) {
        echo ' dir="rtl"';
    } else {
        echo ' dir="ltr"';
    }
}

/**
 * Preload Google Fonts for better performance
 */
function wpcoupon_preload_fonts() {
    $is_rtl = is_rtl();

    if ($is_rtl) {
        echo '<link rel="preconnect" href="https://fonts.googleapis.com">';
        echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>';
        echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
    } else {
        echo '<link rel="preconnect" href="https://fonts.googleapis.com">';
        echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>';
        echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
    }
}
add_action('wp_head', 'wpcoupon_preload_fonts', 1);


add_filter( 'get_the_archive_title', function ($title) {
        if ( is_category() ) {
                $title = single_cat_title( '', false );
            } elseif ( is_tag() ) {
                $title = single_tag_title( '', false );
            } elseif ( is_author() ) {
                $title = '<span class="vcard">' . get_the_author() . '</span>' ;
            } elseif ( is_tax() ) { //for custom post types
                $title = sprintf( __( '%1$s' ), single_term_title( '', false ) );
            } elseif (is_post_type_archive()) {
                $title = post_type_archive_title( '', false );
            }
        return $title;
    });





/**
 * Function to disable SEO Tags generated by Rank Math
 */

function rankmath_disable_features() {
	if ( taxonomy_exists( 'coupon_store' ) ) {
        add_filter( 'rank_math/opengraph/pre_set_default_image', '__return_true' );
	};
};
add_action( 'wp_head', 'rankmath_disable_features', 1 );
add_filter( 'rank_math/frontend/canonical', function( $canonical ) {
        if ( is_singular( 'coupon' )) {
            return '';
        }

	return $canonical;
});