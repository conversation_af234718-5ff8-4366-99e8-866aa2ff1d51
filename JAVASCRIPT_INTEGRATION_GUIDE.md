# JavaScript Integration Guide - Ag-Coupon Theme

## Overview

This guide explains how the new class-based JavaScript system integrates with existing `global.js` functions and AJAX endpoints while maintaining backward compatibility.

## Architecture

### Core Classes

1. **AgCouponManager** - Main coupon functionality
2. **AgSearchManager** - Search and autocomplete
3. **AgFilterManager** - Filtering and tabs
4. **AgModalManager** - Modal dialogs
5. **AgContentManager** - Content expansion/collapse

### Integration Classes

1. **HeaderSearchIntegration** - Enhanced search functionality
2. **CouponCardIntegration** - Coupon card enhancements
3. **StorePageIntegration** - Store page features
4. **ModalIntegration** - Modal enhancements
5. **PerformanceIntegration** - Performance monitoring

## AJAX Integration

### Existing AJAX Functions Preserved

All existing AJAX functions from `global.js` are preserved and enhanced:

```javascript
// Original global.js AJAX calls still work
$.ajax({
    action: "wpcoupon_coupon_ajax",
    st_doing: "vote_coupon",
    // ... other parameters
});

// New class-based approach (same endpoint)
await agCouponManager.makeAjaxRequest({
    st_doing: "vote_coupon",
    // ... other parameters
});
```

### AJAX Endpoints Used

| Action | st_doing | Description | Class Method |
|--------|----------|-------------|--------------|
| `wpcoupon_coupon_ajax` | `vote_coupon` | Vote for coupon | `AgCouponManager.voteCoupon()` |
| `wpcoupon_coupon_ajax` | `save_coupon` | Save coupon | `AgCouponManager.saveCoupon()` |
| `wpcoupon_coupon_ajax` | `remove_saved_coupon` | Remove saved coupon | `AgCouponManager.saveCoupon()` |
| `wpcoupon_coupon_ajax` | `load_coupons` | Load more coupons | `AgCouponManager.loadMoreCoupons()` |
| `wpcoupon_coupon_ajax` | `load_category_coupons` | Load category coupons | `AgCouponManager.loadMoreCoupons()` |
| `wpcoupon_coupon_ajax` | `get_coupon_modal` | Get coupon modal | `AgCouponManager.openCouponModal()` |
| `wpcoupon_coupon_ajax` | `get_coupon_comments` | Load comments | Preserved in global.js |
| `wpcoupon_coupon_ajax` | `send_mail_to_friend` | Email coupon | Preserved in global.js |
| `wpcoupon_coupon_ajax` | `add_favorite` | Add store to favorites | Preserved in global.js |
| `wpcoupon_coupon_ajax` | `delete_favorite` | Remove from favorites | Preserved in global.js |

### Search AJAX Integration

```javascript
// Original search (still works)
ajax_search_coupons(query, form);

// New class-based search
agSearchManager.performSearch(query, form);
```

## Component Integration Examples

### 1. Coupon Card Component

```php
<!-- In template-parts/loop/loop-coupon.php -->
<div class="coupon-card" data-coupon-id="<?php echo $coupon_obj->ID; ?>">
    <!-- Coupon content -->
    <button class="coupon-button" 
            data-coupon-id="<?php echo $coupon_obj->ID; ?>"
            data-type="<?php echo $coupon_type; ?>"
            data-code="<?php echo esc_attr($coupon_code); ?>"
            data-aff-url="<?php echo esc_attr($coupon_obj->get_destination_url()); ?>">
        عرض الكوبون
    </button>
</div>
```

JavaScript automatically handles:
- Copy to clipboard
- Modal opening
- Loading states
- Error handling

### 2. Search Component

```php
<!-- In template-parts/header/search.php -->
<form class="header-search-input">
    <input type="text" name="s" class="prompt" placeholder="البحث عن كوبونات...">
    <button type="submit" class="button">
        <i class="search icon"></i>
    </button>
    <!-- Results container added automatically -->
</form>
```

JavaScript automatically handles:
- Debounced search
- Results display
- Loading states
- Recent searches

### 3. Filter Component

```php
<!-- In store pages -->
<div class="filter-coupons-by-type" data-target=".store-listings">
    <a href="#" data-filter="all" class="active">الكل</a>
    <a href="#" data-filter="code">كوبونات</a>
    <a href="#" data-filter="sale">عروض</a>
    <a href="#" data-filter="print">قابلة للطباعة</a>
</div>
```

JavaScript automatically handles:
- Filter switching
- Item visibility
- Active states
- Statistics updates

## Backward Compatibility

### Global Functions Still Available

```javascript
// These functions still work exactly as before
copyText(text);
setCookie(name, value, days);
getCookie(name);
openCouponModal(id);
listingCouponItem(context);
voteCouponInit(context);
```

### jQuery Integration

The new classes work alongside existing jQuery code:

```javascript
// Existing jQuery code continues to work
$(".coupon-vote").on("click", function() {
    // Original handler
});

// New class-based handlers run in parallel
// No conflicts or interference
```

## Performance Enhancements

### 1. Modern Fetch API

```javascript
// Old XMLHttpRequest approach
$.ajax({
    url: ST.ajax_url,
    type: 'POST',
    data: formData,
    success: function(response) { /* ... */ }
});

// New Fetch API approach
const response = await fetch(ST.ajax_url, {
    method: 'POST',
    body: formData
});
const data = await response.json();
```

### 2. Event Delegation

```javascript
// Old approach - multiple event listeners
$(".coupon-button").on("click", handler);

// New approach - single delegated listener
document.addEventListener('click', (e) => {
    const target = e.target.closest('.coupon-button');
    if (target) handler(e);
});
```

### 3. Intersection Observer

```javascript
// Lazy loading for coupon cards
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            loadCouponDetails(entry.target);
        }
    });
});
```

## Error Handling

### Graceful Degradation

```javascript
// If modern features aren't available, fall back to global.js
if (!window.agCouponManager) {
    // Use original global.js functions
    copyText(code);
} else {
    // Use new class-based approach
    agCouponManager.copyToClipboard(code);
}
```

### Error Recovery

```javascript
try {
    await agCouponManager.voteCoupon(id, type);
} catch (error) {
    // Revert UI changes
    button.classList.remove('active');
    // Show user-friendly error message
    agCouponManager.showToast('حدث خطأ في التصويت', 'error');
}
```

## Usage in Templates

### 1. Include Scripts

```php
// In functions.php
wp_enqueue_script('ag-coupon-core', get_template_directory_uri() . '/assets/js/ag-coupon-core.js', ['jquery'], '1.0.0', true);
wp_enqueue_script('ag-component-integration', get_template_directory_uri() . '/assets/js/component-integration.js', ['ag-coupon-core'], '1.0.0', true);
```

### 2. Template Integration

```php
// Templates automatically work with new classes
// No changes needed to existing template code
// New features are progressively enhanced
```

### 3. Custom Events

```javascript
// Listen for custom events
document.addEventListener('st_coupon_favorite_stores_changed', () => {
    // Update UI when favorites change
});

document.addEventListener('st_coupon_saved_coupons_changed', () => {
    // Update UI when saved coupons change
});
```

## Migration Strategy

### Phase 1: Parallel Operation
- New classes run alongside global.js
- No breaking changes
- Progressive enhancement

### Phase 2: Feature Enhancement
- Add new features using class-based approach
- Maintain global.js compatibility
- Gradual adoption

### Phase 3: Optimization
- Remove redundant code
- Optimize performance
- Full class-based implementation

## Testing

### Browser Compatibility
- Modern browsers: Full class-based functionality
- Older browsers: Graceful fallback to global.js
- Progressive enhancement ensures no functionality loss

### Performance Testing
- Monitor AJAX request performance
- Track user interaction metrics
- Measure loading times and responsiveness

## Conclusion

The new class-based JavaScript system provides:

1. **Enhanced Performance** - Modern APIs and optimizations
2. **Better Maintainability** - Organized, modular code structure
3. **Progressive Enhancement** - Works with existing code
4. **Future-Proof** - Modern JavaScript standards
5. **Backward Compatibility** - No breaking changes

All existing AJAX functions and template integrations continue to work while gaining new capabilities and performance improvements.
