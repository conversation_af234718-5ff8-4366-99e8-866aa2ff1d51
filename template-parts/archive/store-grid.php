<?php
/**
 * Store Archive Grid Template Part
 * 
 * Example usage of the store card component in archive pages
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get stores for archive page
$stores = get_query_var('archive_stores', array());
$grid_style = get_query_var('grid_style', 'grid'); // grid, minimal, featured
$show_filters = get_query_var('show_filters', true);
$stores_per_row = get_query_var('stores_per_row', 4);

// Grid classes based on stores per row
$grid_classes = array(
    'stores-archive-grid',
    'grid',
    'gap-6'
);

switch ($stores_per_row) {
    case 2:
        $grid_classes[] = 'grid-cols-1 md:grid-cols-2';
        break;
    case 3:
        $grid_classes[] = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
        break;
    case 4:
        $grid_classes[] = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
        break;
    case 6:
        $grid_classes[] = 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6';
        break;
    default:
        $grid_classes[] = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
}
?>

<div class="stores-archive-container">
    
    <?php if ($show_filters) : ?>
    <!-- Store Filters -->
    <div class="stores-filters mb-8">
        <div class="filter-tabs flex flex-wrap gap-2 mb-4">
            <button class="filter-btn active" data-filter="all">
                <?php esc_html_e('جميع المتاجر', 'wp-coupon'); ?>
            </button>
            <button class="filter-btn" data-filter="featured">
                <?php esc_html_e('المتاجر المميزة', 'wp-coupon'); ?>
            </button>
            <button class="filter-btn" data-filter="popular">
                <?php esc_html_e('الأكثر شعبية', 'wp-coupon'); ?>
            </button>
            <button class="filter-btn" data-filter="new">
                <?php esc_html_e('متاجر جديدة', 'wp-coupon'); ?>
            </button>
        </div>
        
        <div class="filter-controls flex flex-wrap items-center gap-4">
            <div class="sort-control">
                <label for="store-sort" class="text-sm font-medium text-gray-700">
                    <?php esc_html_e('ترتيب حسب:', 'wp-coupon'); ?>
                </label>
                <select id="store-sort" class="ml-2 border border-gray-300 rounded-md px-3 py-1">
                    <option value="name"><?php esc_html_e('الاسم', 'wp-coupon'); ?></option>
                    <option value="coupons"><?php esc_html_e('عدد الكوبونات', 'wp-coupon'); ?></option>
                    <option value="rating"><?php esc_html_e('التقييم', 'wp-coupon'); ?></option>
                    <option value="date"><?php esc_html_e('الأحدث', 'wp-coupon'); ?></option>
                </select>
            </div>
            
            <div class="view-control">
                <label class="text-sm font-medium text-gray-700">
                    <?php esc_html_e('العرض:', 'wp-coupon'); ?>
                </label>
                <div class="view-buttons ml-2 flex gap-1">
                    <button class="view-btn active" data-view="grid" title="<?php esc_attr_e('عرض شبكي', 'wp-coupon'); ?>">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                        </svg>
                    </button>
                    <button class="view-btn" data-view="list" title="<?php esc_attr_e('عرض قائمة', 'wp-coupon'); ?>">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Stores Grid -->
    <?php if (!empty($stores)) : ?>
    <div class="<?php echo esc_attr(implode(' ', $grid_classes)); ?>" id="stores-grid">
        <?php foreach ($stores as $store) :
            // Determine store card configuration based on store properties
            wpcoupon_setup_store($store);
            $store_obj = wpcoupon_store();
            
            // Determine card style based on store properties
            $card_style = 'grid'; // Default
            $card_config = array();
            
            if (method_exists($store_obj, 'is_featured') && $store_obj->is_featured()) {
                $card_style = 'featured';
                $card_config = array(
                    'show_badge' => true,
                    'badge_text' => esc_html__('مميز', 'wp-coupon'),
                    'badge_type' => 'featured'
                );
            } elseif (method_exists($store_obj, 'get_rating') && $store_obj->get_rating() > 4) {
                $card_style = 'popular';
                $card_config = array(
                    'show_badge' => true,
                    'badge_text' => esc_html__('الأكثر شعبية', 'wp-coupon'),
                    'badge_type' => 'popular'
                );
            }
            
            // Base configuration for archive grid
            $base_config = array(
                'style' => $card_style,
                'show_rating' => true,
                'show_overlay' => true,
                'link_style' => 'overlay',
                'coupon_text_style' => 'count',
                'image_size' => 'medium',
                'additional_classes' => 'archive-store-card'
            );
            
            // Merge configurations
            $final_config = array_merge($base_config, $card_config);
            
            // Render the store card
            wpcoupon_render_store_card($store, $final_config);
            
        endforeach; ?>
    </div>
    
    <!-- Load More Button -->
    <div class="load-more-container mt-8 text-center">
        <button class="load-more-stores btn-secondary">
            <?php esc_html_e('تحميل المزيد من المتاجر', 'wp-coupon'); ?>
            <svg class="inline w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </button>
    </div>
    
    <?php else : ?>
    <!-- No Stores Message -->
    <div class="no-stores-found text-center py-12">
        <div class="max-w-md mx-auto">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
                <?php esc_html_e('لا توجد متاجر', 'wp-coupon'); ?>
            </h3>
            <p class="text-gray-600">
                <?php esc_html_e('لم يتم العثور على متاجر تطابق معايير البحث الخاصة بك.', 'wp-coupon'); ?>
            </p>
            <a href="<?php echo esc_url(home_url('/')); ?>" class="inline-block mt-4 btn-primary">
                <?php esc_html_e('العودة للرئيسية', 'wp-coupon'); ?>
            </a>
        </div>
    </div>
    <?php endif; ?>
    
</div>

<script>
// Store archive functionality
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const filterBtns = document.querySelectorAll('.filter-btn');
    const storeCards = document.querySelectorAll('.archive-store-card');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active state
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const filter = this.dataset.filter;
            
            // Filter stores
            storeCards.forEach(card => {
                if (filter === 'all' || card.classList.contains(filter + '-store')) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
    
    // View toggle functionality
    const viewBtns = document.querySelectorAll('.view-btn');
    const storesGrid = document.getElementById('stores-grid');
    
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            viewBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const view = this.dataset.view;
            
            if (view === 'list') {
                storesGrid.classList.remove('grid', 'grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4');
                storesGrid.classList.add('space-y-4');
            } else {
                storesGrid.classList.add('grid', 'grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4');
                storesGrid.classList.remove('space-y-4');
            }
        });
    });
    
    // Sort functionality
    const sortSelect = document.getElementById('store-sort');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            // Implement sorting logic here
            console.log('Sort by:', this.value);
        });
    }
});
</script>
