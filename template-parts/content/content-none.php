<?php
/**
 * Template part for displaying a message that posts cannot be found
 *
 * @package Ag-Coupon
 */
?>

<div class="no-content-found text-center py-16">
    <div class="max-w-md mx-auto">
        <div class="mb-6">
            <svg class="w-20 h-20 mx-auto text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
        </div>
        
        <h2 class="text-2xl font-bold text-gray-900 mb-4">
            <?php 
            if (is_search()) {
                esc_html_e('لا توجد نتائج', 'wp-coupon');
            } elseif (is_home() && current_user_can('publish_posts')) {
                esc_html_e('جاهز للنشر؟', 'wp-coupon');
            } else {
                esc_html_e('لا يوجد محتوى', 'wp-coupon');
            }
            ?>
        </h2>
        
        <p class="text-gray-600 mb-6">
            <?php 
            if (is_search()) {
                printf(
                    esc_html__('لم نجد أي نتائج لـ "%s". جرب كلمات مختلفة أو تصفح الأقسام.', 'wp-coupon'),
                    '<strong>' . get_search_query() . '</strong>'
                );
            } elseif (is_home() && current_user_can('publish_posts')) {
                printf(
                    wp_kses(
                        __('جاهز لنشر أول مقال؟ <a href="%1$s">ابدأ من هنا</a>.', 'wp-coupon'),
                        array(
                            'a' => array(
                                'href' => array(),
                            ),
                        )
                    ),
                    esc_url(admin_url('post-new.php'))
                );
            } else {
                esc_html_e('عذراً، لا يوجد محتوى متاح في هذا القسم حالياً.', 'wp-coupon');
            }
            ?>
        </p>
        
        <?php if (is_search()) { ?>
            <div class="search-suggestions mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php esc_html_e('اقتراحات البحث:', 'wp-coupon'); ?></h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><?php esc_html_e('• تأكد من صحة الكتابة', 'wp-coupon'); ?></li>
                    <li><?php esc_html_e('• جرب كلمات مختلفة أو أكثر عمومية', 'wp-coupon'); ?></li>
                    <li><?php esc_html_e('• استخدم كلمات أقل في البحث', 'wp-coupon'); ?></li>
                </ul>
            </div>
        <?php } ?>
        
        <div class="action-buttons space-y-3">
            <a href="<?php echo esc_url(home_url('/')); ?>" 
               class="btn-primary inline-flex items-center px-6 py-3 rounded-lg font-semibold">
                <?php esc_html_e('العودة للرئيسية', 'wp-coupon'); ?>
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            
            <?php if (is_search()) { ?>
                <div class="mt-4">
                    <a href="<?php echo esc_url(get_post_type_archive_link('coupon')); ?>" 
                       class="btn-secondary inline-flex items-center px-4 py-2 rounded-lg text-sm">
                        <?php esc_html_e('تصفح جميع الكوبونات', 'wp-coupon'); ?>
                    </a>
                </div>
            <?php } ?>
        </div>
    </div>
</div>
