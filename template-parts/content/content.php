<?php
/**
 * Template part for displaying content
 *
 * @package Ag-Coupon
 */

global $post;
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('content-article bg-white rounded-xl shadow-coupon p-6 mb-8'); ?>>
    <?php if (has_post_thumbnail() && !is_page()) { ?>
        <div class="featured-image mb-6">
            <a href="<?php the_permalink(); ?>" class="block overflow-hidden rounded-lg">
                <?php the_post_thumbnail('large', array('class' => 'w-full h-auto hover:scale-105 transition-transform duration-300')); ?>
            </a>
        </div>
    <?php } ?>
    
    <header class="content-header mb-6">
        <?php if (!is_page()) { ?>
            <div class="post-meta mb-3">
                <div class="flex flex-wrap items-center text-sm text-gray-600 space-x-4 space-x-reverse">
                    <span class="author flex items-center">
                        <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                        <?php the_author(); ?>
                    </span>
                    <span class="date flex items-center">
                        <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo get_the_date(); ?>
                    </span>
                    <?php if (has_category()) { ?>
                        <span class="categories">
                            <?php the_category(', '); ?>
                        </span>
                    <?php } ?>
                </div>
            </div>
        <?php } ?>
        
        <?php if (is_singular()) { ?>
            <h1 class="content-title text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                <?php the_title(); ?>
            </h1>
        <?php } else { ?>
            <h2 class="content-title text-2xl font-bold text-gray-900 mb-4">
                <a href="<?php the_permalink(); ?>" class="hover:text-primary-600 transition-colors duration-200">
                    <?php the_title(); ?>
                </a>
            </h2>
        <?php } ?>
    </header>
    
    <div class="content-body prose prose-gray max-w-none">
        <?php
        if (is_singular()) {
            the_content();
            
            wp_link_pages(array(
                'before' => '<div class="page-links mt-6 p-4 bg-gray-50 rounded-lg"><span class="page-links-title font-semibold text-gray-900">' . esc_html__('الصفحات:', 'wp-coupon') . '</span>',
                'after'  => '</div>',
                'link_before' => '<span class="page-link inline-block px-3 py-1 mx-1 bg-white border border-gray-300 rounded hover:bg-primary-50 transition-colors duration-200">',
                'link_after'  => '</span>',
            ));
        } else {
            the_excerpt();
        }
        ?>
    </div>
    
    <?php if (is_singular() && !is_page()) { ?>
        <footer class="content-footer mt-8 pt-6 border-t border-gray-200">
            <div class="flex flex-wrap items-center justify-between">
                <?php if (has_tag()) { ?>
                    <div class="post-tags">
                        <span class="text-sm font-medium text-gray-700 ml-2"><?php esc_html_e('الوسوم:', 'wp-coupon'); ?></span>
                        <?php
                        $tags = get_the_tags();
                        if ($tags) {
                            foreach ($tags as $tag) {
                                echo '<a href="' . esc_url(get_tag_link($tag->term_id)) . '" class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full mr-2 mb-2 hover:bg-primary-100 hover:text-primary-700 transition-colors duration-200">' . esc_html($tag->name) . '</a>';
                            }
                        }
                        ?>
                    </div>
                <?php } ?>
                
                <div class="post-share">
                    <span class="text-sm font-medium text-gray-700 ml-2"><?php esc_html_e('شارك:', 'wp-coupon'); ?></span>
                    <div class="inline-flex space-x-2 space-x-reverse">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" 
                           target="_blank" rel="noopener"
                           class="text-blue-600 hover:text-blue-700 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" 
                           target="_blank" rel="noopener"
                           class="text-blue-400 hover:text-blue-500 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    <?php } ?>
</article>
