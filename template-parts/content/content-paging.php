<?php
/**
 * Template part for displaying pagination
 *
 * @package Ag-Coupon
 */

global $wp_query;

if ($wp_query->max_num_pages <= 1) {
    return;
}

$paged = get_query_var('paged') ? absint(get_query_var('paged')) : 1;
$max_pages = $wp_query->max_num_pages;
?>

<nav class="pagination-nav mt-12" aria-label="<?php esc_attr_e('تصفح الصفحات', 'wp-coupon'); ?>">
    <div class="flex items-center justify-center">
        <?php
        $pagination_args = array(
            'base'      => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
            'format'    => '?paged=%#%',
            'current'   => $paged,
            'total'     => $max_pages,
            'prev_text' => '<span class="sr-only">' . esc_html__('الصفحة السابقة', 'wp-coupon') . '</span><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',
            'next_text' => '<span class="sr-only">' . esc_html__('الصفحة التالية', 'wp-coupon') . '</span><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>',
            'type'      => 'array',
            'mid_size'  => 2,
            'end_size'  => 1,
        );
        
        $pagination_links = paginate_links($pagination_args);
        
        if ($pagination_links) {
            echo '<div class="flex items-center space-x-1 space-x-reverse">';
            
            foreach ($pagination_links as $link) {
                // Determine if this is the current page
                $is_current = strpos($link, 'current') !== false;
                $is_dots = strpos($link, '&hellip;') !== false || strpos($link, '…') !== false;
                
                // Base classes for all pagination items
                $classes = 'pagination-link relative inline-flex items-center px-4 py-2 text-sm font-medium transition-colors duration-200';
                
                if ($is_current) {
                    $classes .= ' bg-primary-300 text-primary-900 z-10';
                } elseif ($is_dots) {
                    $classes .= ' text-gray-500 cursor-default';
                } else {
                    $classes .= ' text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-primary-600';
                }
                
                // Add rounded corners for first and last items
                if (strpos($link, 'prev') !== false) {
                    $classes .= ' rounded-r-md';
                } elseif (strpos($link, 'next') !== false) {
                    $classes .= ' rounded-l-md';
                }
                
                // Output the link with updated classes
                echo str_replace('page-numbers', $classes, $link);
            }
            
            echo '</div>';
        }
        ?>
    </div>
    
    <!-- Page Info -->
    <div class="text-center mt-4">
        <p class="text-sm text-gray-600">
            <?php
            printf(
                esc_html__('الصفحة %1$s من %2$s', 'wp-coupon'),
                '<span class="font-medium">' . $paged . '</span>',
                '<span class="font-medium">' . $max_pages . '</span>'
            );
            ?>
        </p>
    </div>
</nav>

<style>
/* Custom pagination styles */
.pagination-nav .pagination-link {
    border-radius: 0;
    margin-left: -1px;
}

.pagination-nav .pagination-link:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    margin-left: 0;
}

.pagination-nav .pagination-link:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.pagination-nav .pagination-link:hover {
    z-index: 5;
}

.pagination-nav .pagination-link.current {
    z-index: 10;
}
</style>
