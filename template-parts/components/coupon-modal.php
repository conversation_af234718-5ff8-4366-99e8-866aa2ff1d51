<!-- Coupon Modal -->
<div data-modal-id="<?php echo wpcoupon_coupon()->ID; ?>" class="ui modal coupon-modal coupon-code-modal">
    <div class="coupon-header clearfix">
        <div class="coupon-store-thumb">
            <?php
            echo wpcoupon_coupon()->get_thumb( );
            ?>
        </div>
        <div class="coupon-title" title="<?php echo esc_attr( get_the_title( wpcoupon_coupon()->ID ) ) ?>"><?php echo get_the_title( wpcoupon_coupon()->ID ); ?></div>
        <span class="cancel close"></span>
    </div>
    <div class="coupon-content">
    <p>
 <?php
   echo "✅ تم تجربه  "; echo esc_attr( get_the_title( wpcoupon_coupon()->ID ) ); echo " 👍 اخر مره في تاريخ "; 
 ?> 
 <?php echo   date( 'Y-m-d', current_time( 'timestamp', 1 ) ); ?>
 <?php echo " الوقت " ?>
 <?php echo   date( 'H:i', current_time( 'timestamp', 1 ) ); ?>
            
</p>
        
        <p class="coupon-type-text">
            <?php
            switch ( wpcoupon_coupon()->get_type() ) {
                case 'sale':
                    esc_html_e( 'الصفقه حاليا مفعله ولا تحتاج الي كوبون', 'wp-coupon' );
                    break;
                case 'print':
                    esc_html_e( 'قم بطابعه الكوبون وإستخدمه عند الشراء من المتجر', 'wp-coupon' );
                    break;
                default:
                    esc_html_e( 'قم بنسخ الكوبون وإستخدمه عند عمليه الدفع وإنهاء الطلب ', 'wp-coupon' );
            }
            ?>
        </p>       


        <div class="modal-code">
            <?php
            switch ( wpcoupon_coupon()->get_type() ) {

                case 'sale':
                    ?>
                    <a class="ui button btn btn_secondary deal-actived" target="_blank" rel="nofollow" href="<?php echo esc_attr( wpcoupon_coupon()->get_go_out_url() ); ?>"><?php esc_html_e( 'اذهب الي التسوق', 'wp-coupon' ); ?><i class="angle right icon"></i></a>
                    <?php
                    break;
                case 'print':
                    $image_url = esc_url( wpcoupon_coupon()->get_print_image() );
                    ?>
                    <a class="btn-print-coupon" target="_blank" href="<?php echo esc_attr( $image_url ); ?>"><img alt="" src="<?php echo esc_attr( $image_url ); ?>"/></a>
                    <?php
                    break;
                default:
                    ?>
                    <div class="coupon-code">
                        <div class="ui fluid action input massive">
                            <input  type="text" class="code-text" autocomplete="off" readonly value="<?php echo esc_attr( wpcoupon_coupon()->get_code() ); ?>">
                            <button class="ui right labeled icon button btn btn_secondary">
                                <i class="copy icon"></i>
                                <span class="copy-btn"><?php esc_html_e( 'نسخ', 'wp-coupon' ); ?></span>
                            </button>
                        </div>
                    </div>

                <?php
            }
            ?>
        </div>
        <div class="clearfix">
        
        

            <?php if ( wpcoupon_coupon()->get_type() !== 'sale' ) { ?>
                <?php if ( wpcoupon_coupon()->get_type() == 'print' ) { ?>
                    <a class="ui button btn btn_secondary go-store btn-print-coupon"  href="<?php echo esc_attr( $image_url ); ?>"><?php esc_html_e( 'اطبع الكوبون', 'wp-coupon' ); ?> <i class="print icon"></i></a>
                <?php } else { ?>
                    <a href="<?php echo esc_attr( wpcoupon_coupon()->get_go_out_url() ); ?>" rel="nofollow" target="_blank" class="ui button btn btn_secondary go-store"><?php esc_html_e( 'اذهب الي التسوق', 'wp-coupon' ); ?><i class="angle right icon"></i></a>
                <?php } ?>
            <?php } ?>

        </div>
        <div class="clearfixp">
           
            <span class="show-detail"><a href="#"><?php esc_html_e( 'تفاصيل الكوبون', 'wp-coupon' ) ?><i class="angle down icon"></i></a></span>
        </div>
        <div class="coupon-popup-detail">
            <div class="coupon-detail-content"><?php
                echo str_replace( ']]>', ']]&gt;', apply_filters( 'the_content', wpcoupon_coupon()->post_content ) );  ;
                ?></div>
            
            <p><strong><?php esc_html_e( 'تم اضافه الكود منذ :', 'wp-coupon' ); ?></strong>:
                <?php printf( esc_html__( '%s ago', 'wp-coupon' ), human_time_diff( get_the_time('U'), current_time('timestamp') ) ); ?>
            </p>
        </div>
    </div>
    <div class="coupon-footer">
        <ul class="clearfix">
            <li><span><i class="wifi icon"></i> <?php printf( esc_html__( '%1$s استخدام - %2$s اليوم', 'wp-coupon' ), wpcoupon_coupon()->get_total_used(), wpcoupon_coupon()->get_used_today() ); ?></span></li>
            <li class="modal-share">
                <a class="" href="#"><i class="share alternate icon"></i> <?php esc_html_e( 'شارك الكوبون مع اصدقائك', 'wp-coupon' ); ?></a>
                <div class="share-modal-popup ui popup top right transition hidden---">
                    <?php
                    $args =  array(
                        'title'     => get_the_title( wpcoupon_coupon()->ID ),
                        'summary'   => wpcoupon_coupon()->get_excerpt(140),
                        'url'       => wpcoupon_coupon()->get_share_url()
                    );
                    echo WPCoupon_Socials::facebook_share( $args );
                    echo WPCoupon_Socials::twitter_share( $args );

                    do_action('loop_coupon_more_share_buttons');
                    ?>
                </div>
            </li>
        </ul>

    </div>
</div>