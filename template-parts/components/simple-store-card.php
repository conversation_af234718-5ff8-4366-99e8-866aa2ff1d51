<?php
/**
 * Simple Store Card Component
 * Creative modern store card design with proper links
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get store data
$store_obj = wpcoupon_store();
if (!$store_obj) {
    return;
}

// Get template variables
$style = get_query_var('store_card_style', 'featured');
$args = get_query_var('store_card_args', array());

// Store information
$store_name = $store_obj->name;
$store_url = $store_obj->get_url();
$store_page_link = get_term_link($store_obj->term_id, 'coupon_store'); // Single store page link
$store_external_link = $store_obj->get_go_store_url(); // External store link
$coupon_count = $store_obj->count;

// Get store image with fallbacks
$store_image = '';
$image_id = get_term_meta($store_obj->term_id, '_wpc_store_image_id', true);
if ($image_id && intval($image_id) > 0) {
    $store_image = wp_get_attachment_image_url($image_id, 'medium');
}

if (!$store_image) {
    $store_image_meta = get_term_meta($store_obj->term_id, '_wpc_store_image', true);
    if ($store_image_meta) {
        $store_image = $store_image_meta;
    }
}

if (!$store_image && $store_url) {
    $store_image = 'https://s.wordpress.com/mshots/v1/'.urlencode($store_url).'?w=200&h=115';
}

if (!$store_image) {
    $store_image = get_template_directory_uri() . '/assets/images/store-placeholder.png';
}

// Creative modern card classes based on style
$card_classes = 'ag-store-card group cursor-pointer';

// Add featured class if this is a featured store
if ($style === 'featured' || ($args['show_badge'] && !empty($args['badge_text']))) {
    $card_classes .= ' featured';
}
?>

<div class="<?php echo esc_attr($card_classes); ?>">
    <!-- Store Logo - Rounded Style -->
    <div class="ag-store-logo">
        <?php if ($store_image) : ?>
            <img src="<?php echo esc_url($store_image); ?>"
                 alt="<?php echo esc_attr($store_name); ?>"
                 loading="lazy">
        <?php else : ?>
            <div class="ag-logo-placeholder">
                <?php echo esc_html(substr($store_name, 0, 2)); ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Badge -->
    <?php if ($args['show_badge'] && !empty($args['badge_text'])) : ?>
        <div class="ag-store-badge">
            <?php echo esc_html($args['badge_text']); ?>
        </div>
    <?php endif; ?>

    <!-- Store Content -->
    <div class="ag-store-content">
        <!-- Store Name -->
        <h3 class="ag-store-name">
            <a href="<?php echo esc_url($store_page_link); ?>">
                <?php echo esc_html($store_name); ?>
            </a>
        </h3>

        <!-- Coupons Count -->
        <?php if ($args['show_coupons_count']) : ?>
            <div class="ag-coupons-count">
                <div class="ag-icon-wrapper">
                    <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>
                <span>
                    <?php
                    printf(
                        _n('%d كوبون متاح', '%d كوبون متاح', $coupon_count, 'wp-coupon'),
                        $coupon_count
                    );
                    ?>
                </span>
            </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <?php if ($style !== 'slider') : ?>
            <div class="ag-store-actions">
                <!-- View Store Page Button -->
                <a href="<?php echo esc_url($store_page_link); ?>" class="ag-btn-primary">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <?php esc_html_e('عرض الكوبونات', 'wp-coupon'); ?>
                </a>

                
            </div>
        <?php endif; ?>
    </div>

</div>
