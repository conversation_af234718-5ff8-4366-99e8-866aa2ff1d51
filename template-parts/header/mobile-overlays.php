<?php
/**
 * Template part for displaying mobile overlays (search and menu)
 *
 * @package Ag-Coupon
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Accessible Mobile Search Overlay -->
<div id="mobile-search-overlay"
     class="lg:hidden fixed inset-0 bg-black bg-opacity-60 z-40 hidden backdrop-blur-sm"
     role="dialog"
     aria-modal="true"
     aria-labelledby="mobile-search-title"
     aria-hidden="true">
    <div class="absolute top-0 left-0 right-0 bg-gradient-to-b from-white to-gray-50 p-6 shadow-2xl border-b-4 border-primary-400">
        <div class="flex items-center justify-between mb-6">
            <h3 id="mobile-search-title" class="text-xl font-bold text-gray-900 flex items-center">
                <svg class="w-6 h-6 text-primary-500 ml-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                </svg>
                <?php esc_html_e('البحث', 'wp-coupon'); ?>
            </h3>
            <button id="close-mobile-search"
                    class="p-3 rounded-xl hover:bg-red-50 hover:text-red-600 text-gray-500 transition-all duration-200"
                    aria-label="<?php esc_attr_e('Close search', 'wp-coupon'); ?>">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        <form action="<?php echo home_url('/'); ?>" method="get" class="relative">
            <div class="relative bg-white rounded-2xl border-2 border-gray-200 focus-within:border-primary-400 focus-within:shadow-lg transition-all duration-300">
                <input type="text" name="s"
                       class="form-input w-full pl-4 pr-16 py-4 bg-transparent border-0 rounded-2xl focus:ring-0 focus:outline-none text-lg placeholder-gray-500"
                       placeholder="<?php esc_attr_e('ابحث عن كوبون خصم أو متجر...', 'wp-coupon'); ?>">
                <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white p-3 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Accessible Mobile Menu Overlay -->
<div id="mobile-menu-overlay"
     class="lg:hidden fixed inset-0 bg-black bg-opacity-60 z-40 hidden backdrop-blur-sm"
     role="dialog"
     aria-modal="true"
     aria-labelledby="mobile-menu-title"
     aria-hidden="true">
    <div class="absolute top-0 right-0 w-80 h-full bg-gradient-to-b from-white to-gray-50 shadow-2xl transform translate-x-full transition-transform duration-300"
         id="mobile-menu-panel"
         role="navigation"
         aria-label="<?php esc_attr_e('Mobile navigation menu', 'wp-coupon'); ?>">
        <div class="p-6 h-full overflow-y-auto">
            <div class="flex items-center justify-between mb-8">
                <h3 id="mobile-menu-title" class="text-2xl font-bold text-gray-900 flex items-center">
                    <svg class="w-6 h-6 text-primary-500 ml-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <?php esc_html_e('القائمة', 'wp-coupon'); ?>
                </h3>
                <button id="close-mobile-menu"
                        class="p-3 rounded-xl hover:bg-red-50 hover:text-red-600 text-gray-500 transition-all duration-200"
                        aria-label="<?php esc_attr_e('Close menu', 'wp-coupon'); ?>">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation with Same Desktop Menu Structure -->
            <nav class="mobile-navigation">
                <?php
                // Mobile menu walker that displays same structure as desktop
                class Mobile_Mega_Menu_Walker extends Walker_Nav_Menu {
                    function start_lvl(&$output, $depth = 0, $args = null) {
                        if ($depth === 0) {
                            $output .= '<div class="mobile-mega-menu">';
                        } else {
                            $output .= '<div class="mobile-submenu ml-4 mt-2 space-y-1">';
                        }
                    }

                    function end_lvl(&$output, $depth = 0, $args = null) {
                        $output .= '</div>';
                    }

                    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
                        $classes = empty($item->classes) ? array() : (array) $item->classes;
                        $has_children = in_array('menu-item-has-children', $classes);

                        if ($depth === 0) {
                            $output .= '<div class="mobile-menu-item mb-4">';
                            $output .= '<div class="mobile-menu-header flex items-center justify-between p-3 bg-gray-100 rounded-lg">';
                            $output .= '<a href="' . esc_url($item->url) . '" class="font-semibold text-gray-900">' . esc_html($item->title) . '</a>';
                            if ($has_children) {
                                $output .= '<button class="mobile-toggle-btn p-1" data-toggle="mobile-submenu-' . $item->ID . '">';
                                $output .= '<svg class="w-4 h-4 transform transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">';
                                $output .= '<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>';
                                $output .= '</svg>';
                                $output .= '</button>';
                            }
                            $output .= '</div>';
                            if ($has_children) {
                                $output .= '<div id="mobile-submenu-' . $item->ID . '" class="mobile-submenu-content hidden mt-2">';
                            }
                        } elseif ($depth === 1) {
                            $output .= '<div class="mobile-category mb-3">';
                            $output .= '<h4 class="font-medium text-primary-600 mb-2 text-sm uppercase tracking-wide">' . esc_html($item->title) . '</h4>';
                            $output .= '<div class="mobile-category-items space-y-1">';
                        } else {
                            // Simple mobile menu item
                            $media = wpcoupon_get_menu_item_media($item);

                            $output .= '<a href="' . esc_url($item->url) . '" class="mobile-item-link flex items-center py-2 px-3 text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-200">';

                            // Simple icon
                            if ($media['type'] === 'thumbnail' && $media['url']) {
                                $output .= '<img src="' . esc_url($media['url']) . '" alt="" class="w-4 h-4 mr-2 rounded">';
                            } elseif ($media['type'] === 'icon' && $media['icon']) {
                                $output .= '<i class="' . esc_attr($media['icon']) . ' w-4 h-4 mr-2"></i>';
                            } else {
                                $output .= '<svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                                $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
                                $output .= '</svg>';
                            }

                            $output .= '<span class="text-sm">' . esc_html($item->title) . '</span>';
                            $output .= '</a>';
                        }
                    }

                    function end_el(&$output, $item, $depth = 0, $args = null) {
                        $classes = empty($item->classes) ? array() : (array) $item->classes;
                        $has_children = in_array('menu-item-has-children', $classes);

                        if ($depth === 0) {
                            if ($has_children) {
                                $output .= '</div>';
                            }
                            $output .= '</div>';
                        } elseif ($depth === 1) {
                            $output .= '</div></div>';
                        }
                    }
                }

                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'container' => false,
                    'menu_class' => 'mobile-menu-list',
                    'walker' => new Mobile_Mega_Menu_Walker(),
                    'fallback_cb' => false
                ));
                ?>
            </nav>

            <!-- Mobile Menu Footer -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-center space-x-4 space-x-reverse">
                    <a href="#" class="p-3 rounded-xl bg-gradient-to-r from-primary-500 to-secondary-500 text-white hover:from-primary-600 hover:to-secondary-600 transition-all duration-200 hover:scale-105">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                    </a>
                    <a href="#" class="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-200 hover:scale-105">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                    <a href="#" class="p-3 rounded-xl bg-gradient-to-r from-pink-500 to-pink-600 text-white hover:from-pink-600 hover:to-pink-700 transition-all duration-200 hover:scale-105">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
