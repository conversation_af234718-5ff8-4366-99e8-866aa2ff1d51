<?php
/**
 * Template part for displaying desktop navigation with mega menu
 *
 * @package Ag-Coupon
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Accessible Desktop Navigation (Hidden on Mobile) -->
<nav class="desktop-navigation hidden lg:block bg-gradient-to-r from-gray-50 via-white to-gray-50 border-t border-gray-200 relative z-40"
     role="navigation"
     aria-label="<?php esc_attr_e('Main navigation', 'wp-coupon'); ?>">
    <div class="container mx-auto px-4">
        <a href="#content" class="sr-only focus:not-sr-only"><?php esc_html_e('Skip to content', 'wp-coupon'); ?></a>
        <div class="nav-menu relative z-40" role="menubar">
            <?php
            // Simplified mega menu walker
            class Simple_Mega_Menu_Walker extends Walker_Nav_Menu {
                function start_lvl(&$output, $depth = 0, $args = null) {
                    if ($depth === 0) {
                        $output .= '<div class="mega-menu absolute top-full left-0 w-full bg-white shadow-2xl border-t-4 border-primary-400 opacity-0 invisible translate-y-2 transition-all duration-300 group-hover:opacity-100 group-hover:visible group-hover:translate-y-0 z-50" role="menu" aria-hidden="true">';
                        $output .= '<div class="container mx-auto px-4 py-4">';
                        $output .= '<div class="grid grid-cols-3 gap-4">';
                    } else {
                        $output .= '<div class="column-items space-y-1" role="group">';
                    }
                }

                function end_lvl(&$output, $depth = 0, $args = null) {
                    if ($depth === 0) {
                        $output .= '</div></div></div>';
                    } else {
                        $output .= '</div>';
                    }
                }

                function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
                    $classes = empty($item->classes) ? array() : (array) $item->classes;
                    $has_children = in_array('menu-item-has-children', $classes);

                    if ($depth === 0) {
                        $output .= '<li class="nav-item group relative" role="none">';
                        $aria_expanded = $has_children ? 'false' : '';
                        $aria_haspopup = $has_children ? 'true' : '';
                        $output .= '<a href="' . esc_url($item->url) . '" class="nav-link flex items-center px-4 py-3 text-gray-700 hover:text-primary-600 font-medium transition-all duration-200 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 rounded-lg" role="menuitem"';
                        if ($has_children) {
                            $output .= ' aria-expanded="' . $aria_expanded . '" aria-haspopup="' . $aria_haspopup . '"';
                        }
                        $output .= '>';
                        $output .= '<span>' . esc_html($item->title) . '</span>';
                        if ($has_children) {
                            $output .= '<svg class="w-4 h-4 ml-2 transform group-hover:rotate-180 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">';
                            $output .= '<path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>';
                            $output .= '</svg>';
                            $output .= '<span class="sr-only">' . esc_html__('Expand submenu', 'wp-coupon') . '</span>';
                        }
                        $output .= '</a>';
                    } elseif ($depth === 1) {
                        // Column header - make it clickable too
                        $output .= '<div class="mega-column">';
                        $output .= '<a href="' . esc_url($item->url) . '" class="column-title-link block text-xs font-bold text-primary-600 uppercase tracking-wide mb-2 px-2 py-1 rounded hover:bg-primary-100 transition-colors duration-200" role="menuitem" aria-label="' . esc_attr($item->title . ' - ' . __('Category', 'wp-coupon')) . '">' . esc_html($item->title) . '</a>';
                    } else {
                        // Direct clickable link - NO wrapper elements
                        $media = wpcoupon_get_menu_item_media($item);

                        $output .= '<a href="' . esc_url($item->url) . '" class="flex items-center py-1 px-2 text-gray-600 hover:text-primary-600 hover:bg-primary-25 rounded text-sm transition-colors duration-200" role="menuitem" aria-label="' . esc_attr($item->title) . '">';

                        // Icon with proper alt text
                        if ($media['type'] === 'thumbnail' && $media['url']) {
                            $output .= '<img src="' . esc_url($media['url']) . '" alt="' . esc_attr($item->title . ' icon') . '" class="w-3 h-3 mr-2 rounded-sm">';
                        } elseif ($media['type'] === 'icon' && $media['icon']) {
                            $output .= '<i class="' . esc_attr($media['icon']) . ' w-3 h-3 mr-2 text-gray-400" aria-hidden="true"></i>';
                        } else {
                            $output .= '<svg class="w-3 h-3 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">';
                            $output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
                            $output .= '</svg>';
                        }

                        $output .= '<span>' . esc_html($item->title) . '</span>';
                        $output .= '</a>';
                    }
                }

                function end_el(&$output, $item, $depth = 0, $args = null) {
                    if ($depth === 0) {
                        $output .= '</li>';
                    } elseif ($depth === 1) {
                        $output .= '</div>';
                    }
                    // Depth 2+ items are direct <a> tags, no closing needed
                }
            }

            wp_nav_menu(array(
                'theme_location' => 'primary',
                'container' => false,
                'menu_class' => 'flex items-center space-x-2 space-x-reverse py-2',
                'walker' => new Simple_Mega_Menu_Walker(),
                'fallback_cb' => false
            ));
            ?>
        </div>
    </div>
</nav>
