<?php
/**
 * Template part for displaying site branding with theme options logo
 *
 * @package Ag-Coupon
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get global theme options
global $st_option;

// Get logo URLs from theme options
$site_logo = '';
$retina_logo = '';
$logo_alt = get_bloginfo('name');

// Primary logo from theme options
if (isset($st_option['site_logo']['url']) && !empty($st_option['site_logo']['url'])) {
    $site_logo = $st_option['site_logo']['url'];
}

// Retina logo from theme options
if (isset($st_option['retina_site_logo']['url']) && !empty($st_option['retina_site_logo']['url'])) {
    $retina_logo = $st_option['retina_site_logo']['url'];
}

if (empty($site_logo) && has_custom_logo()) {
    $custom_logo_id = get_theme_mod('custom_logo');
    if ($custom_logo_id) {
        $site_logo = wp_get_attachment_image_url($custom_logo_id, 'full');
        $logo_alt = get_post_meta($custom_logo_id, '_wp_attachment_image_alt', true);
        if (empty($logo_alt)) {
            $logo_alt = get_bloginfo('name');
        }
    }
}

if (empty($site_logo)) {
    $site_logo = get_template_directory_uri() . '/assets/images/logo.png';
}
?>

<!-- Modern Site Branding with Theme Options Logo -->
<div class="logo-area flex-shrink-0 group">
    <a href="<?php echo esc_url(home_url('/')); ?>"
       class="site-logo-link block relative overflow-hidden rounded-2xl p-2 hover:bg-gradient-to-br hover:from-primary-50 hover:to-secondary-50 transition-all duration-500 ease-out"
       rel="home"
       aria-label="<?php echo esc_attr($logo_alt); ?>">

        <?php if ($site_logo) : ?>
            <div class="logo-container relative">
                <img src="<?php echo esc_url($site_logo); ?>"
                     <?php if ($retina_logo) : ?>
                     srcset="<?php echo esc_url($site_logo); ?> 1x, <?php echo esc_url($retina_logo); ?> 2x"
                     <?php endif; ?>
                     alt="<?php echo esc_attr($logo_alt); ?>"
                     class="site-logo h-10 lg:h-12 w-auto object-contain transition-all duration-500 ease-out group-hover:scale-105 group-hover:brightness-110"
                     loading="eager">

                <!-- Logo Glow Effect -->
                <div class="logo-glow absolute inset-0 bg-gradient-to-r from-primary-400/20 to-secondary-400/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease-out blur-xl"></div>
            </div>
        <?php else : ?>
            <!-- Text Logo Fallback -->
            <div class="text-logo-container">
                <h1 class="site-title text-2xl lg:text-3xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent group-hover:from-primary-500 group-hover:to-secondary-500 transition-all duration-500">
                    <?php bloginfo('name'); ?>
                </h1>
                <?php
                $description = get_bloginfo('description', 'display');
                if ($description || is_customize_preview()) :
                ?>
                    <p class="site-description text-xs text-gray-500 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <?php echo esc_html($description); ?>
                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </a>
</div>
