<?php
/**
 * Template part for displaying header actions
 *
 * @package Ag-Coupon
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Header Actions -->
<div class="header-actions flex items-center space-x-4 space-x-reverse">
    <!-- Quick Actions (Desktop) -->
    <div class="hidden lg:flex items-center space-x-3 space-x-reverse">
        <a href="#" class="p-2 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 text-gray-600 hover:text-primary-600 transition-all duration-200 group">
            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
            </svg>
        </a>
        <a href="#" class="p-2 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 text-gray-600 hover:text-primary-600 transition-all duration-200 group">
            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
            </svg>
        </a>
    </div>

    <!-- Mobile Search Button -->
    <button id="mobile-search-toggle" class="lg:hidden p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 text-gray-600 hover:text-primary-600 transition-all duration-200">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
        </svg>
    </button>

    <!-- Mobile Menu Toggle -->
    <button id="mobile-menu-toggle" class="lg:hidden p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 text-gray-600 hover:text-primary-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-300">
        <div class="hamburger-menu space-y-1.5">
            <span class="block w-6 h-0.5 bg-current transition-all duration-300 transform origin-center"></span>
            <span class="block w-6 h-0.5 bg-current transition-all duration-300"></span>
            <span class="block w-6 h-0.5 bg-current transition-all duration-300 transform origin-center"></span>
        </div>
    </button>
</div>
