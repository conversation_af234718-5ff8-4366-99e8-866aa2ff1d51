<?php
/**
 * Template part for displaying blog posts in loops
 *
 * @package Ag-Coupon
 */

global $post, $authordata;

if (!is_object($authordata)) {
    $authordata = get_user_by('ID', $post->post_author);
}

$is_sticky = is_sticky();
$post_categories = get_the_category();
$reading_time = wpcoupon_get_reading_time(get_the_content());
?>

<article <?php post_class('blog-card bg-white rounded-xl shadow-coupon hover:shadow-coupon-hover transition-all duration-300 overflow-hidden group mb-8 relative'); ?>>
    
    <?php if ($is_sticky) { ?>
        <div class="absolute top-4 right-4 z-10">
            <span class="bg-primary-300 text-primary-900 text-xs px-2 py-1 rounded-full font-semibold">
                <?php esc_html_e('مثبت', 'wp-coupon'); ?>
            </span>
        </div>
    <?php } ?>
    
    <?php if (has_post_thumbnail()) { ?>
        <div class="featured-image relative overflow-hidden">
            <a href="<?php the_permalink(); ?>" class="block">
                <?php the_post_thumbnail('large', array('class' => 'w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300')); ?>
            </a>
            
            <!-- Overlay gradient for better text readability -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
    <?php } ?>
    
    <div class="content p-6">
        <header class="post-header mb-4">
            <?php if (!empty($post_categories)) { ?>
                <div class="post-categories mb-3">
                    <?php foreach ($post_categories as $category) { ?>
                        <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" 
                           class="inline-block bg-secondary-100 text-secondary-700 text-xs px-2 py-1 rounded-full mr-2 mb-1 hover:bg-secondary-200 transition-colors duration-200">
                            <?php echo esc_html($category->name); ?>
                        </a>
                    <?php } ?>
                </div>
            <?php } ?>
            
            <h2 class="post-title text-xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-primary-600 transition-colors duration-200">
                <a href="<?php the_permalink(); ?>" class="hover:underline">
                    <?php the_title(); ?>
                </a>
            </h2>
        </header>
        
        <div class="post-excerpt text-gray-600 mb-4 line-clamp-3 leading-relaxed">
            <?php 
            $excerpt = get_the_excerpt();
            if (strlen($excerpt) > 150) {
                $excerpt = substr($excerpt, 0, 150) . '...';
            }
            echo $excerpt;
            ?>
        </div>
        
        <footer class="post-footer flex items-center justify-between text-sm text-gray-500">
            <div class="post-meta flex items-center space-x-4 space-x-reverse">
                <span class="author flex items-center">
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                    <?php the_author(); ?>
                </span>
                <span class="date flex items-center">
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo get_the_date(); ?>
                </span>
                <?php if ($reading_time) { ?>
                    <span class="reading-time flex items-center">
                        <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo $reading_time; ?> <?php esc_html_e('دقيقة', 'wp-coupon'); ?>
                    </span>
                <?php } ?>
            </div>
            
            <a href="<?php the_permalink(); ?>" 
               class="read-more text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 flex items-center">
                <?php esc_html_e('اقرأ المزيد', 'wp-coupon'); ?>
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </footer>
    </div>
</article>
