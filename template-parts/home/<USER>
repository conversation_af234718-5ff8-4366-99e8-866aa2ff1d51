<?php
/**
 * Featured Coupons Section for Home Page
 * Uses enhanced coupon card system with deduplication
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get featured coupons data with deduplication
$featured_coupons = get_query_var('featured_coupons', array());
if (empty($featured_coupons)) {
    $featured_coupons = get_featured_coupons(
        8, 
        true, // exclude already displayed
        'home-featured-coupons' // section identifier
    );
}

// Only show section if we have coupons
if (empty($featured_coupons)) {
    return;
}
?>

<!-- Featured Coupons Section -->
<section class="ag-featured-coupons-section py-16 bg-gradient-to-br from-yellow-50 to-blue-50">
    <div class="container mx-auto px-4">
        
        <!-- Section Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full mb-6">
                <svg class="w-8 h-8 text-yellow-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            
            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                <?php esc_html_e('الكوبونات المميزة', 'wp-coupon'); ?>
            </h2>
            
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('اكتشف أفضل العروض والخصومات الحصرية من المتاجر المفضلة لديك', 'wp-coupon'); ?>
            </p>
            
            <!-- Decorative Elements -->
            <div class="flex justify-center items-center space-x-4 space-x-reverse mt-6">
                <div class="w-12 h-0.5 bg-gradient-to-r from-transparent to-yellow-400"></div>
                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                <div class="w-6 h-0.5 bg-yellow-400"></div>
                <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                <div class="w-12 h-0.5 bg-gradient-to-l from-transparent to-blue-400"></div>
            </div>
        </div>

        <!-- Featured Coupons Grid -->
        <?php
// Simple featured coupons grid
        if (!empty($featured_coupons)) : ?>
            <div class="featured-coupons-grid-container mb-12">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <?php foreach ($featured_coupons as $coupon) : ?>
                        <div class="coupon-grid-item">
                            <?php render_coupon_card($coupon, 'featured', array(
                                'show_store_logo' => true,
                                'show_badges' => true,
                                'show_description' => true,
                                'description_length' => 20,
                                'show_image' => true
                            )); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php else : ?>
            <p class="text-center text-gray-500 py-8">لا توجد كوبونات مميزة متاحة حالياً</p>
        <?php endif;
        ?>

        <!-- Call to Action -->
        <div class="text-center mt-12">
            <a href="<?php echo esc_url(get_post_type_archive_link('coupon')); ?>" 
               class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
                <?php esc_html_e('عرض جميع الكوبونات', 'wp-coupon'); ?>
            </a>
        </div>
        
        <!-- Statistics -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16 pt-12 border-t border-gray-200">
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                    <?php echo esc_html(wp_count_posts('coupon')->publish); ?>+
                </div>
                <div class="text-sm text-gray-600">
                    <?php esc_html_e('كوبون متاح', 'wp-coupon'); ?>
                </div>
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">
                    <?php 
                    $stores_count = wp_count_terms(array('taxonomy' => 'coupon_store', 'hide_empty' => true));
                    echo esc_html($stores_count);
                    ?>+
                </div>
                <div class="text-sm text-gray-600">
                    <?php esc_html_e('متجر شريك', 'wp-coupon'); ?>
                </div>
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-yellow-600 mb-2">95%</div>
                <div class="text-sm text-gray-600">
                    <?php esc_html_e('معدل النجاح', 'wp-coupon'); ?>
                </div>
            </div>
            
            <div class="text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">24/7</div>
                <div class="text-sm text-gray-600">
                    <?php esc_html_e('تحديث مستمر', 'wp-coupon'); ?>
                </div>
            </div>
        </div>
    </div>
</section>
