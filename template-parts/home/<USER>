<?php
/**
 * Home Page Newsletter Section
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if newsletter is enabled in theme options
$newsletter_enabled = wpcoupon_get_option('enable_newsletter', true);
$newsletter_title = wpcoupon_get_option('newsletter_title', 'لا تفوت أي عرض!');
$newsletter_description = wpcoupon_get_option('newsletter_description', 'اشترك في نشرتنا الإخبارية واحصل على أحدث الكوبونات والعروض الحصرية مباشرة في بريدك الإلكتروني');

// Skip if newsletter is disabled
if (!$newsletter_enabled) {
    return;
}
?>

<!-- Newsletter Section -->
<section class="newsletter-section">
    <div class="section-container">
        <div class="newsletter-content">
            <div class="newsletter-text">
                <h2 class="newsletter-title"><?php echo esc_html($newsletter_title); ?></h2>
                <p class="newsletter-description"><?php echo esc_html($newsletter_description); ?></p>
                <div class="newsletter-features">
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span><?php esc_html_e('عروض حصرية', 'wp-coupon'); ?></span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span><?php esc_html_e('تحديثات أسبوعية', 'wp-coupon'); ?></span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span><?php esc_html_e('إلغاء الاشتراك في أي وقت', 'wp-coupon'); ?></span>
                    </div>
                </div>
            </div>
            <div class="newsletter-form">
                <form class="subscription-form" action="<?php echo esc_url(admin_url('admin-ajax.php')); ?>" method="post" id="newsletter-form">
                    <?php wp_nonce_field('newsletter_subscription', 'newsletter_nonce'); ?>
                    <input type="hidden" name="action" value="newsletter_subscription">
                    
                    <div class="form-group">
                        <div class="input-wrapper">
                            <input type="email" 
                                   name="email" 
                                   id="newsletter-email"
                                   placeholder="<?php esc_attr_e('أدخل بريدك الإلكتروني', 'wp-coupon'); ?>" 
                                   required
                                   autocomplete="email">
                            <button type="submit" class="subscribe-btn" id="newsletter-submit">
                                <span class="btn-text"><?php esc_html_e('اشترك الآن', 'wp-coupon'); ?></span>
                                <span class="btn-loading" style="display: none;"><?php esc_html_e('جاري الإرسال...', 'wp-coupon'); ?></span>
                                <svg class="btn-icon" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="form-messages">
                            <div class="success-message" style="display: none;">
                                <svg class="message-icon" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span><?php esc_html_e('تم الاشتراك بنجاح! شكراً لك.', 'wp-coupon'); ?></span>
                            </div>
                            <div class="error-message" style="display: none;">
                                <svg class="message-icon" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="error-text"><?php esc_html_e('حدث خطأ. يرجى المحاولة مرة أخرى.', 'wp-coupon'); ?></span>
                            </div>
                        </div>
                    </div>
                    <p class="form-note"><?php esc_html_e('نحن نحترم خصوصيتك ولن نشارك بياناتك مع أطراف ثالثة', 'wp-coupon'); ?></p>
                </form>
            </div>
        </div>
    </div>
</section>

<script>
// Newsletter form handling
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('newsletter-form');
    const submitBtn = document.getElementById('newsletter-submit');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const successMessage = document.querySelector('.success-message');
    const errorMessage = document.querySelector('.error-message');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
            
            // Hide previous messages
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';
            
            // Submit form via AJAX
            const formData = new FormData(form);
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    successMessage.style.display = 'flex';
                    form.reset();
                } else {
                    errorMessage.style.display = 'flex';
                    const errorText = errorMessage.querySelector('.error-text');
                    errorText.textContent = data.data || '<?php esc_html_e('حدث خطأ. يرجى المحاولة مرة أخرى.', 'wp-coupon'); ?>';
                }
            })
            .catch(error => {
                errorMessage.style.display = 'flex';
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
            });
        });
    }
});
</script>
