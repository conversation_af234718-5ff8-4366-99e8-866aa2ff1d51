<?php
/**
 * Home Page Featured Stores Section - Enhanced Version
 * Fixed to display multiple stores with rating system and modern design
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get featured stores directly using WordPress functions to ensure multiple stores
$featured_stores = get_terms(array(
    'taxonomy' => 'coupon_store',
    'number' => 12,
    'hide_empty' => false,
    'meta_query' => array(
        array(
            'key' => '_wpc_is_featured',
            'value' => 'on',
            'compare' => '='
        )
    ),
    'orderby' => 'count',
    'order' => 'DESC'
));

// Fallback: if no featured stores, get popular stores
if (empty($featured_stores)) {
    $featured_stores = get_terms(array(
        'taxonomy' => 'coupon_store',
        'number' => 12,
        'hide_empty' => true,
        'orderby' => 'count',
        'order' => 'DESC'
    ));
}
?>

<!-- Featured Stores Slider Section -->
<section class="featured-stores-section py-16 bg-gradient-to-br from-primary-50 via-white to-secondary-50 overflow-hidden">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="section-header text-center mb-12">
            <h2 class="section-title text-3xl lg:text-4xl font-bold text-gray-900 mb-4 relative">
                <?php esc_html_e('المتاجر المميزة', 'wp-coupon'); ?>
                <span class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full"></span>
            </h2>
            <p class="section-subtitle text-lg text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('اكتشف أفضل المتاجر والعلامات التجارية المميزة مع أحدث العروض والخصومات الحصرية', 'wp-coupon'); ?>
            </p>
        </div>

        <?php if (!empty($featured_stores)) : ?>
            <!-- Enhanced Featured Stores Grid -->
            <div class="featured-stores-grid-container">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                    <?php foreach ($featured_stores as $store) :
                        // Setup store context
                        wpcoupon_setup_store($store);
                        $store_obj = wpcoupon_store();

                        // Store information
                        $store_name = $store_obj->name;
                        $store_page_link = get_term_link($store->term_id, 'coupon_store');
                        $store_external_link = $store_obj->get_go_store_url();
                        $coupon_count = $store_obj->count;
                        $is_featured = get_term_meta($store->term_id, '_wpc_is_featured', true) === 'on';

                        // Get store image with fallbacks
                        $store_image = '';
                        $image_id = get_term_meta($store->term_id, '_wpc_store_image_id', true);
                        if ($image_id && intval($image_id) > 0) {
                            $store_image = wp_get_attachment_image_url($image_id, 'medium');
                        }

                        if (!$store_image) {
                            $store_image_meta = get_term_meta($store->term_id, '_wpc_store_image', true);
                            if ($store_image_meta) {
                                $store_image = $store_image_meta;
                            }
                        }

                        if (!$store_image) {
                            $store_image = get_template_directory_uri() . '/assets/images/store-placeholder.png';
                        }
                    ?>
                        <!-- Enhanced Store Card -->
                        <div class="enhanced-store-card group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 overflow-hidden border border-gray-100 hover:border-primary-200">

                            <!-- Animated Background -->
                            <div class="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-transparent to-secondary-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <!-- Featured Badge -->
                            <?php if ($is_featured) : ?>
                                <div class="absolute top-4 right-4 z-10">
                                    <div class="bg-gradient-to-r from-primary-400 to-secondary-400 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                        <svg class="w-3 h-3 inline-block mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        <?php esc_html_e('مميز', 'wp-coupon'); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Store Logo Section -->
                            <div class="relative p-6 pb-4">
                                <div class="store-logo-container relative mx-auto w-20 h-20 mb-4">
                                    <div class="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-2xl transform rotate-6 group-hover:rotate-12 transition-transform duration-500"></div>
                                    <div class="relative w-full h-full bg-white rounded-2xl shadow-md overflow-hidden border-2 border-gray-100 group-hover:border-primary-200 transition-colors duration-300">
                                        <?php if ($store_image) : ?>
                                            <img src="<?php echo esc_url($store_image); ?>"
                                                 alt="<?php echo esc_attr($store_name); ?>"
                                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                                 loading="lazy">
                                        <?php else : ?>
                                            <div class="w-full h-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white font-bold text-lg">
                                                <?php echo esc_html(substr($store_name, 0, 2)); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Store Content -->
                            <div class="relative px-6 pb-6">
                                <!-- Store Name -->
                                <h3 class="text-lg font-bold text-gray-900 mb-2 text-center group-hover:text-primary-600 transition-colors duration-300">
                                    <a href="<?php echo esc_url($store_page_link); ?>" class="hover:text-primary-600 transition-colors duration-200">
                                        <?php echo esc_html($store_name); ?>
                                    </a>
                                </h3>

                                <!-- Store Rating -->
                                <div class="flex justify-center mb-3">
                                    <?php echo wpcoupon_rating($store->term_id, 'loop', 'store', array('size' => 'small')); ?>
                                </div>

                                <!-- Coupon Count -->
                                <div class="flex items-center justify-center gap-2 text-gray-600 mb-4">
                                    <div class="w-8 h-8 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 5a3 3 0 015-2.236A3 3 0 0114.83 6H16a2 2 0 110 4h-5V9a1 1 0 10-2 0v1H4a2 2 0 110-4h1.17C5.06 5.687 5 5.35 5 5zm4 1V5a1 1 0 10-1 1h1zm3 0a1 1 0 10-1-1v1h1z" clip-rule="evenodd"/>
                                            <path d="M9 11H3v5a2 2 0 002 2h4v-7zM11 18h4a2 2 0 002-2v-5h-6v7z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium">
                                        <?php printf(_n('%s كوبون', '%s كوبون', $coupon_count, 'wp-coupon'), number_format($coupon_count)); ?>
                                    </span>
                                </div>

                                <!-- Action Button -->
                                <div class="text-center">
                                    <a href="<?php echo esc_url($store_page_link); ?>"
                                       class="inline-flex items-center gap-2 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 transform group-hover:scale-105 shadow-md hover:shadow-lg">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                        </svg>
                                        <?php esc_html_e('عرض الكوبونات', 'wp-coupon'); ?>
                                    </a>
                                </div>
                            </div>

                            <!-- Hover Effect Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- View All Stores Button -->
            <div class="section-footer text-center mt-12">
                <a href="<?php echo esc_url(home_url('/stores/')); ?>"
                   class="view-all-btn inline-flex items-center gap-3 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-semibold px-8 py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl group">
                    <?php esc_html_e('عرض جميع المتاجر', 'wp-coupon'); ?>
                    <svg class="btn-arrow w-5 h-5 transition-transform duration-300 group-hover:translate-x-1 rtl:group-hover:-translate-x-1"
                         fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        <?php else : ?>
            <!-- No Stores Message -->
            <div class="no-stores-message text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        <?php esc_html_e('لا توجد متاجر مميزة حالياً', 'wp-coupon'); ?>
                    </h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('سيتم عرض المتاجر المميزة هنا عندما تصبح متاحة', 'wp-coupon'); ?>
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
