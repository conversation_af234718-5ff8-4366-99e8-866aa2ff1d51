<?php
/**
 * Home Page Categories Section
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get categories data
$categories = isset($categories) ? $categories : get_terms(array('taxonomy' => 'coupon_category', 'hide_empty' => true, 'number' => 8));
?>

<!-- Categories Section -->
<section class="categories-section">
    <div class="section-container">
        <div class="section-header">
            <h2 class="section-title"><?php esc_html_e('تسوق حسب الفئة', 'wp-coupon'); ?></h2>
            <p class="section-subtitle"><?php esc_html_e('اكتشف العروض في فئاتك المفضلة', 'wp-coupon'); ?></p>
        </div>

        <?php if (!empty($categories) && !is_wp_error($categories)) : ?>
        <div class="categories-grid">
            <?php foreach ($categories as $category) :
                $category_link = get_term_link($category);
                $category_image = get_term_meta($category->term_id, '_wpc_category_image', true);
                $coupon_count = $category->count;
                
                // Skip if category link is error
                if (is_wp_error($category_link)) {
                    continue;
                }
            ?>
            <a href="<?php echo esc_url($category_link); ?>" class="category-card">
                <div class="category-image">
                    <?php if ($category_image) : ?>
                        <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($category->name); ?>" loading="lazy">
                    <?php else : ?>
                        <div class="category-icon">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                                <path d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="category-info">
                    <h3 class="category-name"><?php echo esc_html($category->name); ?></h3>
                    <p class="category-count"><?php printf(esc_html__('%d كوبون', 'wp-coupon'), $coupon_count); ?></p>
                </div>
                <div class="category-overlay">
                    <span class="overlay-text"><?php esc_html_e('تصفح الفئة', 'wp-coupon'); ?></span>
                </div>
            </a>
            <?php endforeach; ?>
        </div>
        <?php else : ?>
        <div class="no-categories-message">
            <p><?php esc_html_e('لا توجد فئات متاحة حالياً', 'wp-coupon'); ?></p>
        </div>
        <?php endif; ?>
    </div>
</section>
