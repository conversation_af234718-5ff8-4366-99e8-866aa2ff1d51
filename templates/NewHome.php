<?php
/**
 * Template Name: Ag-Coupon Modern Home
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

get_header();

// Simple home page - no complex tracking needed

// Get data for the home page sections - simple approach using WordPress functions
$featured_stores = get_terms(array(
    'taxonomy' => 'coupon_store',
    'number' => 12,
    'meta_query' => array(
        array(
            'key' => '_wpc_is_featured',
            'value' => 'on',
            'compare' => '='
        )
    )
));

$featured_coupons = get_posts(array(
    'post_type' => 'coupon',
    'posts_per_page' => 8,
    'meta_query' => array(
        array(
            'key' => '_wpc_exclusive',
            'value' => 'on',
            'compare' => '='
        )
    )
));

$latest_coupons = get_posts(array(
    'post_type' => 'coupon',
    'posts_per_page' => 12,
    'orderby' => 'date',
    'order' => 'DESC'
));

$categories = get_terms(array('taxonomy' => 'coupon_category', 'hide_empty' => true, 'number' => 8));

// Note: Both stores and coupons will be automatically filtered to prevent duplicates
// using the comprehensive tracking systems

// Make data available to template parts
set_query_var('featured_stores', $featured_stores);
set_query_var('featured_coupons', $featured_coupons);
set_query_var('latest_coupons', $latest_coupons);
set_query_var('categories', $categories);

?>

<!-- Modern Home Page Container -->
<div id="home-page" class="ag-coupon-home">

    <?php
    /**
     * Hero Section
     * Displays main hero banner with statistics and floating cards
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Featured Stores Section
     * Displays featured/promoted stores with special badges
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Featured Coupons Section
     * Displays exclusive and premium coupons with enhanced cards
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Latest Coupons Section
     * Displays the most recent coupons with interactive buttons
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Categories Section
     * Displays coupon categories for easy browsing
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Popular Stores Section
     * Displays most popular stores based on user activity
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Newsletter Section
     * Displays newsletter subscription form with AJAX functionality
     */
    get_template_part('template-parts/home/<USER>');
    ?>

</div>

<?php
// Simple debug information for the home page
if (WP_DEBUG && current_user_can('manage_options')) {
    echo '<!-- Home Page Debug: Featured Stores: ' . count($featured_stores) . ' -->';
}
?>

<?php get_footer(); ?>