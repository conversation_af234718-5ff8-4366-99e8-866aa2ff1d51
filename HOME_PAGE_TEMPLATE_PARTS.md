# Home Page Template Parts Structure

## Overview

The home page has been reorganized into smaller, manageable template parts for easier development and maintenance. Each section is now a separate file that can be edited independently.

## File Structure

```
template-parts/home/
├── hero-section.php          # Main hero banner with statistics
├── featured-stores.php       # Featured/promoted stores section
├── latest-coupons.php        # Recent coupons with interactive buttons
├── categories-section.php    # Coupon categories grid
├── popular-stores.php        # Most popular stores section
└── newsletter-section.php    # Newsletter subscription form
```

## Template Parts Details

### 1. Hero Section (`template-parts/home/<USER>

**Purpose:** Main hero banner with site statistics and floating cards

**Features:**
- Dynamic statistics (coupon count, store count)
- Floating animated cards
- Responsive design
- Customizable hero text

**Data Used:**
- `wp_count_posts('coupon')` - Total coupons
- `wp_count_terms('coupon_store')` - Total stores

**Customization:**
- Edit hero title and description directly in the file
- Modify floating cards content and icons
- Adjust statistics display

---

### 2. Featured Stores (`template-parts/home/<USER>

**Purpose:** Display featured/promoted stores with special badges

**Features:**
- Featured store badges
- Store thumbnails with fallback placeholders
- Coupon count per store
- Responsive grid layout

**Data Used:**
- `$featured_stores` - From `wpcoupon_get_featured_stores(12)`
- Store thumbnails, names, URLs, coupon counts

**Customization:**
- Change number of stores displayed (modify main template)
- Customize store card design
- Add/remove store information fields

---

### 3. Latest Coupons (`template-parts/home/<USER>

**Purpose:** Display recent coupons with interactive functionality

**Features:**
- Coupon type badges (code/deal)
- Store logos with fallbacks
- Interactive coupon buttons with AJAX
- Expiry date display
- Responsive coupon cards

**Data Used:**
- `$latest_coupons` - From `wpcoupon_get_coupons()`
- Coupon details, store information, expiry dates

**Customization:**
- Modify coupon card layout
- Change button text and styling
- Add/remove coupon metadata

---

### 4. Categories Section (`template-parts/home/<USER>

**Purpose:** Display coupon categories for easy browsing

**Features:**
- Category images with fallback icons
- Coupon count per category
- Hover effects and overlays
- Error handling for invalid categories

**Data Used:**
- `$categories` - From `get_terms('coupon_category')`
- Category images, names, coupon counts

**Customization:**
- Change category display layout
- Modify fallback icons
- Add category descriptions

---

### 5. Popular Stores (`template-parts/home/<USER>

**Purpose:** Display most popular stores based on activity

**Features:**
- Popularity badges for high-rated stores
- Store rating system (if available)
- Overlay effects on hover
- "View All Stores" button
- Enhanced store cards

**Data Used:**
- `$popular_stores` - From `wpcoupon_get_stores()`
- Store ratings, thumbnails, coupon counts

**Customization:**
- Add/remove store rating display
- Modify popularity criteria
- Change overlay effects

---

### 6. Newsletter Section (`template-parts/home/<USER>

**Purpose:** Newsletter subscription with AJAX functionality

**Features:**
- AJAX form submission
- Loading states and feedback messages
- Feature list with icons
- Theme option integration
- Email validation

**Data Used:**
- Theme options for newsletter settings
- AJAX endpoint for subscription

**Customization:**
- Modify newsletter features list
- Change form styling
- Add/remove form fields

## Usage in Main Template

### Current Implementation (`templates/NewHome.php`)

```php
<?php
// Get data for all sections
$featured_stores = wpcoupon_get_featured_stores(12);
$popular_stores = wpcoupon_get_stores(array('number' => 16, 'orderby' => 'count', 'order' => 'DESC'));
$latest_coupons = wpcoupon_get_coupons(array('posts_per_page' => 8, 'hide_expired' => true));
$categories = get_terms(array('taxonomy' => 'coupon_category', 'hide_empty' => true, 'number' => 8));

// Make data available to template parts
set_query_var('featured_stores', $featured_stores);
set_query_var('popular_stores', $popular_stores);
set_query_var('latest_coupons', $latest_coupons);
set_query_var('categories', $categories);
?>

<div id="home-page" class="ag-coupon-home">
    <?php get_template_part('template-parts/home/<USER>'); ?>
    <?php get_template_part('template-parts/home/<USER>'); ?>
    <?php get_template_part('template-parts/home/<USER>'); ?>
    <?php get_template_part('template-parts/home/<USER>'); ?>
    <?php get_template_part('template-parts/home/<USER>'); ?>
    <?php get_template_part('template-parts/home/<USER>'); ?>
</div>
```

## Benefits of This Structure

### 1. **Easier Maintenance**
- Each section can be edited independently
- Smaller files are easier to navigate
- Reduced risk of breaking other sections

### 2. **Better Organization**
- Clear separation of concerns
- Logical file naming
- Consistent structure across sections

### 3. **Improved Reusability**
- Template parts can be reused in other templates
- Easy to create variations of sections
- Modular approach for future development

### 4. **Enhanced Collaboration**
- Multiple developers can work on different sections
- Easier code reviews
- Better version control

### 5. **Flexible Customization**
- Easy to add/remove sections
- Simple to reorder sections
- Quick to modify individual components

## Customization Examples

### Adding a New Section

1. Create new template part file:
```php
// template-parts/home/<USER>
<section class="testimonials-section">
    <!-- Testimonials content -->
</section>
```

2. Add to main template:
```php
<?php get_template_part('template-parts/home/<USER>'); ?>
```

### Modifying Section Order

Simply reorder the `get_template_part()` calls in `templates/NewHome.php`:

```php
<?php get_template_part('template-parts/home/<USER>'); ?>
<?php get_template_part('template-parts/home/<USER>'); ?>  <!-- Moved up -->
<?php get_template_part('template-parts/home/<USER>'); ?>
<!-- etc... -->
```

### Conditional Section Display

```php
<?php if (wpcoupon_get_option('show_newsletter', true)) : ?>
    <?php get_template_part('template-parts/home/<USER>'); ?>
<?php endif; ?>
```

### Passing Custom Data

```php
// In main template
set_query_var('custom_data', $my_custom_data);

// In template part
$custom_data = get_query_var('custom_data');
```

## Development Workflow

### Working on Individual Sections

1. **Identify the section** you want to modify
2. **Open the corresponding template part** file
3. **Make your changes** without affecting other sections
4. **Test the specific section** functionality
5. **Commit changes** with clear section-specific messages

### Adding New Features

1. **Determine which section** the feature belongs to
2. **Edit the appropriate template part**
3. **Add any required data** to the main template
4. **Test integration** with existing functionality

### Debugging Issues

1. **Isolate the problematic section**
2. **Check the specific template part** file
3. **Verify data availability** using `get_query_var()`
4. **Test section independently**

## Best Practices

### 1. **Data Handling**
- Always check if data exists before using it
- Use fallbacks for missing data
- Escape output properly

### 2. **Performance**
- Avoid heavy queries in template parts
- Use caching when appropriate
- Optimize images and assets

### 3. **Accessibility**
- Use semantic HTML
- Include proper ARIA labels
- Ensure keyboard navigation

### 4. **Responsive Design**
- Test on multiple screen sizes
- Use mobile-first approach
- Optimize for touch interfaces

### 5. **Code Quality**
- Follow WordPress coding standards
- Add comments for complex logic
- Use consistent naming conventions

## Future Enhancements

### Potential Additions
- **Testimonials section**
- **Featured categories**
- **Recent blog posts**
- **Social media feeds**
- **Partner stores**

### Advanced Features
- **Section reordering via admin**
- **Dynamic section enabling/disabling**
- **A/B testing for different layouts**
- **Performance analytics per section**

This modular structure provides a solid foundation for ongoing development and makes the home page much more maintainable and flexible.
