<?php
get_header();
$cate_title = single_cat_title( '', false );
$cate_id =  get_queried_object_id();

$layout = wpcoupon_get_option( 'coupon_cate_layout', 'right-sidebar' );
?>

<div id="content-wrap" class="container <?php echo esc_attr( $layout ); ?>">

	<div id="primary" class="content-area">
		<main id="main" class="site-main ajax-coupons" role="main">
			<section id="store-listings-wrapper" class="wpb_content_element">

				<Header>
					<?php the_archive_title( '<h1 class="section-heading">', '</h1>' ); ?>
				</Header>
				<div class="tag-items store-listings st-list-coupons">
					<?php
                global $wp_rewrite, $post;
                $tpl = wpcoupon_get_option( 'coupon_cate_tpl', 'cat' );

                if ( have_posts() ) {
                   while( have_posts() ) {
                       the_post();
                       wpcoupon_setup_coupon( $post );
                       get_template_part('template-parts/loop/loop-coupon', $tpl);
                   }
                }

                ?>

				</div>


			</section>
			<?php
            get_template_part( 'template-parts/content/content', 'paging' );
            ?>
			<!-- Display Term Description By AG -->
			<?php echo term_description(); ?>
		</main><!-- #main -->
	</div><!-- #primary -->

	<div id="secondary" class="widget-area sidebar" role="complementary">
		<?php
        dynamic_sidebar( 'sidebar-coupon-category' );
        ?>
	</div>

	<?php
    $ads = wpcoupon_get_option( 'coupon_cate_ads', '' );
    if ( $ads ) {
        echo '<div class="clear"></div>';
        echo balanceTags( $ads );
    }
    ?>

</div> <!-- /#content-wrap -->

<?php
get_footer();
?>