<?php
    /**
     * The template for displaying the footer.
     *
     * Contains the closing of the #content div and all content after
     *
     * @package WP Coupon
     */
    global $st_option;

        if( wpcoupon_get_option( 'before_footer', '' ) != '' ) {
            if( wpcoupon_get_option( 'before_footer_apply', 'home' ) != 'all' ) {
                if ( get_option( 'show_on_front' ) == 'page' && is_home() ) {
                    echo do_shortcode( wpcoupon_get_option( 'before_footer', '' ) );
                }
            } else {
                echo do_shortcode( wpcoupon_get_option( 'before_footer', '' ) );
            }

        }
        ?>
		</div> <!-- END .site-content -->

        <!-- Tailwind CSS Creative Footer -->
        <footer id="colophon" class="site-footer bg-gradient-to-br from-gray-900 via-gray-800 to-secondary-900 text-white relative overflow-hidden" role="contentinfo">

			<!-- Background Pattern -->
			<div class="absolute inset-0 opacity-5">
				<div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
			</div>

			<div class="container mx-auto px-4 relative z-10">

                <?php if ( wpcoupon_get_option( 'footer_widgets' ) ) {

                    $footer_columns = wpcoupon_get_option( 'footer_columns', 4 );
                    $layouts = 16;
                    if ( $footer_columns > 1 ){
                        $layouts = wpcoupon_get_option( 'footer_columns_layout_'.$footer_columns );
                    }
                    $layouts = explode( '+', $layouts );
                    foreach ( $layouts as $k => $v ) {
                        $v = absint( trim( $v ) );
                        $v =  $v >= 16 ? 16 : $v;
                        $layouts[ $k ] = $v;
                    }

                    ?>
                    <!-- Footer Widgets -->
                    <div class="footer-widgets-area py-16">
                        <div class="grid grid-cols-1 md:grid-cols-<?php echo $footer_columns; ?> gap-8">
                            <?php
                            for ($count = 0; $count < $footer_columns; $count++) {
                                ?>
                                <div id="footer-<?php echo esc_attr( $count +1 ) ?>" class="footer-column widget-area" role="complementary">
                                    <div class="footer-widget-content">
                                        <?php dynamic_sidebar('footer-' . ( $count +1 ) ); ?>
                                    </div>
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                    </div>

                <?php } ?>

                <!-- Footer Bottom -->
                <div class="footer-bottom border-t border-gray-700 py-8">
                    <div class="flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0">

                        <!-- Copyright -->
                        <div class="footer-copyright text-center lg:text-right">
                            <p class="text-gray-300 text-sm">
                                <?php
                                if ( wpcoupon_get_option('footer_copyright') == '' ) {
                                    printf(
                                        esc_html__( 'Copyright &copy; %1$s %2$s. All Rights Reserved.', 'wp-coupon' ),
                                        '<span class="text-primary-300 font-semibold">' . esc_attr(date('Y')) . '</span>',
                                        '<span class="text-white font-semibold">' . get_bloginfo('name') . '</span>'
                                    );
                                } else {
                                    echo wp_kses_post( wpcoupon_get_option('footer_copyright') );
                                }
                                ?>
                            </p>

                            <?php if ( wpcoupon_get_option('enable_footer_author') ) { ?>
                                <p class="text-gray-400 text-xs mt-2">
                                    <?php
                                    printf(
                                        esc_html__( 'Made with %s by %s', 'wp-coupon' ),
                                        '<span class="text-red-400">❤</span>',
                                        '<a rel="nofollow" target="_blank" href="https://abdullah-g.com/" class="text-primary-300 hover:text-primary-200 transition-colors duration-200 font-medium">A.Gamal</a>'
                                    );
                                    ?>
                                </p>
                            <?php } ?>
                        </div>

                        <!-- Footer Navigation -->
                        <nav id="footer-nav" class="footer-navigation">
                            <?php
                            wp_nav_menu( array(
                                'container' => false,
                                'theme_location' => 'footer',
                                'menu_class' => 'flex items-center space-x-6 space-x-reverse text-sm',
                                'link_before' => '<span class="text-gray-300 hover:text-primary-300 transition-colors duration-200">',
                                'link_after' => '</span>',
                                'fallback_cb' => false
                            ) );
                            ?>
                        </nav>

                        <!-- Back to Top Button -->
                        <button id="back-to-top" class="bg-primary-500 hover:bg-primary-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-300">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
		</footer><!-- END #colophon-->

	</div><!-- END #page -->

    <?php wp_footer(); ?>
</body>
</html>
